Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.50s/it]
[2025-06-26 14:36:38,053][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
100%|██████████| 675/675 [00:00<00:00, 200279.80it/s]
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
Loading model...
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.12s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
Map: 100%|██████████| 6254/6254 [00:00<00:00, 7337.90 examples/s]
Map: 100%|██████████| 208/208 [00:00<00:00, 7018.01 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
Using auto half precision backend
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-06-26 14:38:02,971] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-06-26 14:38:02,971] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-06-26 14:38:07,556] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-06-26 14:38:07,559] [INFO] [logging.py:128:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-06-26 14:38:07,560] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-06-26 14:38:07,580] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-06-26 14:38:07,580] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-06-26 14:38:07,580] [INFO] [logging.py:128:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:149:__init__] Reduce bucket size 500000000
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:150:__init__] Allgather bucket size 200000000
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:151:__init__] CPU Offload: False
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:152:__init__] Round robin gradient partitioning: False
[2025-06-26 14:38:11,281] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-06-26 14:38:11,282] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB
[2025-06-26 14:38:11,283] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 37.11 GB, percent = 3.7%
[2025-06-26 14:38:11,446] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-06-26 14:38:11,447] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB
[2025-06-26 14:38:11,447] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 37.11 GB, percent = 3.7%
[2025-06-26 14:38:11,448] [INFO] [stage_1_and_2.py:544:__init__] optimizer state initialized
[2025-06-26 14:38:11,626] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-06-26 14:38:11,627] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB
[2025-06-26 14:38:11,627] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 37.2 GB, percent = 3.7%
[2025-06-26 14:38:11,629] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-06-26 14:38:11,629] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-06-26 14:38:11,629] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-06-26 14:38:11,630] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-06-26 14:38:11,634] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-06-26 14:38:11,634] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false,
    "contiguous_memory_optimization": false,
    "cpu_checkpointing": false,
    "number_checkpoints": null,
    "synchronize_checkpoint_boundary": false,
    "profile": false
}
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false,
    "start_step": null,
    "end_step": null,
    "metric_path": null,
    "arg_mappings": null,
    "metric": "throughput",
    "model_info": null,
    "results_dir": "autotuning_results",
    "exps_dir": "autotuning_exps",
    "overwrite": true,
    "fast": true,
    "start_profile_step": 3,
    "end_profile_step": 5,
    "tuner_type": "gridsearch",
    "tuner_early_stopping": 5,
    "tuner_num_trials": 50,
    "model_info_path": null,
    "mp_size": 1,
    "max_train_batch_size": null,
    "min_train_batch_size": 1,
    "max_train_micro_batch_size_per_gpu": 1.024000e+03,
    "min_train_micro_batch_size_per_gpu": 1,
    "num_tuning_micro_batch_sizes": 3
}
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f6371356c20>
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false,
    "recompute_fwd_factor": 0.0,
    "profile_step": 1,
    "module_depth": -1,
    "top_modules": 1,
    "detailed": true,
    "output_file": null
}
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false,
    "persistent_storage_path": null,
    "persistent_time_interval": 100,
    "num_of_version_in_retention": 2,
    "enable_nebula_load": true,
    "load_path": null
}
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   train_batch_size ............. 16
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  True
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=200000000 overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=False use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-06-26 14:38:11,648] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 2
[2025-06-26 14:38:11,648] [INFO] [config.py:989:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2,
        "allgather_partitions": true,
        "allgather_bucket_size": 2.000000e+08,
        "overlap_comm": true,
        "reduce_scatter": true,
        "contiguous_gradients": true
    },
    "gradient_accumulation_steps": 4,
    "gradient_clipping": 1.0,
    "steps_per_print": inf,
    "train_batch_size": 16,
    "train_micro_batch_size_per_gpu": 1,
    "wall_clock_breakdown": false,
    "bf16": {
        "enabled": true
    },
    "fp16": {
        "enabled": false
    },
    "zero_allow_untested_optimizer": true
}
***** Running training *****
  Num examples = 6,254
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 3,910
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"
                                                    
{'loss': 1.92, 'grad_norm': 51.04514694213867, 'learning_rate': 9.997442455242967e-05, 'epoch': 0.0}
{'loss': 1.3574, 'grad_norm': 2.478416919708252, 'learning_rate': 9.936061381074169e-05, 'epoch': 0.06}
{'loss': 1.2474, 'grad_norm': 2.743911027908325, 'learning_rate': 9.872122762148338e-05, 'epoch': 0.13}
{'loss': 1.2145, 'grad_norm': 1.9198548793792725, 'learning_rate': 9.808184143222507e-05, 'epoch': 0.19}
{'loss': 1.1658, 'grad_norm': 1.785643219947815, 'learning_rate': 9.744245524296676e-05, 'epoch': 0.26}
{'loss': 1.0941, 'grad_norm': 1.9877444505691528, 'learning_rate': 9.680306905370844e-05, 'epoch': 0.32}
{'loss': 1.0961, 'grad_norm': 1.597650170326233, 'learning_rate': 9.616368286445013e-05, 'epoch': 0.38}
{'loss': 1.0337, 'grad_norm': 1.6794203519821167, 'learning_rate': 9.552429667519182e-05, 'epoch': 0.45}
{'loss': 1.0184, 'grad_norm': 1.3539094924926758, 'learning_rate': 9.488491048593351e-05, 'epoch': 0.51}
{'loss': 1.031, 'grad_norm': 1.4158295392990112, 'learning_rate': 9.42455242966752e-05, 'epoch': 0.58}
{'loss': 1.0093, 'grad_norm': 1.3328862190246582, 'learning_rate': 9.360613810741689e-05, 'epoch': 0.64}
{'loss': 0.949, 'grad_norm': 1.5916404724121094, 'learning_rate': 9.296675191815857e-05, 'epoch': 0.7}
{'loss': 0.8962, 'grad_norm': 1.5085554122924805, 'learning_rate': 9.232736572890026e-05, 'epoch': 0.77}
{'loss': 0.8796, 'grad_norm': 1.5415908098220825, 'learning_rate': 9.168797953964195e-05, 'epoch': 0.83}
{'loss': 0.8412, 'grad_norm': 1.5445359945297241, 'learning_rate': 9.104859335038364e-05, 'epoch': 0.9}
{'loss': 0.8726, 'grad_norm': 1.444787621498108, 'learning_rate': 9.040920716112533e-05, 'epoch': 0.96}
{'loss': 0.8042, 'grad_norm': 1.3148874044418335, 'learning_rate': 8.976982097186701e-05, 'epoch': 1.02}
{'loss': 0.7569, 'grad_norm': 1.1983317136764526, 'learning_rate': 8.91304347826087e-05, 'epoch': 1.09}
{'loss': 0.694, 'grad_norm': 1.3833144903182983, 'learning_rate': 8.849104859335039e-05, 'epoch': 1.15}
{'loss': 0.7175, 'grad_norm': 1.2883570194244385, 'learning_rate': 8.785166240409208e-05, 'epoch': 1.21}
{'loss': 0.6819, 'grad_norm': 1.2545000314712524, 'learning_rate': 8.721227621483377e-05, 'epoch': 1.28}
{'loss': 0.6492, 'grad_norm': 1.2674205303192139, 'learning_rate': 8.657289002557545e-05, 'epoch': 1.34}
{'loss': 0.6671, 'grad_norm': 1.1528080701828003, 'learning_rate': 8.593350383631714e-05, 'epoch': 1.41}
{'loss': 0.6577, 'grad_norm': 1.1249068975448608, 'learning_rate': 8.529411764705883e-05, 'epoch': 1.47}
{'loss': 0.6536, 'grad_norm': 1.0751123428344727, 'learning_rate': 8.465473145780052e-05, 'epoch': 1.53}
{'loss': 0.6296, 'grad_norm': 1.0893763303756714, 'learning_rate': 8.40153452685422e-05, 'epoch': 1.6}
{'loss': 0.5884, 'grad_norm': 1.1417633295059204, 'learning_rate': 8.33759590792839e-05, 'epoch': 1.66}
{'loss': 0.5947, 'grad_norm': 1.2017974853515625, 'learning_rate': 8.273657289002558e-05, 'epoch': 1.73}
{'loss': 0.6155, 'grad_norm': 1.0679086446762085, 'learning_rate': 8.209718670076727e-05, 'epoch': 1.79}
{'loss': 0.5831, 'grad_norm': 1.4074851274490356, 'learning_rate': 8.145780051150896e-05, 'epoch': 1.85}
{'loss': 0.577, 'grad_norm': 1.1386092901229858, 'learning_rate': 8.081841432225065e-05, 'epoch': 1.92}
{'loss': 0.545, 'grad_norm': 1.1608484983444214, 'learning_rate': 8.017902813299233e-05, 'epoch': 1.98}
{'loss': 0.5071, 'grad_norm': 0.9658547043800354, 'learning_rate': 7.953964194373402e-05, 'epoch': 2.05}
{'loss': 0.4577, 'grad_norm': 1.2401636838912964, 'learning_rate': 7.890025575447571e-05, 'epoch': 2.11}
{'loss': 0.4575, 'grad_norm': 1.1377052068710327, 'learning_rate': 7.82608695652174e-05, 'epoch': 2.17}
{'loss': 0.4776, 'grad_norm': 1.1052194833755493, 'learning_rate': 7.762148337595909e-05, 'epoch': 2.24}
{'loss': 0.4801, 'grad_norm': 0.9782165884971619, 'learning_rate': 7.698209718670077e-05, 'epoch': 2.3}
{'loss': 0.4403, 'grad_norm': 1.0849721431732178, 'learning_rate': 7.634271099744246e-05, 'epoch': 2.37}
{'loss': 0.4507, 'grad_norm': 1.2417902946472168, 'learning_rate': 7.570332480818415e-05, 'epoch': 2.43}
{'loss': 0.4032, 'grad_norm': 0.9264167547225952, 'learning_rate': 7.506393861892584e-05, 'epoch': 2.49}
{'loss': 0.4258, 'grad_norm': 0.9667097330093384, 'learning_rate': 7.442455242966753e-05, 'epoch': 2.56}
{'loss': 0.4242, 'grad_norm': 1.2026914358139038, 'learning_rate': 7.378516624040921e-05, 'epoch': 2.62}
{'loss': 0.4185, 'grad_norm': 1.2307852506637573, 'learning_rate': 7.31457800511509e-05, 'epoch': 2.69}
{'loss': 0.3799, 'grad_norm': 0.8642074465751648, 'learning_rate': 7.250639386189259e-05, 'epoch': 2.75}
{'loss': 0.4224, 'grad_norm': 0.8795709609985352, 'learning_rate': 7.186700767263428e-05, 'epoch': 2.81}
{'loss': 0.3656, 'grad_norm': 1.0927730798721313, 'learning_rate': 7.122762148337597e-05, 'epoch': 2.88}
{'loss': 0.3829, 'grad_norm': 0.8372628688812256, 'learning_rate': 7.058823529411765e-05, 'epoch': 2.94}
{'loss': 0.3526, 'grad_norm': 1.096291422843933, 'learning_rate': 6.994884910485934e-05, 'epoch': 3.01}
{'loss': 0.3122, 'grad_norm': 0.8691463470458984, 'learning_rate': 6.930946291560103e-05, 'epoch': 3.07}
{'loss': 0.3207, 'grad_norm': 0.9615539908409119, 'learning_rate': 6.867007672634272e-05, 'epoch': 3.13}
{'loss': 0.3089, 'grad_norm': 0.838830292224884, 'learning_rate': 6.80306905370844e-05, 'epoch': 3.2}
{'loss': 0.3025, 'grad_norm': 1.083067536354065, 'learning_rate': 6.73913043478261e-05, 'epoch': 3.26}
{'loss': 0.2971, 'grad_norm': 1.0977531671524048, 'learning_rate': 6.675191815856778e-05, 'epoch': 3.32}
{'loss': 0.3029, 'grad_norm': 0.8532986044883728, 'learning_rate': 6.611253196930947e-05, 'epoch': 3.39}
{'loss': 0.2896, 'grad_norm': 0.8712836503982544, 'learning_rate': 6.547314578005116e-05, 'epoch': 3.45}
{'loss': 0.2819, 'grad_norm': 0.8685945868492126, 'learning_rate': 6.483375959079285e-05, 'epoch': 3.52}
{'loss': 0.2656, 'grad_norm': 0.6175561547279358, 'learning_rate': 6.419437340153452e-05, 'epoch': 3.58}
{'loss': 0.2745, 'grad_norm': 0.97038334608078, 'learning_rate': 6.355498721227622e-05, 'epoch': 3.64}
{'loss': 0.2619, 'grad_norm': 0.9724594950675964, 'learning_rate': 6.29156010230179e-05, 'epoch': 3.71}
{'loss': 0.2638, 'grad_norm': 1.0277965068817139, 'learning_rate': 6.22762148337596e-05, 'epoch': 3.77}
{'loss': 0.2599, 'grad_norm': 0.9379246830940247, 'learning_rate': 6.163682864450127e-05, 'epoch': 3.84}
{'loss': 0.2793, 'grad_norm': 1.0384514331817627, 'learning_rate': 6.099744245524297e-05, 'epoch': 3.9}
{'loss': 0.2711, 'grad_norm': 0.8246506452560425, 'learning_rate': 6.035805626598465e-05, 'epoch': 3.96}
{'loss': 0.236, 'grad_norm': 0.793634295463562, 'learning_rate': 5.9718670076726344e-05, 'epoch': 4.03}
{'loss': 0.2188, 'grad_norm': 0.8000171184539795, 'learning_rate': 5.9079283887468026e-05, 'epoch': 4.09}
{'loss': 0.2087, 'grad_norm': 0.8009399175643921, 'learning_rate': 5.843989769820972e-05, 'epoch': 4.16}
{'loss': 0.1988, 'grad_norm': 0.9281237721443176, 'learning_rate': 5.78005115089514e-05, 'epoch': 4.22}
{'loss': 0.177, 'grad_norm': 0.5372213125228882, 'learning_rate': 5.7161125319693096e-05, 'epoch': 4.28}
{'loss': 0.2083, 'grad_norm': 0.8120546340942383, 'learning_rate': 5.652173913043478e-05, 'epoch': 4.35}
{'loss': 0.1878, 'grad_norm': 0.7198323607444763, 'learning_rate': 5.588235294117647e-05, 'epoch': 4.41}
{'loss': 0.1862, 'grad_norm': 0.7830379009246826, 'learning_rate': 5.5242966751918154e-05, 'epoch': 4.48}
{'loss': 0.1839, 'grad_norm': 0.6439346671104431, 'learning_rate': 5.460358056265985e-05, 'epoch': 4.54}
{'loss': 0.2019, 'grad_norm': 0.5754552483558655, 'learning_rate': 5.396419437340153e-05, 'epoch': 4.6}
{'loss': 0.1853, 'grad_norm': 0.836511492729187, 'learning_rate': 5.3324808184143225e-05, 'epoch': 4.67}
{'loss': 0.179, 'grad_norm': 0.6660141944885254, 'learning_rate': 5.268542199488491e-05, 'epoch': 4.73}
{'loss': 0.198, 'grad_norm': 0.828519344329834, 'learning_rate': 5.20460358056266e-05, 'epoch': 4.8}
{'loss': 0.1695, 'grad_norm': 0.7320075035095215, 'learning_rate': 5.140664961636829e-05, 'epoch': 4.86}
{'loss': 0.1805, 'grad_norm': 0.656623125076294, 'learning_rate': 5.076726342710998e-05, 'epoch': 4.92}
{'loss': 0.1756, 'grad_norm': 0.7695077657699585, 'learning_rate': 5.0127877237851665e-05, 'epoch': 4.99}
{'loss': 0.15, 'grad_norm': 0.5612486600875854, 'learning_rate': 4.948849104859335e-05, 'epoch': 5.05}
{'loss': 0.1437, 'grad_norm': 0.6725096702575684, 'learning_rate': 4.884910485933504e-05, 'epoch': 5.12}
{'loss': 0.1437, 'grad_norm': 0.5595406293869019, 'learning_rate': 4.820971867007673e-05, 'epoch': 5.18}
{'loss': 0.1561, 'grad_norm': 0.5209560394287109, 'learning_rate': 4.757033248081842e-05, 'epoch': 5.24}
{'loss': 0.1561, 'grad_norm': 0.7302173376083374, 'learning_rate': 4.6930946291560105e-05, 'epoch': 5.31}
{'loss': 0.1286, 'grad_norm': 0.6633211374282837, 'learning_rate': 4.629156010230179e-05, 'epoch': 5.37}
{'loss': 0.1408, 'grad_norm': 0.39233705401420593, 'learning_rate': 4.565217391304348e-05, 'epoch': 5.43}
{'loss': 0.1345, 'grad_norm': 0.6151101589202881, 'learning_rate': 4.501278772378517e-05, 'epoch': 5.5}
{'loss': 0.1379, 'grad_norm': 1.0051714181900024, 'learning_rate': 4.437340153452686e-05, 'epoch': 5.56}
{'loss': 0.126, 'grad_norm': 0.7098011374473572, 'learning_rate': 4.3734015345268545e-05, 'epoch': 5.63}
{'loss': 0.1392, 'grad_norm': 0.6773862242698669, 'learning_rate': 4.309462915601023e-05, 'epoch': 5.69}
{'loss': 0.1259, 'grad_norm': 0.49850451946258545, 'learning_rate': 4.245524296675192e-05, 'epoch': 5.75}
{'loss': 0.1245, 'grad_norm': 0.5925995111465454, 'learning_rate': 4.181585677749361e-05, 'epoch': 5.82}
{'loss': 0.1249, 'grad_norm': 0.5363137722015381, 'learning_rate': 4.11764705882353e-05, 'epoch': 5.88}
{'loss': 0.1191, 'grad_norm': 0.5227335691452026, 'learning_rate': 4.0537084398976985e-05, 'epoch': 5.95}
{'loss': 0.1157, 'grad_norm': 0.5731391310691833, 'learning_rate': 3.989769820971867e-05, 'epoch': 6.01}
{'loss': 0.1065, 'grad_norm': 0.624992847442627, 'learning_rate': 3.925831202046036e-05, 'epoch': 6.07}
{'loss': 0.0997, 'grad_norm': 0.5940295457839966, 'learning_rate': 3.861892583120205e-05, 'epoch': 6.14}
{'loss': 0.1086, 'grad_norm': 0.3888510763645172, 'learning_rate': 3.797953964194374e-05, 'epoch': 6.2}
{'loss': 0.1011, 'grad_norm': 0.490695595741272, 'learning_rate': 3.7340153452685426e-05, 'epoch': 6.27}
{'loss': 0.1064, 'grad_norm': 0.5110863447189331, 'learning_rate': 3.6700767263427114e-05, 'epoch': 6.33}
{'loss': 0.1066, 'grad_norm': 0.575789213180542, 'learning_rate': 3.60613810741688e-05, 'epoch': 6.39}
{'loss': 0.0955, 'grad_norm': 0.5254873633384705, 'learning_rate': 3.542199488491049e-05, 'epoch': 6.46}
{'loss': 0.0962, 'grad_norm': 0.5788753628730774, 'learning_rate': 3.478260869565218e-05, 'epoch': 6.52}
{'loss': 0.1047, 'grad_norm': 0.37460291385650635, 'learning_rate': 3.4143222506393866e-05, 'epoch': 6.59}
{'loss': 0.0927, 'grad_norm': 0.7633658051490784, 'learning_rate': 3.3503836317135554e-05, 'epoch': 6.65}
{'loss': 0.095, 'grad_norm': 0.5634973049163818, 'learning_rate': 3.286445012787724e-05, 'epoch': 6.71}
{'loss': 0.0879, 'grad_norm': 0.526990532875061, 'learning_rate': 3.222506393861893e-05, 'epoch': 6.78}
{'loss': 0.1011, 'grad_norm': 0.6740297079086304, 'learning_rate': 3.158567774936062e-05, 'epoch': 6.84}
{'loss': 0.0914, 'grad_norm': 0.3880011737346649, 'learning_rate': 3.0946291560102306e-05, 'epoch': 6.91}
{'loss': 0.0876, 'grad_norm': 0.3968127369880676, 'learning_rate': 3.030690537084399e-05, 'epoch': 6.97}
{'loss': 0.0921, 'grad_norm': 0.5650238394737244, 'learning_rate': 2.966751918158568e-05, 'epoch': 7.03}
{'loss': 0.081, 'grad_norm': 0.5522066950798035, 'learning_rate': 2.9028132992327367e-05, 'epoch': 7.1}
{'loss': 0.0912, 'grad_norm': 0.5009344816207886, 'learning_rate': 2.8388746803069055e-05, 'epoch': 7.16}
{'loss': 0.0834, 'grad_norm': 0.4166412353515625, 'learning_rate': 2.7749360613810743e-05, 'epoch': 7.23}
{'loss': 0.0782, 'grad_norm': 0.2804437279701233, 'learning_rate': 2.710997442455243e-05, 'epoch': 7.29}
{'loss': 0.0818, 'grad_norm': 0.31494662165641785, 'learning_rate': 2.647058823529412e-05, 'epoch': 7.35}
{'loss': 0.0781, 'grad_norm': 0.3036784827709198, 'learning_rate': 2.5831202046035807e-05, 'epoch': 7.42}
{'loss': 0.0849, 'grad_norm': 0.2896527945995331, 'learning_rate': 2.5191815856777495e-05, 'epoch': 7.48}
{'loss': 0.0821, 'grad_norm': 0.4325106739997864, 'learning_rate': 2.4552429667519183e-05, 'epoch': 7.54}
{'loss': 0.0789, 'grad_norm': 0.2922871708869934, 'learning_rate': 2.391304347826087e-05, 'epoch': 7.61}
{'loss': 0.0751, 'grad_norm': 0.6019728183746338, 'learning_rate': 2.327365728900256e-05, 'epoch': 7.67}
{'loss': 0.0799, 'grad_norm': 0.40585023164749146, 'learning_rate': 2.2634271099744247e-05, 'epoch': 7.74}
{'loss': 0.081, 'grad_norm': 0.45310041308403015, 'learning_rate': 2.1994884910485935e-05, 'epoch': 7.8}
{'loss': 0.0729, 'grad_norm': 0.26771169900894165, 'learning_rate': 2.1355498721227623e-05, 'epoch': 7.86}
{'loss': 0.0752, 'grad_norm': 0.2737603485584259, 'learning_rate': 2.071611253196931e-05, 'epoch': 7.93}
{'loss': 0.0709, 'grad_norm': 0.47309884428977966, 'learning_rate': 2.0076726342711e-05, 'epoch': 7.99}
{'loss': 0.0717, 'grad_norm': 0.23987722396850586, 'learning_rate': 1.9437340153452684e-05, 'epoch': 8.06}
{'loss': 0.0689, 'grad_norm': 0.2890155017375946, 'learning_rate': 1.8797953964194372e-05, 'epoch': 8.12}
{'loss': 0.0654, 'grad_norm': 0.3300662934780121, 'learning_rate': 1.815856777493606e-05, 'epoch': 8.18}
{'loss': 0.0684, 'grad_norm': 0.2691166400909424, 'learning_rate': 1.7519181585677748e-05, 'epoch': 8.25}
{'loss': 0.0658, 'grad_norm': 0.5035260915756226, 'learning_rate': 1.6879795396419436e-05, 'epoch': 8.31}
{'loss': 0.0603, 'grad_norm': 0.4748343825340271, 'learning_rate': 1.6240409207161124e-05, 'epoch': 8.38}
{'loss': 0.0641, 'grad_norm': 0.207920640707016, 'learning_rate': 1.5601023017902812e-05, 'epoch': 8.44}
{'loss': 0.0637, 'grad_norm': 0.19097232818603516, 'learning_rate': 1.4961636828644502e-05, 'epoch': 8.5}
{'loss': 0.0654, 'grad_norm': 0.2891494929790497, 'learning_rate': 1.432225063938619e-05, 'epoch': 8.57}
{'loss': 0.0708, 'grad_norm': 0.4414593279361725, 'learning_rate': 1.3682864450127878e-05, 'epoch': 8.63}
{'loss': 0.0688, 'grad_norm': 0.4285537600517273, 'learning_rate': 1.3043478260869566e-05, 'epoch': 8.7}
{'loss': 0.0661, 'grad_norm': 0.31454744935035706, 'learning_rate': 1.2404092071611254e-05, 'epoch': 8.76}
{'loss': 0.0694, 'grad_norm': 0.21961313486099243, 'learning_rate': 1.1764705882352942e-05, 'epoch': 8.82}
{'loss': 0.0627, 'grad_norm': 0.4640918970108032, 'learning_rate': 1.112531969309463e-05, 'epoch': 8.89}
{'loss': 0.0661, 'grad_norm': 0.25889191031455994, 'learning_rate': 1.0485933503836318e-05, 'epoch': 8.95}
{'loss': 0.0626, 'grad_norm': 0.3358956575393677, 'learning_rate': 9.846547314578006e-06, 'epoch': 9.02}
{'loss': 0.0574, 'grad_norm': 0.1670030951499939, 'learning_rate': 9.207161125319694e-06, 'epoch': 9.08}
{'loss': 0.0569, 'grad_norm': 0.17014889419078827, 'learning_rate': 8.567774936061382e-06, 'epoch': 9.14}
{'loss': 0.0598, 'grad_norm': 0.09686073660850525, 'learning_rate': 7.92838874680307e-06, 'epoch': 9.21}
{'loss': 0.06, 'grad_norm': 0.30939456820487976, 'learning_rate': 7.289002557544758e-06, 'epoch': 9.27}
{'loss': 0.0606, 'grad_norm': 0.1654721051454544, 'learning_rate': 6.649616368286446e-06, 'epoch': 9.34}
{'loss': 0.0572, 'grad_norm': 0.32274121046066284, 'learning_rate': 6.010230179028133e-06, 'epoch': 9.4}
{'loss': 0.0585, 'grad_norm': 0.13703332841396332, 'learning_rate': 5.370843989769821e-06, 'epoch': 9.46}
{'loss': 0.0569, 'grad_norm': 0.5089929699897766, 'learning_rate': 4.731457800511509e-06, 'epoch': 9.53}
{'loss': 0.059, 'grad_norm': 0.16062669456005096, 'learning_rate': 4.092071611253197e-06, 'epoch': 9.59}
{'loss': 0.06, 'grad_norm': 0.1981465071439743, 'learning_rate': 3.4526854219948846e-06, 'epoch': 9.65}
{'loss': 0.0582, 'grad_norm': 0.18919645249843597, 'learning_rate': 2.813299232736573e-06, 'epoch': 9.72}
{'loss': 0.0618, 'grad_norm': 0.1623886674642563, 'learning_rate': 2.173913043478261e-06, 'epoch': 9.78}
{'loss': 0.0634, 'grad_norm': 0.23087430000305176, 'learning_rate': 1.534526854219949e-06, 'epoch': 9.85}
{'loss': 0.059, 'grad_norm': 0.13514994084835052, 'learning_rate': 8.951406649616369e-07, 'epoch': 9.91}
{'loss': 0.0571, 'grad_norm': 0.16097837686538696, 'learning_rate': 2.5575447570332484e-07, 'epoch': 9.97}
Configuration saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/config.json
Configuration saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/generation_config.json
The model is bigger than the maximum size per checkpoint (5GB) and is going to be split in 3 checkpoint shards. You can find where each parameters has been saved in the index located at data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/model.safetensors.index.json.
tokenizer config file saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/tokenizer_config.json
Special tokens file saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/special_tokens_map.json
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
[2025-06-26 16:13:10,413] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step3910 is about to be saved!
[2025-06-26 16:13:13,973] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt
[2025-06-26 16:13:13,973] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt...
[2025-06-26 16:13:59,542] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt.
[2025-06-26 16:13:59,554] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt...
[2025-06-26 16:14:10,617] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt.
[2025-06-26 16:14:10,621] [INFO] [engine.py:3536:_save_zero_checkpoint] zero checkpoint saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt
[2025-06-26 16:14:10,622] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step3910 is ready now!


Training completed. Do not forget to share your model on huggingface.co/models =)


100%|██████████| 3910/3910 [1:36:02<00:00,  1.47s/it]
{'train_runtime': 5762.4929, 'train_samples_per_second': 10.853, 'train_steps_per_second': 0.679, 'train_loss': 0.30228340509907364, 'epoch': 10.0}
Training completed!
Running final evaluation...

***** Running Evaluation *****
  Num examples = 208
  Batch size = 1
100%|██████████| 52/52 [00:06<00:00,  8.23it/s]
Evaluation metrics at step 3910: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4586, 'eval_samples_per_second': 32.205, 'eval_steps_per_second': 8.051}
Final evaluation results: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4586, 'eval_samples_per_second': 32.205, 'eval_steps_per_second': 8.051, 'epoch': 10.0}
