{"train/learning_rate": 2.5575447570332484e-07, "train_samples_per_second": 10.853, "dataset_train_size": 6254, "_wandb": {"runtime": 5875}, "eval/loss": 0.06868267059326172, "final_eval_steps_per_second": 8.051, "train_steps_per_second": 0.679, "_runtime": 5875.732818011, "_timestamp": 1750968860.6276355, "final_eval_samples_per_second": 32.205, "train/loss": 0.0571, "model_parameters": 7356420096, "eval_samples_per_second": 32.205, "train/grad_norm": 0.16097837686538696, "n_weights": 771, "dataset_test_size": 487, "eval/steps_per_second": 8.051, "trainable_percentage": 26.075709311965863, "eval_runtime": 6.4586, "final_epoch": 10, "train/epoch": 10, "train/global_step": 3910, "eval_steps_per_second": 8.051, "total_flos": 8.288256040475034e+17, "_step": 3910, "final_eval_runtime": 6.4586, "train_runtime": 5762.4929, "eval_loss": 0.06868267059326172, "final_eval_loss": 0.06868267059326172, "dataset_val_size": 208, "eval/runtime": 6.4586, "eval/samples_per_second": 32.205, "trainable_parameters": 1918238720, "train_loss": 0.30228340509907364, "n_router_weights": 256}