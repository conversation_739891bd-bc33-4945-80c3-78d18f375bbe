Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.76s/it]
[2025-06-26 14:22:45,070][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
100%|██████████| 675/675 [00:00<00:00, 203314.56it/s]
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
Loading model...
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
Loading checkpoint shards: 100%|██████████| 2/2 [00:18<00:00,  9.20s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
Map: 100%|██████████| 6254/6254 [00:00<00:00, 7996.04 examples/s]
Map: 100%|██████████| 208/208 [00:00<00:00, 6959.89 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
Using auto half precision backend
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-06-26 14:24:23,801] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed info: version=0.17.1, git-hash=unknown, git-branch=unknown
[2025-06-26 14:24:23,802] [INFO] [config.py:655:__init__] Config mesh_device None world_size = 4
[2025-06-26 14:24:25,443] [INFO] [engine.py:1325:_configure_distributed_model] ********** distributed groups summary **********
	 self.dp_world_size=4
	 self.mp_world_size=1
	 self.seq_dp_world_size=4
	 self.sequence_parallel_size=1
***********************************************
[2025-06-26 14:24:28,519] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-06-26 14:24:28,523] [INFO] [logging.py:107:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-06-26 14:24:28,523] [INFO] [logging.py:107:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-06-26 14:24:28,543] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-06-26 14:24:28,543] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-06-26 14:24:28,544] [INFO] [logging.py:107:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:151:__init__] Reduce bucket size 500000000
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:152:__init__] Allgather bucket size 200000000
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:153:__init__] CPU Offload: False
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:154:__init__] Round robin gradient partitioning: False
[2025-06-26 14:24:32,211] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-06-26 14:24:32,213] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB
[2025-06-26 14:24:32,213] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 38.21 GB, percent = 3.8%
[2025-06-26 14:24:32,412] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-06-26 14:24:32,413] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB
[2025-06-26 14:24:32,414] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 38.13 GB, percent = 3.8%
[2025-06-26 14:24:32,414] [INFO] [stage_1_and_2.py:573:__init__] optimizer state initialized
[2025-06-26 14:24:32,594] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-06-26 14:24:32,595] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB
[2025-06-26 14:24:32,595] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 38.13 GB, percent = 3.8%
[2025-06-26 14:24:32,597] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-06-26 14:24:32,598] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-06-26 14:24:32,598] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-06-26 14:24:32,598] [INFO] [logging.py:107:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-06-26 14:24:32,602] [INFO] [logging.py:107:log_dist] [Rank 0] [TorchCheckpointEngine] Initialized with serialization = True
[2025-06-26 14:24:32,602] [INFO] [config.py:921:print] DeepSpeedEngine configuration:
[2025-06-26 14:24:32,602] [INFO] [config.py:925:print]   activation_checkpointing_config  {
    "partition_activations": false,
    "contiguous_memory_optimization": false,
    "cpu_checkpointing": false,
    "number_checkpoints": null,
    "synchronize_checkpoint_boundary": false,
    "profile": false
}
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'intra_op_parallelism': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   amp_enabled .................. False
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   amp_params ................... False
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   autotuning_config ............ {
    "enabled": false,
    "start_step": null,
    "end_step": null,
    "metric_path": null,
    "arg_mappings": null,
    "metric": "throughput",
    "model_info": null,
    "results_dir": "autotuning_results",
    "exps_dir": "autotuning_exps",
    "overwrite": true,
    "fast": true,
    "start_profile_step": 3,
    "end_profile_step": 5,
    "tuner_type": "gridsearch",
    "tuner_early_stopping": 5,
    "tuner_num_trials": 50,
    "model_info_path": null,
    "mp_size": 1,
    "max_train_batch_size": null,
    "min_train_batch_size": 1,
    "max_train_micro_batch_size_per_gpu": 1.024000e+03,
    "min_train_micro_batch_size_per_gpu": 1,
    "num_tuning_micro_batch_sizes": 3
}
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   bfloat16_config .............. enabled=True immediate_grad_update=False check_grad_overflow=False
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_config ............ {'tag_validation': 'WARN', 'checkpoint_serialization': True, 'writer': None}
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_parallel_write_pipeline  False
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_tag_validation_enabled  True
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_tag_validation_fail  False
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f8ec5f722c0>
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   communication_data_type ...... None
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   compile_config ............... deepcompile=False free_activation=False offload_activation=False offload_opt_states=False double_buffer=True symmetric_memory=False debug_log=False offload_parameters=False sync_before_reduce=False sync_after_reduce=False sync_before_allgather=False sync_after_allgather=False
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   curriculum_enabled_legacy .... False
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   curriculum_params_legacy ..... False
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'pin_memory': False, 'curriculum_learning': {'enabled': False}, 'dynamic_batching': {'enabled': False, 'lr_scaling_method': 'linear', 'min_batch_size': 1, 'max_batch_size': None, 'sequence_picking_order': 'dataloader', 'verbose': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   data_efficiency_enabled ...... False
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   dataloader_drop_last ......... False
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   disable_allgather ............ False
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   dump_state ................... False
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_enabled ........... False
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_gas_boundary_resolution  1
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_layer_num ......... 0
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_max_iter .......... 100
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_stability ......... 1e-06
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   eigenvalue_tol ............... 0.01
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   eigenvalue_verbose ........... False
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   elasticity_enabled ........... False
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   float16_config ............... enabled=False auto_cast=False loss_scale=0.0 initial_scale_power=16 loss_scale_window=1000 hysteresis=2 consecutive_hysteresis=False min_loss_scale=1 fp16_master_weights_and_grads=False
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   flops_profiler_config ........ {
    "enabled": false,
    "recompute_fwd_factor": 0.0,
    "profile_step": 1,
    "module_depth": -1,
    "top_modules": 1,
    "detailed": true,
    "output_file": null
}
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   global_rank .................. 0
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   grad_accum_dtype ............. None
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   gradient_accumulation_steps .. 4
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   gradient_clipping ............ 1.0
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   gradient_predivide_factor .... 1.0
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   graph_harvesting ............. False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   load_universal_checkpoint .... False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   memory_breakdown ............. False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   mics_hierarchial_params_gather  False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   mics_shard_size .............. -1
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   nebula_config ................ {
    "enabled": false,
    "persistent_storage_path": null,
    "persistent_time_interval": 100,
    "num_of_version_in_retention": 2,
    "enable_nebula_load": true,
    "load_path": null
}
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   optimizer_legacy_fusion ...... False
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   optimizer_name ............... None
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   optimizer_params ............. None
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   pld_enabled .................. False
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   pld_params ................... False
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   prescale_gradients ........... False
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   scheduler_name ............... None
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   scheduler_params ............. None
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   seq_parallel_communication_data_type  torch.float32
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   sparse_attention ............. None
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   sparse_gradients_enabled ..... False
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   steps_per_print .............. inf
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   tensor_parallel_config ....... dtype=torch.float16 autotp_size=0 tp_overlap_comm=False tensor_parallel=TPConfig(tp_size=1, tp_grain_size=1, mpu=None, tp_group=None) injection_policy_tuple=None keep_module_on_host=False replace_with_kernel_inject=False
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   timers_config ................ enabled=True synchronized=True
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   train_batch_size ............. 16
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   train_micro_batch_size_per_gpu  1
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   use_data_before_expert_parallel_  False
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   use_node_local_storage ....... False
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   wall_clock_breakdown ......... False
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   weight_quantization_config ... None
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   world_size ................... 4
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_allow_untested_optimizer  True
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=200000000 overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=False module_granularity_threshold=0 use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False zeropp_loco_param=None mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True log_trace_cache_warnings=False
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_enabled ................. True
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_force_ds_cpu_optimizer .. True
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_optimization_stage ...... 2
[2025-06-26 14:24:32,616] [INFO] [config.py:911:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2,
        "allgather_partitions": true,
        "allgather_bucket_size": 2.000000e+08,
        "overlap_comm": true,
        "reduce_scatter": true,
        "contiguous_gradients": true
    },
    "gradient_accumulation_steps": 4,
    "gradient_clipping": 1.0,
    "steps_per_print": inf,
    "train_batch_size": 16,
    "train_micro_batch_size_per_gpu": 1,
    "wall_clock_breakdown": false,
    "bf16": {
        "enabled": true
    },
    "fp16": {
        "enabled": false
    },
    "zero_allow_untested_optimizer": true
}
***** Running training *****
  Num examples = 6,254
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 3,910
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"
  0%|          | 0/3910 [00:00<?, ?it/s]Error executing job with overrides: []
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 202, in main
    trainer.train()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 2123, in train
    return inner_training_loop(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 2480, in _inner_training_loop
    with context():
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/accelerator.py", line 1047, in no_sync
    with context():
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2225, in no_sync
    assert not self.zero_optimization_partition_gradients(), \
AssertionError: no_sync context manager is incompatible with gradient partitioning logic of ZeRO stage 2

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
