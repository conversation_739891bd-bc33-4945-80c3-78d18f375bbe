Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.54s/it]
Error executing job with overrides: []
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 63, in main
    expertmerger.compose()
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/mergoo/composers/composer_lora_moe.py", line 113, in compose
    assert self.config["router_layers"] == list(
AssertionError

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
