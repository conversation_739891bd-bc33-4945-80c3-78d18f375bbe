{"os": "Linux-5.15.0-101-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.11", "startedAt": "2025-06-26T20:36:52.529554Z", "program": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", "codePath": "projects/exmp_1.py", "git": {"remote": "**************:Leeroo-AI/mergoo.git", "commit": "8dec73f1083d21e174484b5b8bc75747c4034282"}, "email": "<EMAIL>", "root": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects", "host": "cn-g012.server.mila.quebec", "executable": "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python", "codePathLocal": "exmp_1.py", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 4, "disk": {"/": {"total": "************", "used": "25333678080"}}, "memory": {"total": "1076141596672"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-d472dbc6-acc9-e278-6caa-d4d879b66fca"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-d778d976-a795-04d0-ecc4-fe24c9383e7c"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-0df18309-0a8c-41ba-869d-eaeeead70f51"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-9a8d6133-a0cf-74d0-ea69-69e6518a7de0"}], "slurm": {"cluster_name": "mila", "conf": "/etc/slurm/slurm.conf", "cpus_on_node": "16", "cpus_per_task": "16", "gpus_on_node": "4", "gtids": "0", "job_account": "mila", "job_cpus_per_node": "16", "job_end_time": "**********", "job_gid": "*********", "job_gpus": "0,1,2,3", "job_id": "7068244", "job_name": "moe_support", "job_nodelist": "cn-g012", "job_num_nodes": "1", "job_partition": "short-unkillable", "job_qos": "normal", "job_start_time": "**********", "job_uid": "*********", "job_user": "marya<PERSON>.<PERSON><PERSON>", "jobid": "7068244", "localid": "0", "mem_per_node": "131072", "nnodes": "1", "nodeid": "0", "nodelist": "cn-g012", "prio_process": "0", "procid": "0", "script_context": "prolog_task", "submit_dir": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects", "submit_host": "cn-g021.server.mila.quebec", "task_pid": "2532991", "tasks_per_node": "1", "tmpdir": "/tmp", "topology_addr": "cn-g012", "topology_addr_pattern": "node", "tres_per_task": "cpu:16"}, "cudaVersion": "12.2"}