==================================================
LoRA Fine-tuning Configuration:
Base model: mistralai/Mistral-7B-v0.1
Dataset path: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/pubmed/pubmed_qa/ori_pqau_instruction_response_10k_sample.json
Output directory: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau/
Wandb project: lora-finetuning
Run name: 
==================================================
Dataset loaded: 10000 total samples
Training samples: 9000
Evaluation samples: 1000
[2025-06-25 16:40:32,162] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Starting LoRA fine-tuning...
{'loss': 2.4652, 'grad_norm': 9.59710693359375, 'learning_rate': 0.0001998397435897436, 'epoch': 0.0}
{'loss': 1.8381, 'grad_norm': 1.985697865486145, 'learning_rate': 0.0001983974358974359, 'epoch': 0.03}
{'loss': 1.8122, 'grad_norm': 1.945962905883789, 'learning_rate': 0.00019679487179487178, 'epoch': 0.06}
{'loss': 1.7898, 'grad_norm': 1.823306679725647, 'learning_rate': 0.0001951923076923077, 'epoch': 0.1}
{'loss': 1.7953, 'grad_norm': 2.123161554336548, 'learning_rate': 0.0001935897435897436, 'epoch': 0.13}
{'loss': 1.8208, 'grad_norm': 1.8922276496887207, 'learning_rate': 0.0001919871794871795, 'epoch': 0.16}
{'loss': 1.81, 'grad_norm': 1.8789223432540894, 'learning_rate': 0.00019038461538461538, 'epoch': 0.19}
{'loss': 1.8162, 'grad_norm': 1.8132027387619019, 'learning_rate': 0.00018878205128205127, 'epoch': 0.22}
{'loss': 1.8152, 'grad_norm': 1.8831301927566528, 'learning_rate': 0.0001871794871794872, 'epoch': 0.26}
{'loss': 1.8404, 'grad_norm': 1.8895668983459473, 'learning_rate': 0.0001855769230769231, 'epoch': 0.29}
{'loss': 1.827, 'grad_norm': 2.1043460369110107, 'learning_rate': 0.00018397435897435897, 'epoch': 0.32}
{'loss': 1.775, 'grad_norm': 2.038529872894287, 'learning_rate': 0.00018237179487179487, 'epoch': 0.35}
{'loss': 1.8647, 'grad_norm': 1.9233695268630981, 'learning_rate': 0.00018076923076923077, 'epoch': 0.38}
{'loss': 1.8191, 'grad_norm': 2.0395214557647705, 'learning_rate': 0.0001791666666666667, 'epoch': 0.42}
{'loss': 1.8252, 'grad_norm': 1.9629511833190918, 'learning_rate': 0.00017756410256410257, 'epoch': 0.45}
{'loss': 1.7672, 'grad_norm': 1.9993902444839478, 'learning_rate': 0.00017596153846153846, 'epoch': 0.48}
{'loss': 1.8222, 'grad_norm': 2.0912840366363525, 'learning_rate': 0.00017435897435897436, 'epoch': 0.51}
{'loss': 1.8172, 'grad_norm': 1.9819002151489258, 'learning_rate': 0.00017275641025641026, 'epoch': 0.54}
{'loss': 1.8267, 'grad_norm': 2.123680353164673, 'learning_rate': 0.00017115384615384616, 'epoch': 0.58}
{'loss': 1.8372, 'grad_norm': 2.002261161804199, 'learning_rate': 0.00016955128205128206, 'epoch': 0.61}
{'loss': 1.7959, 'grad_norm': 1.9735636711120605, 'learning_rate': 0.00016794871794871796, 'epoch': 0.64}
{'loss': 1.8112, 'grad_norm': 2.174168825149536, 'learning_rate': 0.00016634615384615386, 'epoch': 0.67}
{'loss': 1.7721, 'grad_norm': 1.9978160858154297, 'learning_rate': 0.00016474358974358976, 'epoch': 0.7}
{'loss': 1.8266, 'grad_norm': 2.0502612590789795, 'learning_rate': 0.00016314102564102565, 'epoch': 0.74}
{'loss': 1.8213, 'grad_norm': 1.971590518951416, 'learning_rate': 0.00016153846153846155, 'epoch': 0.77}
{'loss': 1.8012, 'grad_norm': 2.067819356918335, 'learning_rate': 0.00015993589743589745, 'epoch': 0.8}
{'loss': 1.8396, 'grad_norm': 2.4471848011016846, 'learning_rate': 0.00015833333333333332, 'epoch': 0.83}
{'loss': 1.8098, 'grad_norm': 2.133666515350342, 'learning_rate': 0.00015673076923076925, 'epoch': 0.86}
{'loss': 1.8396, 'grad_norm': 1.9956107139587402, 'learning_rate': 0.00015512820512820515, 'epoch': 0.9}
{'loss': 1.8215, 'grad_norm': 2.1988935470581055, 'learning_rate': 0.00015352564102564105, 'epoch': 0.93}
{'loss': 1.8063, 'grad_norm': 1.995598316192627, 'learning_rate': 0.00015192307692307692, 'epoch': 0.96}
{'loss': 1.8113, 'grad_norm': 2.063180685043335, 'learning_rate': 0.00015032051282051282, 'epoch': 0.99}
{'loss': 1.5019, 'grad_norm': 2.0523080825805664, 'learning_rate': 0.00014871794871794872, 'epoch': 1.02}
{'loss': 1.381, 'grad_norm': 2.428623676300049, 'learning_rate': 0.00014711538461538464, 'epoch': 1.06}
{'loss': 1.3574, 'grad_norm': 2.3025665283203125, 'learning_rate': 0.00014551282051282051, 'epoch': 1.09}
{'loss': 1.3624, 'grad_norm': 2.6631922721862793, 'learning_rate': 0.0001439102564102564, 'epoch': 1.12}
{'loss': 1.3491, 'grad_norm': 2.7005655765533447, 'learning_rate': 0.0001423076923076923, 'epoch': 1.15}
{'loss': 1.348, 'grad_norm': 3.1635324954986572, 'learning_rate': 0.0001407051282051282, 'epoch': 1.18}
{'loss': 1.3603, 'grad_norm': 2.8461594581604004, 'learning_rate': 0.0001391025641025641, 'epoch': 1.22}
{'loss': 1.3448, 'grad_norm': 2.735116481781006, 'learning_rate': 0.0001375, 'epoch': 1.25}
{'loss': 1.3714, 'grad_norm': 3.0248851776123047, 'learning_rate': 0.0001358974358974359, 'epoch': 1.28}
{'loss': 1.3625, 'grad_norm': 2.778353452682495, 'learning_rate': 0.00013429487179487178, 'epoch': 1.31}
{'loss': 1.3607, 'grad_norm': 2.733949661254883, 'learning_rate': 0.0001326923076923077, 'epoch': 1.34}
{'loss': 1.3702, 'grad_norm': 2.909402370452881, 'learning_rate': 0.0001310897435897436, 'epoch': 1.38}
{'loss': 1.3472, 'grad_norm': 2.908738613128662, 'learning_rate': 0.0001294871794871795, 'epoch': 1.41}
{'loss': 1.3583, 'grad_norm': 2.8309216499328613, 'learning_rate': 0.00012788461538461537, 'epoch': 1.44}
{'loss': 1.3711, 'grad_norm': 2.947791814804077, 'learning_rate': 0.00012628205128205127, 'epoch': 1.47}
{'loss': 1.3545, 'grad_norm': 3.2139456272125244, 'learning_rate': 0.0001246794871794872, 'epoch': 1.5}
{'loss': 1.3626, 'grad_norm': 2.9597814083099365, 'learning_rate': 0.0001230769230769231, 'epoch': 1.54}
{'loss': 1.3579, 'grad_norm': 3.010314464569092, 'learning_rate': 0.00012147435897435897, 'epoch': 1.57}
{'loss': 1.3632, 'grad_norm': 2.7516047954559326, 'learning_rate': 0.00011987179487179487, 'epoch': 1.6}
{'loss': 1.3654, 'grad_norm': 3.15960431098938, 'learning_rate': 0.00011826923076923078, 'epoch': 1.63}
{'loss': 1.38, 'grad_norm': 3.1783995628356934, 'learning_rate': 0.00011666666666666668, 'epoch': 1.66}
{'loss': 1.3595, 'grad_norm': 2.958502769470215, 'learning_rate': 0.00011506410256410256, 'epoch': 1.7}
{'loss': 1.3593, 'grad_norm': 2.9373996257781982, 'learning_rate': 0.00011346153846153846, 'epoch': 1.73}
{'loss': 1.372, 'grad_norm': 3.260676622390747, 'learning_rate': 0.00011185897435897436, 'epoch': 1.76}
{'loss': 1.3471, 'grad_norm': 3.127492666244507, 'learning_rate': 0.00011025641025641027, 'epoch': 1.79}
{'loss': 1.3467, 'grad_norm': 2.8735198974609375, 'learning_rate': 0.00010865384615384615, 'epoch': 1.82}
{'loss': 1.3687, 'grad_norm': 2.97033429145813, 'learning_rate': 0.00010705128205128206, 'epoch': 1.86}
{'loss': 1.3678, 'grad_norm': 3.0956075191497803, 'learning_rate': 0.00010544871794871796, 'epoch': 1.89}
{'loss': 1.3541, 'grad_norm': 3.021268606185913, 'learning_rate': 0.00010384615384615386, 'epoch': 1.92}
{'loss': 1.3675, 'grad_norm': 2.9104838371276855, 'learning_rate': 0.00010224358974358974, 'epoch': 1.95}
{'loss': 1.371, 'grad_norm': 3.2636961936950684, 'learning_rate': 0.00010064102564102564, 'epoch': 1.98}
{'loss': 1.1467, 'grad_norm': 4.362855434417725, 'learning_rate': 9.903846153846155e-05, 'epoch': 2.02}
{'loss': 0.7945, 'grad_norm': 4.002874851226807, 'learning_rate': 9.743589743589744e-05, 'epoch': 2.05}
{'loss': 0.7841, 'grad_norm': 5.074804782867432, 'learning_rate': 9.583333333333334e-05, 'epoch': 2.08}
{'loss': 0.7962, 'grad_norm': 4.6274027824401855, 'learning_rate': 9.423076923076924e-05, 'epoch': 2.11}
{'loss': 0.7936, 'grad_norm': 4.557649612426758, 'learning_rate': 9.262820512820513e-05, 'epoch': 2.14}
{'loss': 0.7989, 'grad_norm': 4.553938865661621, 'learning_rate': 9.102564102564103e-05, 'epoch': 2.18}
{'loss': 0.7738, 'grad_norm': 3.813526153564453, 'learning_rate': 8.942307692307693e-05, 'epoch': 2.21}
{'loss': 0.8151, 'grad_norm': 4.426795959472656, 'learning_rate': 8.782051282051283e-05, 'epoch': 2.24}
{'loss': 0.8173, 'grad_norm': 4.850522518157959, 'learning_rate': 8.621794871794873e-05, 'epoch': 2.27}
{'loss': 0.7911, 'grad_norm': 4.148812770843506, 'learning_rate': 8.461538461538461e-05, 'epoch': 2.3}
{'loss': 0.8224, 'grad_norm': 4.55037260055542, 'learning_rate': 8.301282051282053e-05, 'epoch': 2.34}
{'loss': 0.8039, 'grad_norm': 4.2753095626831055, 'learning_rate': 8.141025641025641e-05, 'epoch': 2.37}
{'loss': 0.8075, 'grad_norm': 4.650018215179443, 'learning_rate': 7.980769230769231e-05, 'epoch': 2.4}
{'loss': 0.7936, 'grad_norm': 4.560468673706055, 'learning_rate': 7.820512820512821e-05, 'epoch': 2.43}
{'loss': 0.8074, 'grad_norm': 4.526761531829834, 'learning_rate': 7.660256410256411e-05, 'epoch': 2.46}
{'loss': 0.815, 'grad_norm': 4.465812683105469, 'learning_rate': 7.5********000001e-05, 'epoch': 2.5}
{'loss': 0.8028, 'grad_norm': 4.8853759765625, 'learning_rate': 7.339743589743589e-05, 'epoch': 2.53}
{'loss': 0.7988, 'grad_norm': 4.296356678009033, 'learning_rate': 7.17948717948718e-05, 'epoch': 2.56}
{'loss': 0.8028, 'grad_norm': 4.5810441970825195, 'learning_rate': 7.019230769230769e-05, 'epoch': 2.59}
{'loss': 0.7897, 'grad_norm': 4.416682243347168, 'learning_rate': 6.858974358974359e-05, 'epoch': 2.62}
{'loss': 0.8079, 'grad_norm': 4.335587024688721, 'learning_rate': 6.698717948717949e-05, 'epoch': 2.66}
{'loss': 0.7977, 'grad_norm': 4.778957843780518, 'learning_rate': 6.538461538461539e-05, 'epoch': 2.69}
{'loss': 0.8118, 'grad_norm': 4.373316764831543, 'learning_rate': 6.378205128205128e-05, 'epoch': 2.72}
{'loss': 0.8135, 'grad_norm': 4.45171594619751, 'learning_rate': 6.217948717948718e-05, 'epoch': 2.75}
{'loss': 0.7999, 'grad_norm': 4.429684638977051, 'learning_rate': 6.0576923076923076e-05, 'epoch': 2.78}
{'loss': 0.8161, 'grad_norm': 4.801398754119873, 'learning_rate': 5.897435897435898e-05, 'epoch': 2.82}
{'loss': 0.8032, 'grad_norm': 4.519754409790039, 'learning_rate': 5.737179487179487e-05, 'epoch': 2.85}
{'loss': 0.8146, 'grad_norm': 4.654350757598877, 'learning_rate': 5.576923076923077e-05, 'epoch': 2.88}
{'loss': 0.8324, 'grad_norm': 4.699825286865234, 'learning_rate': 5.4166666666666664e-05, 'epoch': 2.91}
{'loss': 0.7956, 'grad_norm': 4.31113862991333, 'learning_rate': 5.256410256410257e-05, 'epoch': 2.94}
{'loss': 0.8299, 'grad_norm': 4.498342037200928, 'learning_rate': 5.096153846153846e-05, 'epoch': 2.98}
{'loss': 0.7375, 'grad_norm': 6.71644401550293, 'learning_rate': 4.935897435897436e-05, 'epoch': 3.01}
{'loss': 0.4289, 'grad_norm': 9.025991439819336, 'learning_rate': 4.775641025641026e-05, 'epoch': 3.04}
{'loss': 0.409, 'grad_norm': 4.475805759429932, 'learning_rate': 4.615384615384616e-05, 'epoch': 3.07}
{'loss': 0.4088, 'grad_norm': 5.56240701675415, 'learning_rate': 4.455128205128206e-05, 'epoch': 3.1}
{'loss': 0.3972, 'grad_norm': 4.735909461975098, 'learning_rate': 4.294871794871795e-05, 'epoch': 3.14}
{'loss': 0.399, 'grad_norm': 4.572948932647705, 'learning_rate': 4.134615384615385e-05, 'epoch': 3.17}
{'loss': 0.4108, 'grad_norm': 5.1130452156066895, 'learning_rate': 3.974358974358974e-05, 'epoch': 3.2}
{'loss': 0.4092, 'grad_norm': 5.000241279602051, 'learning_rate': 3.814102564102564e-05, 'epoch': 3.23}
{'loss': 0.3905, 'grad_norm': 5.392737865447998, 'learning_rate': 3.653846153846154e-05, 'epoch': 3.26}
{'loss': 0.3974, 'grad_norm': 4.721529960632324, 'learning_rate': 3.4935897435897436e-05, 'epoch': 3.3}
{'loss': 0.4059, 'grad_norm': 4.909647464752197, 'learning_rate': 3.3333333333333335e-05, 'epoch': 3.33}
{'loss': 0.387, 'grad_norm': 4.949917793273926, 'learning_rate': 3.1730769230769234e-05, 'epoch': 3.36}
{'loss': 0.3923, 'grad_norm': 4.9137797355651855, 'learning_rate': 3.012820512820513e-05, 'epoch': 3.39}
{'loss': 0.4069, 'grad_norm': 5.460274696350098, 'learning_rate': 2.8525641025641025e-05, 'epoch': 3.42}
{'loss': 0.3982, 'grad_norm': 5.246048450469971, 'learning_rate': 2.6923076923076923e-05, 'epoch': 3.46}
{'loss': 0.418, 'grad_norm': 5.049222946166992, 'learning_rate': 2.5320512820512822e-05, 'epoch': 3.49}
{'loss': 0.3905, 'grad_norm': 4.997745037078857, 'learning_rate': 2.3717948717948718e-05, 'epoch': 3.52}
{'loss': 0.4134, 'grad_norm': 5.165561199188232, 'learning_rate': 2.2115384615384616e-05, 'epoch': 3.55}
{'loss': 0.4025, 'grad_norm': 5.0313544273376465, 'learning_rate': 2.0512820512820512e-05, 'epoch': 3.58}
{'loss': 0.4016, 'grad_norm': 4.849417209625244, 'learning_rate': 1.891025641025641e-05, 'epoch': 3.62}
{'loss': 0.3918, 'grad_norm': 5.420068740844727, 'learning_rate': 1.730769230769231e-05, 'epoch': 3.65}
{'loss': 0.3876, 'grad_norm': 5.579901695251465, 'learning_rate': 1.5705128205128205e-05, 'epoch': 3.68}
{'loss': 0.4066, 'grad_norm': 5.9170451164245605, 'learning_rate': 1.4102564102564104e-05, 'epoch': 3.71}
{'loss': 0.3986, 'grad_norm': 5.127438545227051, 'learning_rate': 1.25e-05, 'epoch': 3.74}
{'loss': 0.3966, 'grad_norm': 4.872450828552246, 'learning_rate': 1.0897435897435898e-05, 'epoch': 3.78}
{'loss': 0.4007, 'grad_norm': 5.283291816711426, 'learning_rate': 9.294871794871795e-06, 'epoch': 3.81}
{'loss': 0.3975, 'grad_norm': 5.198952674865723, 'learning_rate': 7.692307692307694e-06, 'epoch': 3.84}
{'loss': 0.3984, 'grad_norm': 5.174223899841309, 'learning_rate': 6.08974358974359e-06, 'epoch': 3.87}
{'loss': 0.3986, 'grad_norm': 5.333778381347656, 'learning_rate': 4.487179487179488e-06, 'epoch': 3.9}
{'loss': 0.4021, 'grad_norm': 5.404483795166016, 'learning_rate': 2.884615384615385e-06, 'epoch': 3.94}
{'loss': 0.3924, 'grad_norm': 4.46821928024292, 'learning_rate': 1.282051282051282e-06, 'epoch': 3.97}
{'train_runtime': 1403.0564, 'train_samples_per_second': 28.509, 'train_steps_per_second': 0.889, 'train_loss': 1.0981317607638164, 'epoch': 3.99}
Saving LoRA adapter to: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau/
LoRA fine-tuning completed!
Adapter saved in: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau/
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Wed Jun 25 17:04:00 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2347652
            GPU Utilization               : 64 %
            Memory Utilization            : 25 %
            Max memory usage              : 47290 MiB
            Time                          : 1413154 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2347652
            GPU Utilization               : 64 %
            Memory Utilization            : 23 %
            Max memory usage              : 30524 MiB
            Time                          : 1413153 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2347652
            GPU Utilization               : 64 %
            Memory Utilization            : 23 %
            Max memory usage              : 30524 MiB
            Time                          : 1413152 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2347652
            GPU Utilization               : 64 %
            Memory Utilization            : 23 %
            Max memory usage              : 30524 MiB
            Time                          : 1413151 ms
            Is Running                    : 0

Wed Jun 25 17:04:00 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   40C    P0             118W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   37C    P0             120W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   41C    P0             125W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   35C    P0             123W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
