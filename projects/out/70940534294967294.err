[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: marya<PERSON>ha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250701_135500-2aw6i09v
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run denim-universe-4
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/lora-finetuning
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/lora-finetuning/runs/2aw6i09v

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:00<00:00,  3.40it/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:00<00:00,  4.49it/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:00<00:00,  4.28it/s]

Generating train split: 0 examples [00:00, ? examples/s]
Generating train split: 14818 examples [00:00, 34449.17 examples/s]
Generating train split: 14818 examples [00:00, 33576.53 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/14818 [00:00<?, ? examples/s]
Map:   0%|          | 0/14818 [00:00<?, ? examples/s]
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/lora_finetune.py", line 224, in <module>
    main()
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/lora_finetune.py", line 178, in main
    trainer = SFTTrainer(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py", line 101, in inner_f
    return f(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/deprecation.py", line 165, in wrapped_func
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 368, in __init__
    train_dataset = self._prepare_dataset(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 477, in _prepare_dataset
    return self._prepare_non_packed_dataloader(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 549, in _prepare_non_packed_dataloader
    tokenized_dataset = dataset.map(tokenize, **map_kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 557, in wrapper
    out: Union["Dataset", "DatasetDict"] = func(self, *args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3079, in map
    for rank, done, content in Dataset._map_single(**dataset_kwargs):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3525, in _map_single
    for i, batch in iter_outputs(shard_iterable):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3475, in iter_outputs
    yield i, apply_function(example, i, offset=offset)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3398, in apply_function
    processed_inputs = function(*fn_args, *additional_args, **fn_kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 513, in tokenize
    element[dataset_text_field] if formatting_func is None else formatting_func(element),
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/lora_finetune.py", line 155, in formatting_func
    return [f"### Question: {q}\n### Answer: {a}" for q, a in zip(example["instruction"], example["response"])]
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/formatting/formatting.py", line 278, in __getitem__
    value = self.data[key]
KeyError: 'instruction'
