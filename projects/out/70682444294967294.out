[2025-06-26 16:36:37,446] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
Configuration:Configuration:

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

Configuration:
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

[2025-06-26 16:36:49,151] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:36:49,163] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:36:49,187] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:36:49,187] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:36:51,940] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:36:51,962] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:36:51,964] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:36:51,987] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:36:51,988] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
Creating model checkpoint...MoE Layer Index : [*]

MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-26 16:37:05,348][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 16:37:05,434][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 16:37:05,467][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mmistral-drug_abuse_pubmedqau-moe[0m at: [34mhttps://wandb.ai/sarath-chandar/mistral-moe-training/runs/z8ve741y[0m
[1;34mwandb[0m: Find logs at: [1;35m../../../../../../../../network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250626_163652-z8ve741y/logs[0m
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Thu Jun 26 16:37:09 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2533118
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3912 MiB
            Time                          : 18829 ms
            Is Running                    : 0
        Process ID                        : 2533119
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 19491 ms
            Is Running                    : 0
        Process ID                        : 2533120
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 19480 ms
            Is Running                    : 0
        Process ID                        : 2533121
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 19704 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2533118
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4226 MiB
            Time                          : 18826 ms
            Is Running                    : 0
        Process ID                        : 2533119
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19487 ms
            Is Running                    : 0
        Process ID                        : 2533120
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19476 ms
            Is Running                    : 0
        Process ID                        : 2533121
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19703 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2533118
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4226 MiB
            Time                          : 18825 ms
            Is Running                    : 0
        Process ID                        : 2533119
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19485 ms
            Is Running                    : 0
        Process ID                        : 2533120
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19473 ms
            Is Running                    : 0
        Process ID                        : 2533121
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19702 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2533118
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3628 MiB
            Time                          : 18821 ms
            Is Running                    : 0
        Process ID                        : 2533119
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 19484 ms
            Is Running                    : 0
        Process ID                        : 2533120
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 19471 ms
            Is Running                    : 0
        Process ID                        : 2533121
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 19701 ms
            Is Running                    : 0

Thu Jun 26 16:37:09 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   31C    P0             116W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   27C    P0              85W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   31C    P0             117W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   30C    P0             115W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
