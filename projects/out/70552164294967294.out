[2025-06-24 19:57:29,959] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
Configuration:
Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe_fulldataset

experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe_fulldataset

experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe_fulldataset

[2025-06-24 19:57:41,659] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 19:57:41,660] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 19:57:41,668] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe_fulldataset

[2025-06-24 19:57:42,169] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 19:57:44,245] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-24 19:57:44,257] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-24 19:57:44,257] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-24 19:57:44,257] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-24 19:57:44,698] [INFO] [comm.py:652:init_distributed] cdb=None
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-24 19:58:06,584][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 19:58:06,597][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 19:58:06,639][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 19:58:06,660][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
Loading model...
checkpoint saved at data/mistral_lora_moe
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
Total weights: 611, Router weights: 96
Loading dataset...
Total weights: 611, Router weights: 96Total weights: 611, Router weights: 96
Loading dataset...
Loading dataset...

Total weights: 611, Router weights: 96
Loading dataset...
Dataset sizes - Train: 24184, Val: 806, Test: 1882
Dataset sizes - Train: 24184, Val: 806, Test: 1882
Dataset sizes - Train: 24184, Val: 806, Test: 1882
Dataset sizes - Train: 24184, Val: 806, Test: 1882
cn-l044:821612:821612 [0] NCCL INFO Bootstrap: Using ibp13s0:10.20.9.144<0>
cn-l044:821612:821612 [0] NCCL INFO cudaDriverVersion 12080
cn-l044:821612:821612 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-l044:821615:821615 [3] NCCL INFO cudaDriverVersion 12080
cn-l044:821615:821615 [3] NCCL INFO Bootstrap: Using ibp13s0:10.20.9.144<0>
cn-l044:821615:821615 [3] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-l044:821613:821613 [1] NCCL INFO cudaDriverVersion 12080
cn-l044:821614:821614 [2] NCCL INFO cudaDriverVersion 12080
cn-l044:821613:821613 [1] NCCL INFO Bootstrap: Using ibp13s0:10.20.9.144<0>
cn-l044:821614:821614 [2] NCCL INFO Bootstrap: Using ibp13s0:10.20.9.144<0>
cn-l044:821613:821613 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-l044:821614:821614 [2] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-l044:821612:821612 [0] NCCL INFO Comm config Blocking set to 1
cn-l044:821615:821615 [3] NCCL INFO Comm config Blocking set to 1
cn-l044:821613:821613 [1] NCCL INFO Comm config Blocking set to 1
cn-l044:821614:821614 [2] NCCL INFO Comm config Blocking set to 1
cn-l044:821612:822308 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-l044:821613:822310 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-l044:821614:822311 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-l044:821615:822309 [3] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-l044:821615:822309 [3] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [RO]; OOB ibp13s0:10.20.9.144<0>
cn-l044:821615:822309 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-l044:821615:822309 [3] NCCL INFO Using network IB
cn-l044:821613:822310 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [RO]; OOB ibp13s0:10.20.9.144<0>
cn-l044:821614:822311 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [RO]; OOB ibp13s0:10.20.9.144<0>
cn-l044:821612:822308 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [RO]; OOB ibp13s0:10.20.9.144<0>
cn-l044:821615:822309 [3] NCCL INFO ncclCommInitRankConfig comm 0x55a4084745c0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId e1000 commId 0xc336afc0d18af517 - Init START
cn-l044:821613:822310 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-l044:821613:822310 [1] NCCL INFO Using network IB
cn-l044:821614:822311 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-l044:821614:822311 [2] NCCL INFO Using network IB
cn-l044:821612:822308 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-l044:821612:822308 [0] NCCL INFO Using network IB
cn-l044:821613:822310 [1] NCCL INFO ncclCommInitRankConfig comm 0x560dd5c20650 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 61000 commId 0xc336afc0d18af517 - Init START
cn-l044:821614:822311 [2] NCCL INFO ncclCommInitRankConfig comm 0x562ae93ed570 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId ca000 commId 0xc336afc0d18af517 - Init START
cn-l044:821612:822308 [0] NCCL INFO ncclCommInitRankConfig comm 0x563e771f05d0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 4a000 commId 0xc336afc0d18af517 - Init START
cn-l044:821614:822311 [2] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-l044:821612:822308 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-l044:821615:822309 [3] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-l044:821613:822310 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-l044:821615:822309 [3] NCCL INFO Bootstrap timings total 0.077140 (create 0.000028, send 0.000108, recv 0.033008, ring 0.026297, delay 0.000000)
cn-l044:821612:822308 [0] NCCL INFO Bootstrap timings total 0.074061 (create 0.000017, send 0.029984, recv 0.000052, ring 0.043626, delay 0.000000)
cn-l044:821613:822310 [1] NCCL INFO Bootstrap timings total 0.075799 (create 0.000021, send 0.000079, recv 0.001178, ring 0.000037, delay 0.000000)
cn-l044:821614:822311 [2] NCCL INFO Bootstrap timings total 0.074675 (create 0.000020, send 0.000080, recv 0.000077, ring 0.044030, delay 0.000000)
cn-l044:821613:822310 [1] NCCL INFO Setting affinity for GPU 1 to 55555555
cn-l044:821614:822311 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-l044:821612:822308 [0] NCCL INFO Setting affinity for GPU 0 to 55555555
cn-l044:821615:822309 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-l044:821613:822310 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-l044:821612:822308 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-l044:821614:822311 [2] NCCL INFO comm 0x562ae93ed570 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-l044:821613:822310 [1] NCCL INFO comm 0x560dd5c20650 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-l044:821612:822308 [0] NCCL INFO comm 0x563e771f05d0 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-l044:821615:822309 [3] NCCL INFO comm 0x55a4084745c0 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-l044:821614:822311 [2] NCCL INFO Trees [0] 3/-1/-1->2->1 [1] 3/-1/-1->2->1
cn-l044:821614:822311 [2] NCCL INFO P2P Chunksize set to 131072
cn-l044:821613:822310 [1] NCCL INFO Trees [0] 2/-1/-1->1->0 [1] 2/-1/-1->1->0
cn-l044:821613:822310 [1] NCCL INFO P2P Chunksize set to 131072
cn-l044:821612:822308 [0] NCCL INFO Channel 00/02 : 0 1 2 3
cn-l044:821612:822308 [0] NCCL INFO Channel 01/02 : 0 1 2 3
cn-l044:821615:822309 [3] NCCL INFO Trees [0] -1/-1/-1->3->2 [1] -1/-1/-1->3->2
cn-l044:821615:822309 [3] NCCL INFO P2P Chunksize set to 131072
cn-l044:821614:822321 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 8
cn-l044:821613:822322 [1] NCCL INFO [Proxy Service] Device 1 CPU core 20
cn-l044:821612:822308 [0] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1
cn-l044:821615:822324 [3] NCCL INFO [Proxy Service] Device 3 CPU core 28
cn-l044:821614:822320 [2] NCCL INFO [Proxy Service] Device 2 CPU core 6
cn-l044:821613:822323 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 30
cn-l044:821612:822308 [0] NCCL INFO P2P Chunksize set to 131072
cn-l044:821615:822325 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 24
cn-l044:821612:822308 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 0 directMode 0
cn-l044:821612:822326 [0] NCCL INFO [Proxy Service] Device 0 CPU core 16
cn-l044:821612:822327 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 26
cn-l044:821615:822309 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821615:822309 [3] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821614:822311 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821614:822311 [2] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821613:822310 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821613:822310 [1] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821612:822308 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821612:822308 [0] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821612:822308 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-l044:821613:822310 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-l044:821613:822310 [1] NCCL INFO ncclCommInitRankConfig comm 0x560dd5c20650 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 61000 commId 0xc336afc0d18af517 - Init COMPLETE
cn-l044:821615:822309 [3] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-l044:821615:822309 [3] NCCL INFO ncclCommInitRankConfig comm 0x55a4084745c0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId e1000 commId 0xc336afc0d18af517 - Init COMPLETE
cn-l044:821612:822308 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-l044:821612:822308 [0] NCCL INFO ncclCommInitRankConfig comm 0x563e771f05d0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 4a000 commId 0xc336afc0d18af517 - Init COMPLETE
cn-l044:821613:822310 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.29 (kernels 0.16, alloc 0.03, bootstrap 0.08, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.00, rest 0.01)
cn-l044:821614:822311 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-l044:821614:822311 [2] NCCL INFO ncclCommInitRankConfig comm 0x562ae93ed570 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId ca000 commId 0xc336afc0d18af517 - Init COMPLETE
cn-l044:821615:822309 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.29 (kernels 0.16, alloc 0.03, bootstrap 0.08, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.00, rest 0.01)
cn-l044:821612:822308 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 0.29 (kernels 0.16, alloc 0.04, bootstrap 0.07, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.00, rest 0.01)
cn-l044:821614:822311 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.29 (kernels 0.16, alloc 0.03, bootstrap 0.08, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.00, rest 0.01)
cn-l044:821615:822328 [3] NCCL INFO Channel 00 : 3[3] -> 0[0] via SHM/direct/direct
cn-l044:821614:822331 [2] NCCL INFO Channel 00 : 2[2] -> 3[3] via SHM/direct/direct
cn-l044:821612:822330 [0] NCCL INFO Channel 00 : 0[0] -> 1[1] via SHM/direct/direct
cn-l044:821615:822328 [3] NCCL INFO Channel 01 : 3[3] -> 0[0] via SHM/direct/direct
cn-l044:821614:822331 [2] NCCL INFO Channel 01 : 2[2] -> 3[3] via SHM/direct/direct
cn-l044:821612:822330 [0] NCCL INFO Channel 01 : 0[0] -> 1[1] via SHM/direct/direct
cn-l044:821613:822329 [1] NCCL INFO Channel 00 : 1[1] -> 2[2] via SHM/direct/direct
cn-l044:821613:822329 [1] NCCL INFO Channel 01 : 1[1] -> 2[2] via SHM/direct/direct
cn-l044:821614:822331 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-l044:821612:822330 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-l044:821615:822328 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-l044:821613:822329 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
Model parameters - Total: 7,252,480,000, Trainable: 1,879,572,480
Trainable percentage: 25.92%
Starting training...
cn-l044:821612:821612 [0] NCCL INFO Comm config Blocking set to 1
cn-l044:821612:822392 [0] NCCL INFO Using network IB
cn-l044:821612:822392 [0] NCCL INFO ncclCommInitRankConfig comm 0x563e7333df90 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 4a000 commId 0x110f98547f72c3ab - Init START
cn-l044:821615:821615 [3] NCCL INFO Comm config Blocking set to 1
cn-l044:821615:822406 [3] NCCL INFO Using network IB
cn-l044:821615:822406 [3] NCCL INFO ncclCommInitRankConfig comm 0x55a3e8353030 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId e1000 commId 0x110f98547f72c3ab - Init START
cn-l044:821613:821613 [1] NCCL INFO Comm config Blocking set to 1
cn-l044:821613:822409 [1] NCCL INFO Using network IB
cn-l044:821613:822409 [1] NCCL INFO ncclCommInitRankConfig comm 0x560dd2902d10 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 61000 commId 0x110f98547f72c3ab - Init START
cn-l044:821614:821614 [2] NCCL INFO Comm config Blocking set to 1
cn-l044:821614:822413 [2] NCCL INFO Using network IB
cn-l044:821614:822413 [2] NCCL INFO ncclCommInitRankConfig comm 0x562ae3a80250 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId ca000 commId 0x110f98547f72c3ab - Init START
cn-l044:821614:822413 [2] NCCL INFO Bootstrap timings total 0.000799 (create 0.000028, send 0.000092, recv 0.000099, ring 0.000042, delay 0.000000)
cn-l044:821613:822409 [1] NCCL INFO Bootstrap timings total 0.094018 (create 0.000030, send 0.000089, recv 0.093759, ring 0.000030, delay 0.000000)
cn-l044:821615:822406 [3] NCCL INFO Bootstrap timings total 0.121448 (create 0.000030, send 0.000089, recv 0.000095, ring 0.000458, delay 0.000000)
cn-l044:821612:822392 [0] NCCL INFO Bootstrap timings total 3.572430 (create 0.000027, send 0.000101, recv 3.478516, ring 0.093648, delay 0.000000)
cn-l044:821614:822413 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-l044:821613:822409 [1] NCCL INFO Setting affinity for GPU 1 to 55555555
cn-l044:821613:822409 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-l044:821615:822406 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-l044:821612:822392 [0] NCCL INFO Setting affinity for GPU 0 to 55555555
cn-l044:821612:822392 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-l044:821613:822409 [1] NCCL INFO comm 0x560dd2902d10 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-l044:821612:822392 [0] NCCL INFO comm 0x563e7333df90 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-l044:821614:822413 [2] NCCL INFO comm 0x562ae3a80250 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-l044:821615:822406 [3] NCCL INFO comm 0x55a3e8353030 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-l044:821613:822409 [1] NCCL INFO Trees [0] 2/-1/-1->1->0 [1] 2/-1/-1->1->0
cn-l044:821613:822409 [1] NCCL INFO P2P Chunksize set to 131072
cn-l044:821612:822392 [0] NCCL INFO Channel 00/02 : 0 1 2 3
cn-l044:821612:822392 [0] NCCL INFO Channel 01/02 : 0 1 2 3
cn-l044:821614:822413 [2] NCCL INFO Trees [0] 3/-1/-1->2->1 [1] 3/-1/-1->2->1
cn-l044:821614:822413 [2] NCCL INFO P2P Chunksize set to 131072
cn-l044:821614:822416 [2] NCCL INFO [Proxy Service] Device 2 CPU core 2
cn-l044:821614:822417 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 22
cn-l044:821615:822406 [3] NCCL INFO Trees [0] -1/-1/-1->3->2 [1] -1/-1/-1->3->2
cn-l044:821613:822414 [1] NCCL INFO [Proxy Service] Device 1 CPU core 18
cn-l044:821612:822392 [0] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1
cn-l044:821612:822392 [0] NCCL INFO P2P Chunksize set to 131072
cn-l044:821615:822406 [3] NCCL INFO P2P Chunksize set to 131072
cn-l044:821613:822415 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 0
cn-l044:821612:822392 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 0 directMode 0
cn-l044:821615:822418 [3] NCCL INFO [Proxy Service] Device 3 CPU core 10
cn-l044:821612:822420 [0] NCCL INFO [Proxy Service] Device 0 CPU core 30
cn-l044:821615:822419 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 12
cn-l044:821612:822421 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 28
cn-l044:821615:822406 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821615:822406 [3] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821613:822409 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821613:822409 [1] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821612:822392 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821612:822392 [0] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821614:822413 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-l044:821614:822413 [2] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
cn-l044:821612:822392 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-l044:821615:822406 [3] NCCL INFO ncclCommInitRankConfig comm 0x55a3e8353030 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId e1000 commId 0x110f98547f72c3ab - Init COMPLETE
cn-l044:821613:822409 [1] NCCL INFO ncclCommInitRankConfig comm 0x560dd2902d10 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 61000 commId 0x110f98547f72c3ab - Init COMPLETE
cn-l044:821612:822392 [0] NCCL INFO ncclCommInitRankConfig comm 0x563e7333df90 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 4a000 commId 0x110f98547f72c3ab - Init COMPLETE
cn-l044:821614:822413 [2] NCCL INFO ncclCommInitRankConfig comm 0x562ae3a80250 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId ca000 commId 0x110f98547f72c3ab - Init COMPLETE
cn-l044:821615:822406 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.15 (kernels 0.00, alloc 0.00, bootstrap 0.12, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.01, rest 0.01)
cn-l044:821613:822409 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.12 (kernels 0.00, alloc 0.00, bootstrap 0.09, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.01, rest 0.00)
cn-l044:821612:822392 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 3.60 (kernels 0.00, alloc 0.00, bootstrap 3.57, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.01, rest 0.01)
cn-l044:821614:822413 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.03 (kernels 0.00, alloc 0.00, bootstrap 0.00, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.01, rest 0.00)
cn-l044:821614:822425 [2] NCCL INFO Channel 00 : 2[2] -> 3[3] via SHM/direct/direct
cn-l044:821613:822423 [1] NCCL INFO Channel 00 : 1[1] -> 2[2] via SHM/direct/direct
cn-l044:821612:822424 [0] NCCL INFO Channel 00 : 0[0] -> 1[1] via SHM/direct/direct
cn-l044:821615:822422 [3] NCCL INFO Channel 00 : 3[3] -> 0[0] via SHM/direct/direct
cn-l044:821612:822424 [0] NCCL INFO Channel 01 : 0[0] -> 1[1] via SHM/direct/direct
cn-l044:821614:822425 [2] NCCL INFO Channel 01 : 2[2] -> 3[3] via SHM/direct/direct
cn-l044:821615:822422 [3] NCCL INFO Channel 01 : 3[3] -> 0[0] via SHM/direct/direct
cn-l044:821613:822423 [1] NCCL INFO Channel 01 : 1[1] -> 2[2] via SHM/direct/direct
cn-l044:821613:822423 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-l044:821615:822422 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-l044:821612:822424 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-l044:821614:822425 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
{'loss': 1.4955, 'grad_norm': 9.899447441101074, 'learning_rate': 9.9933818663137e-06, 'epoch': 0.0}
{'loss': 0.887, 'grad_norm': 3.4703164100646973, 'learning_rate': 9.83454665784249e-06, 'epoch': 0.02}
{'loss': 0.7791, 'grad_norm': 2.7723164558410645, 'learning_rate': 9.669093315684978e-06, 'epoch': 0.03}
{'loss': 0.7428, 'grad_norm': 2.703068971633911, 'learning_rate': 9.503639973527467e-06, 'epoch': 0.05}
{'loss': 0.7293, 'grad_norm': 2.8727054595947266, 'learning_rate': 9.338186631369954e-06, 'epoch': 0.07}
{'loss': 0.7004, 'grad_norm': 2.5415821075439453, 'learning_rate': 9.172733289212444e-06, 'epoch': 0.08}
{'loss': 0.6819, 'grad_norm': 2.5603034496307373, 'learning_rate': 9.007279947054931e-06, 'epoch': 0.1}
{'loss': 0.6688, 'grad_norm': 2.548593759536743, 'learning_rate': 8.84182660489742e-06, 'epoch': 0.12}
{'loss': 0.6603, 'grad_norm': 2.520782709121704, 'learning_rate': 8.676373262739908e-06, 'epoch': 0.13}
{'loss': 0.6508, 'grad_norm': 2.7554750442504883, 'learning_rate': 8.510919920582397e-06, 'epoch': 0.15}
{'loss': 0.6571, 'grad_norm': 2.042721748352051, 'learning_rate': 8.345466578424885e-06, 'epoch': 0.17}
{'loss': 0.6377, 'grad_norm': 2.1980905532836914, 'learning_rate': 8.180013236267374e-06, 'epoch': 0.18}
{'loss': 0.6348, 'grad_norm': 2.3612637519836426, 'learning_rate': 8.014559894109862e-06, 'epoch': 0.2}
{'loss': 0.6442, 'grad_norm': 2.4156746864318848, 'learning_rate': 7.849106551952351e-06, 'epoch': 0.22}
{'loss': 0.6249, 'grad_norm': 2.4941964149475098, 'learning_rate': 7.683653209794839e-06, 'epoch': 0.23}
{'loss': 0.6296, 'grad_norm': 2.436131715774536, 'learning_rate': 7.518199867637327e-06, 'epoch': 0.25}
{'loss': 0.6116, 'grad_norm': 2.076014995574951, 'learning_rate': 7.352746525479816e-06, 'epoch': 0.26}
{'loss': 0.6147, 'grad_norm': 2.258288621902466, 'learning_rate': 7.187293183322304e-06, 'epoch': 0.28}
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-l044:821614:822320 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-l044:821614:825790 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-l044:821614:825790 [2] NCCL INFO comm 0x562ae93ed570 rank 2 nranks 4 cudaDev 2 busId ca000 - Abort COMPLETE
cn-l044:821613:822414 [1] NCCL INFO [Service thread] Connection closed by localRank 2
cn-l044:821615:822418 [3] NCCL INFO [Service thread] Connection closed by localRank 2
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Tue Jun 24 20:15:20 2025
Driver Version                            : 570.133.20
CUDA Version                              : 12.8

Attached GPUs                             : 4
GPU ********:4A:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 821614
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3714 MiB
            Time                          : 1057083 ms
            Is Running                    : 0
        Process ID                        : 821612
            GPU Utilization               : 79 %
            Memory Utilization            : 23 %
            Max memory usage              : 33320 MiB
            Time                          : 1057806 ms
            Is Running                    : 0
        Process ID                        : 821613
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3714 MiB
            Time                          : 1058049 ms
            Is Running                    : 0
        Process ID                        : 821615
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3714 MiB
            Time                          : 1057535 ms
            Is Running                    : 0

GPU ********:61:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 821614
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4210 MiB
            Time                          : 1057083 ms
            Is Running                    : 0
        Process ID                        : 821612
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4210 MiB
            Time                          : 1057805 ms
            Is Running                    : 0
        Process ID                        : 821613
            GPU Utilization               : 80 %
            Memory Utilization            : 23 %
            Max memory usage              : 32958 MiB
            Time                          : 1058047 ms
            Is Running                    : 0
        Process ID                        : 821615
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4210 MiB
            Time                          : 1057534 ms
            Is Running                    : 0

GPU ********:CA:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 821614
            GPU Utilization               : 79 %
            Memory Utilization            : 23 %
            Max memory usage              : 32958 MiB
            Time                          : 1057082 ms
            Is Running                    : 0
        Process ID                        : 821612
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4210 MiB
            Time                          : 1057803 ms
            Is Running                    : 0
        Process ID                        : 821613
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4210 MiB
            Time                          : 1058046 ms
            Is Running                    : 0
        Process ID                        : 821615
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4210 MiB
            Time                          : 1057534 ms
            Is Running                    : 0

GPU ********:E1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 821614
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3620 MiB
            Time                          : 1057082 ms
            Is Running                    : 0
        Process ID                        : 821612
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3620 MiB
            Time                          : 1057802 ms
            Is Running                    : 0
        Process ID                        : 821613
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3620 MiB
            Time                          : 1058045 ms
            Is Running                    : 0
        Process ID                        : 821615
            GPU Utilization               : 80 %
            Memory Utilization            : 23 %
            Max memory usage              : 33320 MiB
            Time                          : 1057533 ms
            Is Running                    : 0

Tue Jun 24 20:15:20 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 570.133.20             Driver Version: 570.133.20     CUDA Version: 12.8     |
|-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA L40S                    On  |   ********:4A:00.0 Off |                    0 |
| N/A   43C    P0            112W /  325W |       0MiB /  46068MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
|   1  NVIDIA L40S                    On  |   ********:61:00.0 Off |                    0 |
| N/A   44C    P0            118W /  325W |       0MiB /  46068MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
|   2  NVIDIA L40S                    On  |   ********:CA:00.0 Off |                    0 |
| N/A   42C    P0            102W /  325W |       0MiB /  46068MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
|   3  NVIDIA L40S                    On  |   ********:E1:00.0 Off |                    0 |
| N/A   41C    P0            106W /  325W |       0MiB /  46068MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
                                                                                         
+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI              PID   Type   Process name                        GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+
