Configuration:
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-26 14:27:00,501][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
Loading model...
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
[2025-06-26 14:27:41,346] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:27:43,138] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
cn-g012:2501858:2501858 [0] NCCL INFO cudaDriverVersion 12020
cn-g012:2501858:2501858 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2501858:2501858 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2501858:2502249 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2501858:2502249 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2501858:2502249 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2501858:2502249 [2] NCCL INFO Using network IB
cn-g012:2501858:2502248 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2501858:2502248 [1] NCCL INFO Using network IB
cn-g012:2501858:2502247 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2501858:2502247 [0] NCCL INFO Using network IB
cn-g012:2501858:2502250 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2501858:2502250 [3] NCCL INFO Using network IB
cn-g012:2501858:2502249 [2] NCCL INFO ncclCommInitAll comm 0x55adc321f5c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x7d169d73a062aac0 - Init START
cn-g012:2501858:2502250 [3] NCCL INFO ncclCommInitAll comm 0x55adc4a1f740 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x7d169d73a062aac0 - Init START
cn-g012:2501858:2502248 [1] NCCL INFO ncclCommInitAll comm 0x55ae31d13ce0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x7d169d73a062aac0 - Init START
cn-g012:2501858:2502247 [0] NCCL INFO ncclCommInitAll comm 0x55adc025f340 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x7d169d73a062aac0 - Init START
cn-g012:2501858:2502247 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2501858:2502248 [1] NCCL INFO Bootstrap timings total 0.001207 (create 0.000021, send 0.000144, recv 0.000616, ring 0.000320, delay 0.000000)
cn-g012:2501858:2502250 [3] NCCL INFO Bootstrap timings total 0.001230 (create 0.000039, send 0.000141, recv 0.000079, ring 0.000177, delay 0.000000)
cn-g012:2501858:2502249 [2] NCCL INFO Bootstrap timings total 0.001250 (create 0.000041, send 0.000784, recv 0.000066, ring 0.000072, delay 0.000000)
cn-g012:2501858:2502247 [0] NCCL INFO Bootstrap timings total 0.001238 (create 0.000029, send 0.000121, recv 0.000118, ring 0.000669, delay 0.000000)
cn-g012:2501858:2502248 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g012:2501858:2502248 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g012:2501858:2502247 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g012:2501858:2502249 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g012:2501858:2502250 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g012:2501858:2502247 [0] NCCL INFO comm 0x55adc025f340 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g012:2501858:2502248 [1] NCCL INFO comm 0x55ae31d13ce0 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g012:2501858:2502250 [3] NCCL INFO comm 0x55adc4a1f740 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g012:2501858:2502247 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g012:2501858:2502247 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g012:2501858:2502247 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g012:2501858:2502247 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g012:2501858:2502247 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g012:2501858:2502248 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g012:2501858:2502248 [1] NCCL INFO P2P Chunksize set to 524288
cn-g012:2501858:2502247 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g012:2501858:2502247 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g012:2501858:2502247 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g012:2501858:2502247 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g012:2501858:2502247 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g012:2501858:2502250 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g012:2501858:2502247 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g012:2501858:2502250 [3] NCCL INFO P2P Chunksize set to 524288
cn-g012:2501858:2502247 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g012:2501858:2502247 [0] NCCL INFO P2P Chunksize set to 524288
cn-g012:2501858:2502249 [2] NCCL INFO comm 0x55adc321f5c0 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g012:2501858:2502249 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g012:2501858:2502249 [2] NCCL INFO P2P Chunksize set to 524288
cn-g012:2501858:2502247 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 1
cn-g012:2501858:2502254 [0] NCCL INFO [Proxy Service] Device 0 CPU core 5
cn-g012:2501858:2502255 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 2
cn-g012:2501858:2502256 [1] NCCL INFO [Proxy Service] Device 1 CPU core 7
cn-g012:2501858:2502257 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 12
cn-g012:2501858:2502258 [2] NCCL INFO [Proxy Service] Device 2 CPU core 6
cn-g012:2501858:2502259 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 15
cn-g012:2501858:2502260 [3] NCCL INFO [Proxy Service] Device 3 CPU core 13
cn-g012:2501858:2502261 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 0
cn-g012:2501858:2502247 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2501858:2502247 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2501858:2502247 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g012:2501858:2502248 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2501858:2502248 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2501858:2502249 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2501858:2502249 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2501858:2502250 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2501858:2502250 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2501858:2502249 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2501858:2502249 [2] NCCL INFO ncclCommInitAll comm 0x55adc321f5c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x7d169d73a062aac0 - Init COMPLETE
cn-g012:2501858:2502249 [2] NCCL INFO Init timings - ncclCommInitAll: rank 2 nranks 4 total 0.49 (kernels 0.29, alloc 0.03, bootstrap 0.00, allgathers 0.00, topo 0.07, graphs 0.00, connections 0.05, rest 0.03)
cn-g012:2501858:2502247 [0] NCCL INFO ncclCommInitAll comm 0x55adc025f340 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x7d169d73a062aac0 - Init COMPLETE
cn-g012:2501858:2502247 [0] NCCL INFO Init timings - ncclCommInitAll: rank 0 nranks 4 total 0.49 (kernels 0.29, alloc 0.03, bootstrap 0.00, allgathers 0.00, topo 0.07, graphs 0.00, connections 0.05, rest 0.03)
cn-g012:2501858:2502248 [1] NCCL INFO ncclCommInitAll comm 0x55ae31d13ce0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x7d169d73a062aac0 - Init COMPLETE
cn-g012:2501858:2502248 [1] NCCL INFO Init timings - ncclCommInitAll: rank 1 nranks 4 total 0.49 (kernels 0.30, alloc 0.03, bootstrap 0.00, allgathers 0.01, topo 0.07, graphs 0.00, connections 0.05, rest 0.02)
cn-g012:2501858:2502250 [3] NCCL INFO ncclCommInitAll comm 0x55adc4a1f740 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x7d169d73a062aac0 - Init COMPLETE
cn-g012:2501858:2502250 [3] NCCL INFO Init timings - ncclCommInitAll: rank 3 nranks 4 total 0.49 (kernels 0.29, alloc 0.03, bootstrap 0.00, allgathers 0.00, topo 0.07, graphs 0.00, connections 0.05, rest 0.04)
cn-g012:2501858:2502262 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502262 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/direct pointer/read
cn-g012:2501858:2502265 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/direct pointer/read
cn-g012:2501858:2502263 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/direct pointer/read
cn-g012:2501858:2502264 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2501858:2502265 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2501858:2502262 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2501858:2502263 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Thu Jun 26 14:36:06 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2501858
            GPU Utilization               : 49 %
            Memory Utilization            : 19 %
            Max memory usage              : 62230 MiB
            Time                          : 0 ms
            Is Running                    : 1

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2501858
            GPU Utilization               : 47 %
            Memory Utilization            : 14 %
            Max memory usage              : 27698 MiB
            Time                          : 0 ms
            Is Running                    : 1

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2501858
            GPU Utilization               : 48 %
            Memory Utilization            : 14 %
            Max memory usage              : 27698 MiB
            Time                          : 0 ms
            Is Running                    : 1

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2501858
            GPU Utilization               : 48 %
            Memory Utilization            : 14 %
            Max memory usage              : 27108 MiB
            Time                          : 0 ms
            Is Running                    : 1

Thu Jun 26 14:36:06 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   42C    P0             121W / 500W |  62238MiB / 81920MiB |     53%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   34C    P0              87W / 500W |  27706MiB / 81920MiB |     46%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   43C    P0             122W / 500W |  27706MiB / 81920MiB |     46%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   37C    P0             115W / 500W |  27116MiB / 81920MiB |     44%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|    0   N/A  N/A   2501858      C   python                                    62230MiB |
|    1   N/A  N/A   2501858      C   python                                    27698MiB |
|    2   N/A  N/A   2501858      C   python                                    27698MiB |
|    3   N/A  N/A   2501858      C   python                                    27108MiB |
+---------------------------------------------------------------------------------------+
