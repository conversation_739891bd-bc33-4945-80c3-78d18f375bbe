[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: marya<PERSON><PERSON> (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250625_164026-cds3cm77
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run eager-jazz-3
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/lora-finetuning
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/lora-finetuning/runs/cds3cm77

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:00<00:00,  7.14it/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:00<00:00,  8.55it/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:00<00:00,  8.28it/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/10000 [00:00<?, ? examples/s]
Map:  40%|████      | 4000/10000 [00:00<00:00, 24879.06 examples/s]
Map:  80%|████████  | 8000/10000 [00:00<00:00, 25638.62 examples/s]
Map: 100%|██████████| 10000/10000 [00:00<00:00, 25001.08 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.

  0%|          | 0/1248 [00:00<?, ?it/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

  0%|          | 1/1248 [00:03<1:16:10,  3.66s/it]
                                                  

  0%|          | 1/1248 [00:03<1:16:10,  3.66s/it]
  0%|          | 2/1248 [00:04<44:20,  2.14s/it]  
  0%|          | 3/1248 [00:05<34:05,  1.64s/it]
  0%|          | 4/1248 [00:07<30:46,  1.48s/it]
  0%|          | 5/1248 [00:08<28:07,  1.36s/it]
  0%|          | 6/1248 [00:09<25:55,  1.25s/it]
  1%|          | 7/1248 [00:10<25:23,  1.23s/it]
  1%|          | 8/1248 [00:11<26:18,  1.27s/it]
  1%|          | 9/1248 [00:12<24:49,  1.20s/it]
  1%|          | 10/1248 [00:13<23:55,  1.16s/it]
                                                 

  1%|          | 10/1248 [00:13<23:55,  1.16s/it]
  1%|          | 11/1248 [00:14<23:15,  1.13s/it]
  1%|          | 12/1248 [00:16<23:47,  1.15s/it]
  1%|          | 13/1248 [00:17<23:07,  1.12s/it]
  1%|          | 14/1248 [00:18<22:34,  1.10s/it]
  1%|          | 15/1248 [00:19<23:11,  1.13s/it]
  1%|▏         | 16/1248 [00:20<23:45,  1.16s/it]
  1%|▏         | 17/1248 [00:21<22:57,  1.12s/it]
  1%|▏         | 18/1248 [00:22<23:21,  1.14s/it]
  2%|▏         | 19/1248 [00:24<23:47,  1.16s/it]
  2%|▏         | 20/1248 [00:25<22:56,  1.12s/it]
                                                 

  2%|▏         | 20/1248 [00:25<22:56,  1.12s/it]
  2%|▏         | 21/1248 [00:26<22:21,  1.09s/it]
  2%|▏         | 22/1248 [00:27<22:02,  1.08s/it]
  2%|▏         | 23/1248 [00:28<23:02,  1.13s/it]
  2%|▏         | 24/1248 [00:29<22:37,  1.11s/it]
  2%|▏         | 25/1248 [00:30<22:13,  1.09s/it]
  2%|▏         | 26/1248 [00:31<22:42,  1.12s/it]
  2%|▏         | 27/1248 [00:32<23:24,  1.15s/it]
  2%|▏         | 28/1248 [00:34<22:59,  1.13s/it]
  2%|▏         | 29/1248 [00:35<22:25,  1.10s/it]
  2%|▏         | 30/1248 [00:36<21:57,  1.08s/it]
                                                 

  2%|▏         | 30/1248 [00:36<21:57,  1.08s/it]
  2%|▏         | 31/1248 [00:37<22:49,  1.13s/it]
  3%|▎         | 32/1248 [00:38<22:19,  1.10s/it]
  3%|▎         | 33/1248 [00:39<22:41,  1.12s/it]
  3%|▎         | 34/1248 [00:40<23:12,  1.15s/it]
  3%|▎         | 35/1248 [00:41<22:32,  1.11s/it]
  3%|▎         | 36/1248 [00:42<22:06,  1.09s/it]
  3%|▎         | 37/1248 [00:43<21:53,  1.08s/it]
  3%|▎         | 38/1248 [00:45<22:51,  1.13s/it]
  3%|▎         | 39/1248 [00:46<22:16,  1.11s/it]
  3%|▎         | 40/1248 [00:47<21:53,  1.09s/it]
                                                 

  3%|▎         | 40/1248 [00:47<21:53,  1.09s/it]
  3%|▎         | 41/1248 [00:48<21:44,  1.08s/it]
  3%|▎         | 42/1248 [00:49<22:34,  1.12s/it]
  3%|▎         | 43/1248 [00:50<22:02,  1.10s/it]
  4%|▎         | 44/1248 [00:51<21:38,  1.08s/it]
  4%|▎         | 45/1248 [00:52<21:17,  1.06s/it]
  4%|▎         | 46/1248 [00:53<22:21,  1.12s/it]
  4%|▍         | 47/1248 [00:54<22:13,  1.11s/it]
  4%|▍         | 48/1248 [00:56<21:53,  1.09s/it]
  4%|▍         | 49/1248 [00:57<21:42,  1.09s/it]
  4%|▍         | 50/1248 [00:58<22:42,  1.14s/it]
                                                 

  4%|▍         | 50/1248 [00:58<22:42,  1.14s/it]
  4%|▍         | 51/1248 [00:59<22:19,  1.12s/it]
  4%|▍         | 52/1248 [01:00<21:52,  1.10s/it]
  4%|▍         | 53/1248 [01:01<21:37,  1.09s/it]
  4%|▍         | 54/1248 [01:02<22:37,  1.14s/it]
  4%|▍         | 55/1248 [01:03<22:04,  1.11s/it]
  4%|▍         | 56/1248 [01:04<21:52,  1.10s/it]
  5%|▍         | 57/1248 [01:05<21:50,  1.10s/it]
  5%|▍         | 58/1248 [01:07<22:24,  1.13s/it]
  5%|▍         | 59/1248 [01:08<22:04,  1.11s/it]
  5%|▍         | 60/1248 [01:09<21:40,  1.09s/it]
                                                 

  5%|▍         | 60/1248 [01:09<21:40,  1.09s/it]
  5%|▍         | 61/1248 [01:10<22:24,  1.13s/it]
  5%|▍         | 62/1248 [01:11<21:52,  1.11s/it]
  5%|▌         | 63/1248 [01:12<21:23,  1.08s/it]
  5%|▌         | 64/1248 [01:13<21:10,  1.07s/it]
  5%|▌         | 65/1248 [01:14<21:58,  1.11s/it]
  5%|▌         | 66/1248 [01:15<21:52,  1.11s/it]
  5%|▌         | 67/1248 [01:17<21:31,  1.09s/it]
  5%|▌         | 68/1248 [01:18<21:04,  1.07s/it]
  6%|▌         | 69/1248 [01:19<21:54,  1.11s/it]
  6%|▌         | 70/1248 [01:20<21:39,  1.10s/it]
                                                 

  6%|▌         | 70/1248 [01:20<21:39,  1.10s/it]
  6%|▌         | 71/1248 [01:21<21:19,  1.09s/it]
  6%|▌         | 72/1248 [01:22<21:03,  1.07s/it]
  6%|▌         | 73/1248 [01:23<21:49,  1.11s/it]
  6%|▌         | 74/1248 [01:24<21:29,  1.10s/it]
  6%|▌         | 75/1248 [01:25<21:10,  1.08s/it]
  6%|▌         | 76/1248 [01:27<22:05,  1.13s/it]
  6%|▌         | 77/1248 [01:28<21:33,  1.10s/it]
  6%|▋         | 78/1248 [01:29<21:10,  1.09s/it]
  6%|▋         | 79/1248 [01:30<20:49,  1.07s/it]
  6%|▋         | 80/1248 [01:31<21:42,  1.11s/it]
                                                 

  6%|▋         | 80/1248 [01:31<21:42,  1.11s/it]
  6%|▋         | 81/1248 [01:32<21:18,  1.10s/it]
  7%|▋         | 82/1248 [01:33<21:00,  1.08s/it]
  7%|▋         | 83/1248 [01:34<20:55,  1.08s/it]
  7%|▋         | 84/1248 [01:35<21:40,  1.12s/it]
  7%|▋         | 85/1248 [01:36<21:14,  1.10s/it]
  7%|▋         | 86/1248 [01:37<21:13,  1.10s/it]
  7%|▋         | 87/1248 [01:38<21:14,  1.10s/it]
  7%|▋         | 88/1248 [01:40<21:55,  1.13s/it]
  7%|▋         | 89/1248 [01:41<21:45,  1.13s/it]
  7%|▋         | 90/1248 [01:42<21:41,  1.12s/it]
                                                 

  7%|▋         | 90/1248 [01:42<21:41,  1.12s/it]
  7%|▋         | 91/1248 [01:43<21:13,  1.10s/it]
  7%|▋         | 92/1248 [01:44<21:49,  1.13s/it]
  7%|▋         | 93/1248 [01:45<21:27,  1.11s/it]
  8%|▊         | 94/1248 [01:46<21:03,  1.09s/it]
  8%|▊         | 95/1248 [01:47<21:41,  1.13s/it]
  8%|▊         | 96/1248 [01:49<21:28,  1.12s/it]
  8%|▊         | 97/1248 [01:50<21:03,  1.10s/it]
  8%|▊         | 98/1248 [01:51<20:39,  1.08s/it]
  8%|▊         | 99/1248 [01:52<21:35,  1.13s/it]
  8%|▊         | 100/1248 [01:53<21:05,  1.10s/it]
                                                  

  8%|▊         | 100/1248 [01:53<21:05,  1.10s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

  8%|▊         | 101/1248 [01:55<28:07,  1.47s/it]
  8%|▊         | 102/1248 [01:56<25:40,  1.34s/it]
  8%|▊         | 103/1248 [01:58<24:51,  1.30s/it]
  8%|▊         | 104/1248 [01:59<23:20,  1.22s/it]
  8%|▊         | 105/1248 [02:00<22:16,  1.17s/it]
  8%|▊         | 106/1248 [02:01<21:29,  1.13s/it]
  9%|▊         | 107/1248 [02:02<22:09,  1.17s/it]
  9%|▊         | 108/1248 [02:03<21:34,  1.14s/it]
  9%|▊         | 109/1248 [02:04<21:02,  1.11s/it]
  9%|▉         | 110/1248 [02:05<21:42,  1.14s/it]
                                                  

  9%|▉         | 110/1248 [02:05<21:42,  1.14s/it]
  9%|▉         | 111/1248 [02:06<21:26,  1.13s/it]
  9%|▉         | 112/1248 [02:07<20:57,  1.11s/it]
  9%|▉         | 113/1248 [02:09<21:37,  1.14s/it]
  9%|▉         | 114/1248 [02:10<22:06,  1.17s/it]
  9%|▉         | 115/1248 [02:11<21:30,  1.14s/it]
  9%|▉         | 116/1248 [02:12<20:58,  1.11s/it]
  9%|▉         | 117/1248 [02:13<20:32,  1.09s/it]
  9%|▉         | 118/1248 [02:14<21:41,  1.15s/it]
 10%|▉         | 119/1248 [02:15<21:20,  1.13s/it]
 10%|▉         | 120/1248 [02:16<20:58,  1.12s/it]
                                                  

 10%|▉         | 120/1248 [02:16<20:58,  1.12s/it]
 10%|▉         | 121/1248 [02:18<20:34,  1.09s/it]
 10%|▉         | 122/1248 [02:19<21:17,  1.13s/it]
 10%|▉         | 123/1248 [02:20<20:45,  1.11s/it]
 10%|▉         | 124/1248 [02:21<20:25,  1.09s/it]
 10%|█         | 125/1248 [02:22<20:16,  1.08s/it]
 10%|█         | 126/1248 [02:23<20:58,  1.12s/it]
 10%|█         | 127/1248 [02:24<20:52,  1.12s/it]
 10%|█         | 128/1248 [02:25<20:30,  1.10s/it]
 10%|█         | 129/1248 [02:26<20:58,  1.12s/it]
 10%|█         | 130/1248 [02:28<21:25,  1.15s/it]
                                                  

 10%|█         | 130/1248 [02:28<21:25,  1.15s/it]
 10%|█         | 131/1248 [02:29<20:50,  1.12s/it]
 11%|█         | 132/1248 [02:30<20:25,  1.10s/it]
 11%|█         | 133/1248 [02:31<21:02,  1.13s/it]
 11%|█         | 134/1248 [02:32<20:29,  1.10s/it]
 11%|█         | 135/1248 [02:33<20:13,  1.09s/it]
 11%|█         | 136/1248 [02:34<20:00,  1.08s/it]
 11%|█         | 137/1248 [02:35<20:43,  1.12s/it]
 11%|█         | 138/1248 [02:36<20:19,  1.10s/it]
 11%|█         | 139/1248 [02:37<20:05,  1.09s/it]
 11%|█         | 140/1248 [02:39<19:54,  1.08s/it]
                                                  

 11%|█         | 140/1248 [02:39<19:54,  1.08s/it]
 11%|█▏        | 141/1248 [02:40<20:42,  1.12s/it]
 11%|█▏        | 142/1248 [02:41<20:14,  1.10s/it]
 11%|█▏        | 143/1248 [02:42<19:56,  1.08s/it]
 12%|█▏        | 144/1248 [02:43<20:57,  1.14s/it]
 12%|█▏        | 145/1248 [02:44<20:21,  1.11s/it]
 12%|█▏        | 146/1248 [02:45<20:00,  1.09s/it]
 12%|█▏        | 147/1248 [02:46<19:52,  1.08s/it]
 12%|█▏        | 148/1248 [02:48<21:21,  1.16s/it]
 12%|█▏        | 149/1248 [02:49<20:38,  1.13s/it]
 12%|█▏        | 150/1248 [02:50<20:05,  1.10s/it]
                                                  

 12%|█▏        | 150/1248 [02:50<20:05,  1.10s/it]
 12%|█▏        | 151/1248 [02:51<20:06,  1.10s/it]
 12%|█▏        | 152/1248 [02:52<21:06,  1.16s/it]
 12%|█▏        | 153/1248 [02:53<20:28,  1.12s/it]
 12%|█▏        | 154/1248 [02:54<19:58,  1.10s/it]
 12%|█▏        | 155/1248 [02:55<19:45,  1.09s/it]
 12%|█▎        | 156/1248 [02:56<20:34,  1.13s/it]
 13%|█▎        | 157/1248 [02:57<20:08,  1.11s/it]
 13%|█▎        | 158/1248 [02:59<20:08,  1.11s/it]
 13%|█▎        | 159/1248 [03:00<19:46,  1.09s/it]
 13%|█▎        | 160/1248 [03:01<20:32,  1.13s/it]
                                                  

 13%|█▎        | 160/1248 [03:01<20:32,  1.13s/it]
 13%|█▎        | 161/1248 [03:02<20:16,  1.12s/it]
 13%|█▎        | 162/1248 [03:03<19:51,  1.10s/it]
 13%|█▎        | 163/1248 [03:04<20:52,  1.15s/it]
 13%|█▎        | 164/1248 [03:05<20:25,  1.13s/it]
 13%|█▎        | 165/1248 [03:06<19:59,  1.11s/it]
 13%|█▎        | 166/1248 [03:08<19:49,  1.10s/it]
 13%|█▎        | 167/1248 [03:09<20:28,  1.14s/it]
 13%|█▎        | 168/1248 [03:10<19:59,  1.11s/it]
 14%|█▎        | 169/1248 [03:11<19:42,  1.10s/it]
 14%|█▎        | 170/1248 [03:12<19:27,  1.08s/it]
                                                  

 14%|█▎        | 170/1248 [03:12<19:27,  1.08s/it]
 14%|█▎        | 171/1248 [03:13<20:07,  1.12s/it]
 14%|█▍        | 172/1248 [03:14<19:40,  1.10s/it]
 14%|█▍        | 173/1248 [03:15<19:17,  1.08s/it]
 14%|█▍        | 174/1248 [03:16<20:08,  1.12s/it]
 14%|█▍        | 175/1248 [03:17<19:46,  1.11s/it]
 14%|█▍        | 176/1248 [03:19<19:31,  1.09s/it]
 14%|█▍        | 177/1248 [03:20<19:14,  1.08s/it]
 14%|█▍        | 178/1248 [03:21<20:11,  1.13s/it]
 14%|█▍        | 179/1248 [03:22<19:42,  1.11s/it]
 14%|█▍        | 180/1248 [03:23<19:32,  1.10s/it]
                                                  

 14%|█▍        | 180/1248 [03:23<19:32,  1.10s/it]
 15%|█▍        | 181/1248 [03:24<19:19,  1.09s/it]
 15%|█▍        | 182/1248 [03:25<20:14,  1.14s/it]
 15%|█▍        | 183/1248 [03:26<19:45,  1.11s/it]
 15%|█▍        | 184/1248 [03:27<19:22,  1.09s/it]
 15%|█▍        | 185/1248 [03:29<20:07,  1.14s/it]
 15%|█▍        | 186/1248 [03:30<19:35,  1.11s/it]
 15%|█▍        | 187/1248 [03:31<19:17,  1.09s/it]
 15%|█▌        | 188/1248 [03:32<19:05,  1.08s/it]
 15%|█▌        | 189/1248 [03:33<19:52,  1.13s/it]
 15%|█▌        | 190/1248 [03:34<19:29,  1.11s/it]
                                                  

 15%|█▌        | 190/1248 [03:34<19:29,  1.11s/it]
 15%|█▌        | 191/1248 [03:35<19:15,  1.09s/it]
 15%|█▌        | 192/1248 [03:36<18:58,  1.08s/it]
 15%|█▌        | 193/1248 [03:37<19:38,  1.12s/it]
 16%|█▌        | 194/1248 [03:38<19:31,  1.11s/it]
 16%|█▌        | 195/1248 [03:40<19:19,  1.10s/it]
 16%|█▌        | 196/1248 [03:41<19:02,  1.09s/it]
 16%|█▌        | 197/1248 [03:42<19:57,  1.14s/it]
 16%|█▌        | 198/1248 [03:43<19:36,  1.12s/it]
 16%|█▌        | 199/1248 [03:44<19:16,  1.10s/it]
 16%|█▌        | 200/1248 [03:45<19:02,  1.09s/it]
                                                  

 16%|█▌        | 200/1248 [03:45<19:02,  1.09s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 16%|█▌        | 201/1248 [03:47<25:55,  1.49s/it]
 16%|█▌        | 202/1248 [03:49<23:37,  1.36s/it]
 16%|█▋        | 203/1248 [03:50<22:02,  1.27s/it]
 16%|█▋        | 204/1248 [03:51<20:46,  1.19s/it]
 16%|█▋        | 205/1248 [03:52<21:33,  1.24s/it]
 17%|█▋        | 206/1248 [03:53<20:31,  1.18s/it]
 17%|█▋        | 207/1248 [03:54<19:45,  1.14s/it]
 17%|█▋        | 208/1248 [03:55<20:10,  1.16s/it]
 17%|█▋        | 209/1248 [03:56<19:43,  1.14s/it]
 17%|█▋        | 210/1248 [03:57<19:08,  1.11s/it]
                                                  

 17%|█▋        | 210/1248 [03:57<19:08,  1.11s/it]
 17%|█▋        | 211/1248 [03:58<18:53,  1.09s/it]
 17%|█▋        | 212/1248 [04:00<19:37,  1.14s/it]
 17%|█▋        | 213/1248 [04:01<19:13,  1.11s/it]
 17%|█▋        | 214/1248 [04:02<18:44,  1.09s/it]
 17%|█▋        | 215/1248 [04:03<18:39,  1.08s/it]
 17%|█▋        | 216/1248 [04:04<19:26,  1.13s/it]
 17%|█▋        | 217/1248 [04:05<18:55,  1.10s/it]
 17%|█▋        | 218/1248 [04:06<18:43,  1.09s/it]
 18%|█▊        | 219/1248 [04:07<18:28,  1.08s/it]
 18%|█▊        | 220/1248 [04:08<19:21,  1.13s/it]
                                                  

 18%|█▊        | 220/1248 [04:08<19:21,  1.13s/it]
 18%|█▊        | 221/1248 [04:10<18:55,  1.11s/it]
 18%|█▊        | 222/1248 [04:11<18:52,  1.10s/it]
 18%|█▊        | 223/1248 [04:12<18:34,  1.09s/it]
 18%|█▊        | 224/1248 [04:13<19:14,  1.13s/it]
 18%|█▊        | 225/1248 [04:14<18:52,  1.11s/it]
 18%|█▊        | 226/1248 [04:15<18:42,  1.10s/it]
 18%|█▊        | 227/1248 [04:16<19:17,  1.13s/it]
 18%|█▊        | 228/1248 [04:17<18:53,  1.11s/it]
 18%|█▊        | 229/1248 [04:18<18:52,  1.11s/it]
 18%|█▊        | 230/1248 [04:19<18:35,  1.10s/it]
                                                  

 18%|█▊        | 230/1248 [04:19<18:35,  1.10s/it]
 19%|█▊        | 231/1248 [04:21<19:19,  1.14s/it]
 19%|█▊        | 232/1248 [04:22<18:52,  1.11s/it]
 19%|█▊        | 233/1248 [04:23<18:40,  1.10s/it]
 19%|█▉        | 234/1248 [04:24<19:08,  1.13s/it]
 19%|█▉        | 235/1248 [04:25<20:13,  1.20s/it]
 19%|█▉        | 236/1248 [04:26<19:26,  1.15s/it]
 19%|█▉        | 237/1248 [04:27<18:52,  1.12s/it]
 19%|█▉        | 238/1248 [04:29<18:36,  1.11s/it]
 19%|█▉        | 239/1248 [04:30<19:13,  1.14s/it]
 19%|█▉        | 240/1248 [04:31<18:49,  1.12s/it]
                                                  

 19%|█▉        | 240/1248 [04:31<18:49,  1.12s/it]
 19%|█▉        | 241/1248 [04:32<18:32,  1.11s/it]
 19%|█▉        | 242/1248 [04:33<18:42,  1.12s/it]
 19%|█▉        | 243/1248 [04:34<19:28,  1.16s/it]
 20%|█▉        | 244/1248 [04:35<18:54,  1.13s/it]
 20%|█▉        | 245/1248 [04:36<18:38,  1.11s/it]
 20%|█▉        | 246/1248 [04:38<19:29,  1.17s/it]
 20%|█▉        | 247/1248 [04:39<18:53,  1.13s/it]
 20%|█▉        | 248/1248 [04:40<18:27,  1.11s/it]
 20%|█▉        | 249/1248 [04:41<18:23,  1.10s/it]
 20%|██        | 250/1248 [04:42<18:55,  1.14s/it]
                                                  

 20%|██        | 250/1248 [04:42<18:55,  1.14s/it]
 20%|██        | 251/1248 [04:43<18:23,  1.11s/it]
 20%|██        | 252/1248 [04:44<18:07,  1.09s/it]
 20%|██        | 253/1248 [04:45<17:56,  1.08s/it]
 20%|██        | 254/1248 [04:47<18:33,  1.12s/it]
 20%|██        | 255/1248 [04:48<18:03,  1.09s/it]
 21%|██        | 256/1248 [04:49<17:49,  1.08s/it]
 21%|██        | 257/1248 [04:50<17:33,  1.06s/it]
 21%|██        | 258/1248 [04:51<18:21,  1.11s/it]
 21%|██        | 259/1248 [04:52<18:08,  1.10s/it]
 21%|██        | 260/1248 [04:53<17:50,  1.08s/it]
                                                  

 21%|██        | 260/1248 [04:53<17:50,  1.08s/it]
 21%|██        | 261/1248 [04:54<18:33,  1.13s/it]
 21%|██        | 262/1248 [04:55<18:09,  1.10s/it]
 21%|██        | 263/1248 [04:56<17:51,  1.09s/it]
 21%|██        | 264/1248 [04:57<17:32,  1.07s/it]
 21%|██        | 265/1248 [04:59<18:15,  1.11s/it]
 21%|██▏       | 266/1248 [05:00<17:56,  1.10s/it]
 21%|██▏       | 267/1248 [05:01<18:12,  1.11s/it]
 21%|██▏       | 268/1248 [05:02<18:00,  1.10s/it]
 22%|██▏       | 269/1248 [05:03<18:44,  1.15s/it]
 22%|██▏       | 270/1248 [05:04<18:24,  1.13s/it]
                                                  

 22%|██▏       | 270/1248 [05:04<18:24,  1.13s/it]
 22%|██▏       | 271/1248 [05:05<17:58,  1.10s/it]
 22%|██▏       | 272/1248 [05:06<17:37,  1.08s/it]
 22%|██▏       | 273/1248 [05:07<18:16,  1.13s/it]
 22%|██▏       | 274/1248 [05:09<17:53,  1.10s/it]
 22%|██▏       | 275/1248 [05:10<18:09,  1.12s/it]
 22%|██▏       | 276/1248 [05:11<18:11,  1.12s/it]
 22%|██▏       | 277/1248 [05:12<18:44,  1.16s/it]
 22%|██▏       | 278/1248 [05:13<18:07,  1.12s/it]
 22%|██▏       | 279/1248 [05:14<17:40,  1.09s/it]
 22%|██▏       | 280/1248 [05:15<17:22,  1.08s/it]
                                                  

 22%|██▏       | 280/1248 [05:15<17:22,  1.08s/it]
 23%|██▎       | 281/1248 [05:16<18:04,  1.12s/it]
 23%|██▎       | 282/1248 [05:17<17:39,  1.10s/it]
 23%|██▎       | 283/1248 [05:18<17:26,  1.08s/it]
 23%|██▎       | 284/1248 [05:20<18:02,  1.12s/it]
 23%|██▎       | 285/1248 [05:21<17:42,  1.10s/it]
 23%|██▎       | 286/1248 [05:22<17:25,  1.09s/it]
 23%|██▎       | 287/1248 [05:23<17:41,  1.10s/it]
 23%|██▎       | 288/1248 [05:24<18:20,  1.15s/it]
 23%|██▎       | 289/1248 [05:25<17:52,  1.12s/it]
 23%|██▎       | 290/1248 [05:26<17:29,  1.10s/it]
                                                  

 23%|██▎       | 290/1248 [05:26<17:29,  1.10s/it]
 23%|██▎       | 291/1248 [05:27<17:47,  1.12s/it]
 23%|██▎       | 292/1248 [05:29<18:40,  1.17s/it]
 23%|██▎       | 293/1248 [05:30<18:17,  1.15s/it]
 24%|██▎       | 294/1248 [05:31<17:51,  1.12s/it]
 24%|██▎       | 295/1248 [05:32<17:27,  1.10s/it]
 24%|██▎       | 296/1248 [05:33<18:17,  1.15s/it]
 24%|██▍       | 297/1248 [05:34<17:44,  1.12s/it]
 24%|██▍       | 298/1248 [05:35<17:22,  1.10s/it]
 24%|██▍       | 299/1248 [05:37<17:54,  1.13s/it]
 24%|██▍       | 300/1248 [05:38<17:32,  1.11s/it]
                                                  

 24%|██▍       | 300/1248 [05:38<17:32,  1.11s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 24%|██▍       | 301/1248 [05:40<23:26,  1.49s/it]
 24%|██▍       | 302/1248 [05:41<21:21,  1.35s/it]
 24%|██▍       | 303/1248 [05:42<20:41,  1.31s/it]
 24%|██▍       | 304/1248 [05:43<19:23,  1.23s/it]
 24%|██▍       | 305/1248 [05:44<18:36,  1.18s/it]
 25%|██▍       | 306/1248 [05:45<17:55,  1.14s/it]
 25%|██▍       | 307/1248 [05:47<18:47,  1.20s/it]
 25%|██▍       | 308/1248 [05:48<18:00,  1.15s/it]
 25%|██▍       | 309/1248 [05:49<17:35,  1.12s/it]
 25%|██▍       | 310/1248 [05:50<17:12,  1.10s/it]
                                                  

 25%|██▍       | 310/1248 [05:50<17:12,  1.10s/it]
 25%|██▍       | 311/1248 [05:51<17:41,  1.13s/it]
 25%|██▌       | 312/1248 [05:52<17:14,  1.10s/it]
 25%|██▌       | 313/1248 [05:53<16:57,  1.09s/it]
 25%|██▌       | 314/1248 [05:54<17:52,  1.15s/it]
 25%|██▌       | 315/1248 [05:55<17:25,  1.12s/it]
 25%|██▌       | 316/1248 [05:57<17:03,  1.10s/it]
 25%|██▌       | 317/1248 [05:58<16:43,  1.08s/it]
 25%|██▌       | 318/1248 [05:59<17:35,  1.14s/it]
 26%|██▌       | 319/1248 [06:00<17:10,  1.11s/it]
 26%|██▌       | 320/1248 [06:01<17:00,  1.10s/it]
                                                  

 26%|██▌       | 320/1248 [06:01<17:00,  1.10s/it]
 26%|██▌       | 321/1248 [06:02<16:36,  1.07s/it]
 26%|██▌       | 322/1248 [06:03<17:51,  1.16s/it]
 26%|██▌       | 323/1248 [06:04<17:25,  1.13s/it]
 26%|██▌       | 324/1248 [06:05<16:59,  1.10s/it]
 26%|██▌       | 325/1248 [06:07<16:53,  1.10s/it]
 26%|██▌       | 326/1248 [06:08<17:26,  1.13s/it]
 26%|██▌       | 327/1248 [06:09<17:01,  1.11s/it]
 26%|██▋       | 328/1248 [06:10<16:43,  1.09s/it]
 26%|██▋       | 329/1248 [06:11<16:38,  1.09s/it]
 26%|██▋       | 330/1248 [06:12<17:13,  1.13s/it]
                                                  

 26%|██▋       | 330/1248 [06:12<17:13,  1.13s/it]
 27%|██▋       | 331/1248 [06:13<16:54,  1.11s/it]
 27%|██▋       | 332/1248 [06:14<16:51,  1.10s/it]
 27%|██▋       | 333/1248 [06:16<17:24,  1.14s/it]
 27%|██▋       | 334/1248 [06:17<16:55,  1.11s/it]
 27%|██▋       | 335/1248 [06:18<16:30,  1.08s/it]
 27%|██▋       | 336/1248 [06:19<16:22,  1.08s/it]
 27%|██▋       | 337/1248 [06:20<16:59,  1.12s/it]
 27%|██▋       | 338/1248 [06:21<16:39,  1.10s/it]
 27%|██▋       | 339/1248 [06:22<16:21,  1.08s/it]
 27%|██▋       | 340/1248 [06:23<16:13,  1.07s/it]
                                                  

 27%|██▋       | 340/1248 [06:23<16:13,  1.07s/it]
 27%|██▋       | 341/1248 [06:24<16:54,  1.12s/it]
 27%|██▋       | 342/1248 [06:25<16:45,  1.11s/it]
 27%|██▋       | 343/1248 [06:26<16:28,  1.09s/it]
 28%|██▊       | 344/1248 [06:27<16:13,  1.08s/it]
 28%|██▊       | 345/1248 [06:29<17:12,  1.14s/it]
 28%|██▊       | 346/1248 [06:30<16:46,  1.12s/it]
 28%|██▊       | 347/1248 [06:31<16:31,  1.10s/it]
 28%|██▊       | 348/1248 [06:32<16:19,  1.09s/it]
 28%|██▊       | 349/1248 [06:33<16:49,  1.12s/it]
 28%|██▊       | 350/1248 [06:34<17:00,  1.14s/it]
                                                  

 28%|██▊       | 350/1248 [06:34<17:00,  1.14s/it]
 28%|██▊       | 351/1248 [06:35<16:35,  1.11s/it]
 28%|██▊       | 352/1248 [06:37<17:12,  1.15s/it]
 28%|██▊       | 353/1248 [06:38<16:42,  1.12s/it]
 28%|██▊       | 354/1248 [06:39<16:22,  1.10s/it]
 28%|██▊       | 355/1248 [06:40<16:02,  1.08s/it]
 29%|██▊       | 356/1248 [06:41<16:39,  1.12s/it]
 29%|██▊       | 357/1248 [06:42<16:17,  1.10s/it]
 29%|██▊       | 358/1248 [06:43<16:05,  1.09s/it]
 29%|██▉       | 359/1248 [06:44<15:54,  1.07s/it]
 29%|██▉       | 360/1248 [06:45<16:32,  1.12s/it]
                                                  

 29%|██▉       | 360/1248 [06:45<16:32,  1.12s/it]
 29%|██▉       | 361/1248 [06:46<16:13,  1.10s/it]
 29%|██▉       | 362/1248 [06:47<16:03,  1.09s/it]
 29%|██▉       | 363/1248 [06:48<15:55,  1.08s/it]
 29%|██▉       | 364/1248 [06:50<16:29,  1.12s/it]
 29%|██▉       | 365/1248 [06:51<16:05,  1.09s/it]
 29%|██▉       | 366/1248 [06:52<15:54,  1.08s/it]
 29%|██▉       | 367/1248 [06:53<16:28,  1.12s/it]
 29%|██▉       | 368/1248 [06:54<16:13,  1.11s/it]
 30%|██▉       | 369/1248 [06:55<15:59,  1.09s/it]
 30%|██▉       | 370/1248 [06:56<15:43,  1.07s/it]
                                                  

 30%|██▉       | 370/1248 [06:56<15:43,  1.07s/it]
 30%|██▉       | 371/1248 [06:57<16:23,  1.12s/it]
 30%|██▉       | 372/1248 [06:58<16:04,  1.10s/it]
 30%|██▉       | 373/1248 [06:59<15:55,  1.09s/it]
 30%|██▉       | 374/1248 [07:01<15:55,  1.09s/it]
 30%|███       | 375/1248 [07:02<16:34,  1.14s/it]
 30%|███       | 376/1248 [07:03<16:09,  1.11s/it]
 30%|███       | 377/1248 [07:04<15:55,  1.10s/it]
 30%|███       | 378/1248 [07:05<15:43,  1.08s/it]
 30%|███       | 379/1248 [07:06<16:30,  1.14s/it]
 30%|███       | 380/1248 [07:07<16:44,  1.16s/it]
                                                  

 30%|███       | 380/1248 [07:07<16:44,  1.16s/it]
 31%|███       | 381/1248 [07:09<16:16,  1.13s/it]
 31%|███       | 382/1248 [07:10<16:00,  1.11s/it]
 31%|███       | 383/1248 [07:11<16:32,  1.15s/it]
 31%|███       | 384/1248 [07:12<16:03,  1.12s/it]
 31%|███       | 385/1248 [07:13<15:55,  1.11s/it]
 31%|███       | 386/1248 [07:14<15:39,  1.09s/it]
 31%|███       | 387/1248 [07:15<16:14,  1.13s/it]
 31%|███       | 388/1248 [07:16<15:49,  1.10s/it]
 31%|███       | 389/1248 [07:17<15:35,  1.09s/it]
 31%|███▏      | 390/1248 [07:19<16:36,  1.16s/it]
                                                  

 31%|███▏      | 390/1248 [07:19<16:36,  1.16s/it]
 31%|███▏      | 391/1248 [07:20<16:04,  1.13s/it]
 31%|███▏      | 392/1248 [07:21<15:39,  1.10s/it]
 31%|███▏      | 393/1248 [07:22<15:21,  1.08s/it]
 32%|███▏      | 394/1248 [07:23<16:07,  1.13s/it]
 32%|███▏      | 395/1248 [07:24<15:53,  1.12s/it]
 32%|███▏      | 396/1248 [07:25<15:51,  1.12s/it]
 32%|███▏      | 397/1248 [07:26<15:36,  1.10s/it]
 32%|███▏      | 398/1248 [07:28<16:21,  1.16s/it]
 32%|███▏      | 399/1248 [07:29<15:57,  1.13s/it]
 32%|███▏      | 400/1248 [07:30<15:32,  1.10s/it]
                                                  

 32%|███▏      | 400/1248 [07:30<15:32,  1.10s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 32%|███▏      | 401/1248 [07:32<20:48,  1.47s/it]
 32%|███▏      | 402/1248 [07:33<19:52,  1.41s/it]
 32%|███▏      | 403/1248 [07:34<18:16,  1.30s/it]
 32%|███▏      | 404/1248 [07:35<17:12,  1.22s/it]
 32%|███▏      | 405/1248 [07:36<16:31,  1.18s/it]
 33%|███▎      | 406/1248 [07:38<16:36,  1.18s/it]
 33%|███▎      | 407/1248 [07:39<16:14,  1.16s/it]
 33%|███▎      | 408/1248 [07:40<15:47,  1.13s/it]
 33%|███▎      | 409/1248 [07:41<16:42,  1.20s/it]
 33%|███▎      | 410/1248 [07:42<16:01,  1.15s/it]
                                                  

 33%|███▎      | 410/1248 [07:42<16:01,  1.15s/it]
 33%|███▎      | 411/1248 [07:43<15:36,  1.12s/it]
 33%|███▎      | 412/1248 [07:44<15:18,  1.10s/it]
 33%|███▎      | 413/1248 [07:46<15:53,  1.14s/it]
 33%|███▎      | 414/1248 [07:47<15:28,  1.11s/it]
 33%|███▎      | 415/1248 [07:48<15:35,  1.12s/it]
 33%|███▎      | 416/1248 [07:49<15:17,  1.10s/it]
 33%|███▎      | 417/1248 [07:50<15:50,  1.14s/it]
 33%|███▎      | 418/1248 [07:51<15:29,  1.12s/it]
 34%|███▎      | 419/1248 [07:52<15:36,  1.13s/it]
 34%|███▎      | 420/1248 [07:53<15:16,  1.11s/it]
                                                  

 34%|███▎      | 420/1248 [07:53<15:16,  1.11s/it]
 34%|███▎      | 421/1248 [07:55<15:52,  1.15s/it]
 34%|███▍      | 422/1248 [07:56<15:30,  1.13s/it]
 34%|███▍      | 423/1248 [07:57<15:09,  1.10s/it]
 34%|███▍      | 424/1248 [07:58<15:24,  1.12s/it]
 34%|███▍      | 425/1248 [07:59<15:48,  1.15s/it]
 34%|███▍      | 426/1248 [08:00<15:18,  1.12s/it]
 34%|███▍      | 427/1248 [08:01<15:07,  1.10s/it]
 34%|███▍      | 428/1248 [08:02<14:54,  1.09s/it]
 34%|███▍      | 429/1248 [08:03<15:37,  1.14s/it]
 34%|███▍      | 430/1248 [08:05<15:13,  1.12s/it]
                                                  

 34%|███▍      | 430/1248 [08:05<15:13,  1.12s/it]
 35%|███▍      | 431/1248 [08:06<14:59,  1.10s/it]
 35%|███▍      | 432/1248 [08:07<15:35,  1.15s/it]
 35%|███▍      | 433/1248 [08:08<15:09,  1.12s/it]
 35%|███▍      | 434/1248 [08:09<15:02,  1.11s/it]
 35%|███▍      | 435/1248 [08:10<14:46,  1.09s/it]
 35%|███▍      | 436/1248 [08:11<15:33,  1.15s/it]
 35%|███▌      | 437/1248 [08:12<15:10,  1.12s/it]
 35%|███▌      | 438/1248 [08:13<14:55,  1.11s/it]
 35%|███▌      | 439/1248 [08:14<14:42,  1.09s/it]
 35%|███▌      | 440/1248 [08:16<15:18,  1.14s/it]
                                                  

 35%|███▌      | 440/1248 [08:16<15:18,  1.14s/it]
 35%|███▌      | 441/1248 [08:17<14:54,  1.11s/it]
 35%|███▌      | 442/1248 [08:18<14:40,  1.09s/it]
 35%|███▌      | 443/1248 [08:19<15:02,  1.12s/it]
 36%|███▌      | 444/1248 [08:20<15:22,  1.15s/it]
 36%|███▌      | 445/1248 [08:21<14:57,  1.12s/it]
 36%|███▌      | 446/1248 [08:22<14:44,  1.10s/it]
 36%|███▌      | 447/1248 [08:23<14:37,  1.10s/it]
 36%|███▌      | 448/1248 [08:25<15:01,  1.13s/it]
 36%|███▌      | 449/1248 [08:26<14:38,  1.10s/it]
 36%|███▌      | 450/1248 [08:27<14:39,  1.10s/it]
                                                  

 36%|███▌      | 450/1248 [08:27<14:39,  1.10s/it]
 36%|███▌      | 451/1248 [08:28<15:11,  1.14s/it]
 36%|███▌      | 452/1248 [08:29<14:45,  1.11s/it]
 36%|███▋      | 453/1248 [08:30<14:26,  1.09s/it]
 36%|███▋      | 454/1248 [08:31<14:13,  1.07s/it]
 36%|███▋      | 455/1248 [08:32<15:15,  1.16s/it]
 37%|███▋      | 456/1248 [08:34<14:47,  1.12s/it]
 37%|███▋      | 457/1248 [08:35<14:56,  1.13s/it]
 37%|███▋      | 458/1248 [08:36<14:33,  1.11s/it]
 37%|███▋      | 459/1248 [08:37<15:03,  1.15s/it]
 37%|███▋      | 460/1248 [08:38<14:52,  1.13s/it]
                                                  

 37%|███▋      | 460/1248 [08:38<14:52,  1.13s/it]
 37%|███▋      | 461/1248 [08:39<14:29,  1.10s/it]
 37%|███▋      | 462/1248 [08:40<14:21,  1.10s/it]
 37%|███▋      | 463/1248 [08:41<14:49,  1.13s/it]
 37%|███▋      | 464/1248 [08:42<14:33,  1.11s/it]
 37%|███▋      | 465/1248 [08:44<14:20,  1.10s/it]
 37%|███▋      | 466/1248 [08:45<14:06,  1.08s/it]
 37%|███▋      | 467/1248 [08:46<14:36,  1.12s/it]
 38%|███▊      | 468/1248 [08:47<14:15,  1.10s/it]
 38%|███▊      | 469/1248 [08:48<14:05,  1.09s/it]
 38%|███▊      | 470/1248 [08:49<13:59,  1.08s/it]
                                                  

 38%|███▊      | 470/1248 [08:49<13:59,  1.08s/it]
 38%|███▊      | 471/1248 [08:50<14:35,  1.13s/it]
 38%|███▊      | 472/1248 [08:51<14:16,  1.10s/it]
 38%|███▊      | 473/1248 [08:52<14:01,  1.09s/it]
 38%|███▊      | 474/1248 [08:53<14:00,  1.09s/it]
 38%|███▊      | 475/1248 [08:55<14:42,  1.14s/it]
 38%|███▊      | 476/1248 [08:56<14:14,  1.11s/it]
 38%|███▊      | 477/1248 [08:57<13:55,  1.08s/it]
 38%|███▊      | 478/1248 [08:58<14:28,  1.13s/it]
 38%|███▊      | 479/1248 [08:59<14:11,  1.11s/it]
 38%|███▊      | 480/1248 [09:00<14:16,  1.11s/it]
                                                  

 38%|███▊      | 480/1248 [09:00<14:16,  1.11s/it]
 39%|███▊      | 481/1248 [09:01<14:02,  1.10s/it]
 39%|███▊      | 482/1248 [09:02<14:24,  1.13s/it]
 39%|███▊      | 483/1248 [09:03<14:05,  1.10s/it]
 39%|███▉      | 484/1248 [09:04<13:55,  1.09s/it]
 39%|███▉      | 485/1248 [09:06<13:46,  1.08s/it]
 39%|███▉      | 486/1248 [09:07<14:17,  1.13s/it]
 39%|███▉      | 487/1248 [09:08<13:58,  1.10s/it]
 39%|███▉      | 488/1248 [09:09<13:44,  1.08s/it]
 39%|███▉      | 489/1248 [09:10<13:43,  1.08s/it]
 39%|███▉      | 490/1248 [09:11<14:13,  1.13s/it]
                                                  

 39%|███▉      | 490/1248 [09:11<14:13,  1.13s/it]
 39%|███▉      | 491/1248 [09:12<14:00,  1.11s/it]
 39%|███▉      | 492/1248 [09:13<13:44,  1.09s/it]
 40%|███▉      | 493/1248 [09:14<13:32,  1.08s/it]
 40%|███▉      | 494/1248 [09:16<14:09,  1.13s/it]
 40%|███▉      | 495/1248 [09:17<13:47,  1.10s/it]
 40%|███▉      | 496/1248 [09:18<13:32,  1.08s/it]
 40%|███▉      | 497/1248 [09:19<14:04,  1.12s/it]
 40%|███▉      | 498/1248 [09:20<13:49,  1.11s/it]
 40%|███▉      | 499/1248 [09:21<13:43,  1.10s/it]
 40%|████      | 500/1248 [09:22<13:39,  1.10s/it]
                                                  

 40%|████      | 500/1248 [09:22<13:39,  1.10s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 40%|████      | 501/1248 [09:25<18:44,  1.51s/it]
 40%|████      | 502/1248 [09:26<16:56,  1.36s/it]
 40%|████      | 503/1248 [09:27<15:43,  1.27s/it]
 40%|████      | 504/1248 [09:28<14:56,  1.21s/it]
 40%|████      | 505/1248 [09:29<14:58,  1.21s/it]
 41%|████      | 506/1248 [09:30<14:28,  1.17s/it]
 41%|████      | 507/1248 [09:31<13:58,  1.13s/it]
 41%|████      | 508/1248 [09:32<13:38,  1.11s/it]
 41%|████      | 509/1248 [09:33<14:11,  1.15s/it]
 41%|████      | 510/1248 [09:34<13:47,  1.12s/it]
                                                  

 41%|████      | 510/1248 [09:34<13:47,  1.12s/it]
 41%|████      | 511/1248 [09:35<13:33,  1.10s/it]
 41%|████      | 512/1248 [09:36<13:19,  1.09s/it]
 41%|████      | 513/1248 [09:38<13:55,  1.14s/it]
 41%|████      | 514/1248 [09:39<13:33,  1.11s/it]
 41%|████▏     | 515/1248 [09:40<13:21,  1.09s/it]
 41%|████▏     | 516/1248 [09:41<13:54,  1.14s/it]
 41%|████▏     | 517/1248 [09:42<13:22,  1.10s/it]
 42%|████▏     | 518/1248 [09:43<13:06,  1.08s/it]
 42%|████▏     | 519/1248 [09:44<12:56,  1.07s/it]
 42%|████▏     | 520/1248 [09:45<13:35,  1.12s/it]
                                                  

 42%|████▏     | 520/1248 [09:45<13:35,  1.12s/it]
 42%|████▏     | 521/1248 [09:47<13:31,  1.12s/it]
 42%|████▏     | 522/1248 [09:48<13:12,  1.09s/it]
 42%|████▏     | 523/1248 [09:49<13:01,  1.08s/it]
 42%|████▏     | 524/1248 [09:50<13:34,  1.12s/it]
 42%|████▏     | 525/1248 [09:51<13:13,  1.10s/it]
 42%|████▏     | 526/1248 [09:52<13:09,  1.09s/it]
 42%|████▏     | 527/1248 [09:53<13:04,  1.09s/it]
 42%|████▏     | 528/1248 [09:54<13:35,  1.13s/it]
 42%|████▏     | 529/1248 [09:55<13:24,  1.12s/it]
 42%|████▏     | 530/1248 [09:56<13:09,  1.10s/it]
                                                  

 42%|████▏     | 530/1248 [09:56<13:09,  1.10s/it]
 43%|████▎     | 531/1248 [09:57<12:53,  1.08s/it]
 43%|████▎     | 532/1248 [09:59<13:31,  1.13s/it]
 43%|████▎     | 533/1248 [10:00<13:12,  1.11s/it]
 43%|████▎     | 534/1248 [10:01<13:01,  1.09s/it]
 43%|████▎     | 535/1248 [10:02<12:53,  1.09s/it]
 43%|████▎     | 536/1248 [10:03<13:49,  1.16s/it]
 43%|████▎     | 537/1248 [10:04<13:32,  1.14s/it]
 43%|████▎     | 538/1248 [10:05<13:10,  1.11s/it]
 43%|████▎     | 539/1248 [10:07<13:31,  1.15s/it]
 43%|████▎     | 540/1248 [10:08<13:26,  1.14s/it]
                                                  

 43%|████▎     | 540/1248 [10:08<13:26,  1.14s/it]
 43%|████▎     | 541/1248 [10:09<13:31,  1.15s/it]
 43%|████▎     | 542/1248 [10:10<13:11,  1.12s/it]
 44%|████▎     | 543/1248 [10:11<13:30,  1.15s/it]
 44%|████▎     | 544/1248 [10:12<13:15,  1.13s/it]
 44%|████▎     | 545/1248 [10:13<13:00,  1.11s/it]
 44%|████▍     | 546/1248 [10:14<12:59,  1.11s/it]
 44%|████▍     | 547/1248 [10:16<13:19,  1.14s/it]
 44%|████▍     | 548/1248 [10:17<13:02,  1.12s/it]
 44%|████▍     | 549/1248 [10:18<12:50,  1.10s/it]
 44%|████▍     | 550/1248 [10:19<12:37,  1.08s/it]
                                                  

 44%|████▍     | 550/1248 [10:19<12:37,  1.08s/it]
 44%|████▍     | 551/1248 [10:20<13:08,  1.13s/it]
 44%|████▍     | 552/1248 [10:21<12:47,  1.10s/it]
 44%|████▍     | 553/1248 [10:22<12:33,  1.08s/it]
 44%|████▍     | 554/1248 [10:23<13:09,  1.14s/it]
 44%|████▍     | 555/1248 [10:24<12:50,  1.11s/it]
 45%|████▍     | 556/1248 [10:25<12:35,  1.09s/it]
 45%|████▍     | 557/1248 [10:27<12:28,  1.08s/it]
 45%|████▍     | 558/1248 [10:28<12:59,  1.13s/it]
 45%|████▍     | 559/1248 [10:29<12:45,  1.11s/it]
 45%|████▍     | 560/1248 [10:30<12:30,  1.09s/it]
                                                  

 45%|████▍     | 560/1248 [10:30<12:30,  1.09s/it]
 45%|████▍     | 561/1248 [10:31<12:21,  1.08s/it]
 45%|████▌     | 562/1248 [10:32<12:53,  1.13s/it]
 45%|████▌     | 563/1248 [10:33<12:33,  1.10s/it]
 45%|████▌     | 564/1248 [10:34<12:39,  1.11s/it]
 45%|████▌     | 565/1248 [10:35<12:30,  1.10s/it]
 45%|████▌     | 566/1248 [10:37<12:59,  1.14s/it]
 45%|████▌     | 567/1248 [10:38<12:38,  1.11s/it]
 46%|████▌     | 568/1248 [10:39<12:32,  1.11s/it]
 46%|████▌     | 569/1248 [10:40<12:28,  1.10s/it]
 46%|████▌     | 570/1248 [10:41<12:51,  1.14s/it]
                                                  

 46%|████▌     | 570/1248 [10:41<12:51,  1.14s/it]
 46%|████▌     | 571/1248 [10:42<12:55,  1.15s/it]
 46%|████▌     | 572/1248 [10:43<12:34,  1.12s/it]
 46%|████▌     | 573/1248 [10:44<12:35,  1.12s/it]
 46%|████▌     | 574/1248 [10:46<12:51,  1.14s/it]
 46%|████▌     | 575/1248 [10:47<12:27,  1.11s/it]
 46%|████▌     | 576/1248 [10:48<12:12,  1.09s/it]
 46%|████▌     | 577/1248 [10:49<12:37,  1.13s/it]
 46%|████▋     | 578/1248 [10:50<12:21,  1.11s/it]
 46%|████▋     | 579/1248 [10:51<12:07,  1.09s/it]
 46%|████▋     | 580/1248 [10:52<12:00,  1.08s/it]
                                                  

 46%|████▋     | 580/1248 [10:52<12:00,  1.08s/it]
 47%|████▋     | 581/1248 [10:53<12:27,  1.12s/it]
 47%|████▋     | 582/1248 [10:54<12:14,  1.10s/it]
 47%|████▋     | 583/1248 [10:56<12:25,  1.12s/it]
 47%|████▋     | 584/1248 [10:57<12:08,  1.10s/it]
 47%|████▋     | 585/1248 [10:58<12:30,  1.13s/it]
 47%|████▋     | 586/1248 [10:59<12:17,  1.11s/it]
 47%|████▋     | 587/1248 [11:00<12:06,  1.10s/it]
 47%|████▋     | 588/1248 [11:01<11:54,  1.08s/it]
 47%|████▋     | 589/1248 [11:02<12:23,  1.13s/it]
 47%|████▋     | 590/1248 [11:03<12:08,  1.11s/it]
                                                  

 47%|████▋     | 590/1248 [11:03<12:08,  1.11s/it]
 47%|████▋     | 591/1248 [11:04<12:01,  1.10s/it]
 47%|████▋     | 592/1248 [11:05<11:50,  1.08s/it]
 48%|████▊     | 593/1248 [11:07<12:15,  1.12s/it]
 48%|████▊     | 594/1248 [11:08<12:22,  1.14s/it]
 48%|████▊     | 595/1248 [11:09<12:11,  1.12s/it]
 48%|████▊     | 596/1248 [11:10<12:27,  1.15s/it]
 48%|████▊     | 597/1248 [11:11<12:17,  1.13s/it]
 48%|████▊     | 598/1248 [11:12<11:58,  1.11s/it]
 48%|████▊     | 599/1248 [11:13<12:08,  1.12s/it]
 48%|████▊     | 600/1248 [11:15<12:38,  1.17s/it]
                                                  

 48%|████▊     | 600/1248 [11:15<12:38,  1.17s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 48%|████▊     | 601/1248 [11:17<16:19,  1.51s/it]
 48%|████▊     | 602/1248 [11:18<14:50,  1.38s/it]
 48%|████▊     | 603/1248 [11:19<13:44,  1.28s/it]
 48%|████▊     | 604/1248 [11:20<13:30,  1.26s/it]
 48%|████▊     | 605/1248 [11:21<12:48,  1.20s/it]
 49%|████▊     | 606/1248 [11:22<12:18,  1.15s/it]
 49%|████▊     | 607/1248 [11:23<11:56,  1.12s/it]
 49%|████▊     | 608/1248 [11:25<12:14,  1.15s/it]
 49%|████▉     | 609/1248 [11:26<11:53,  1.12s/it]
 49%|████▉     | 610/1248 [11:27<11:47,  1.11s/it]
                                                  

 49%|████▉     | 610/1248 [11:27<11:47,  1.11s/it]
 49%|████▉     | 611/1248 [11:28<11:35,  1.09s/it]
 49%|████▉     | 612/1248 [11:29<11:58,  1.13s/it]
 49%|████▉     | 613/1248 [11:30<11:42,  1.11s/it]
 49%|████▉     | 614/1248 [11:31<11:29,  1.09s/it]
 49%|████▉     | 615/1248 [11:32<11:55,  1.13s/it]
 49%|████▉     | 616/1248 [11:33<11:40,  1.11s/it]
 49%|████▉     | 617/1248 [11:34<11:27,  1.09s/it]
 50%|████▉     | 618/1248 [11:36<11:19,  1.08s/it]
 50%|████▉     | 619/1248 [11:37<11:38,  1.11s/it]
 50%|████▉     | 620/1248 [11:38<11:24,  1.09s/it]
                                                  

 50%|████▉     | 620/1248 [11:38<11:24,  1.09s/it]
 50%|████▉     | 621/1248 [11:39<11:14,  1.08s/it]
 50%|████▉     | 622/1248 [11:40<11:09,  1.07s/it]
 50%|████▉     | 623/1248 [11:41<11:33,  1.11s/it]
 50%|█████     | 624/1248 [11:42<11:24,  1.10s/it]
 50%|█████     | 625/1248 [11:43<11:12,  1.08s/it]
 50%|█████     | 626/1248 [11:44<11:46,  1.14s/it]
 50%|█████     | 627/1248 [11:45<11:28,  1.11s/it]
 50%|█████     | 628/1248 [11:47<11:17,  1.09s/it]
 50%|█████     | 629/1248 [11:48<11:05,  1.08s/it]
 50%|█████     | 630/1248 [11:49<11:33,  1.12s/it]
                                                  

 50%|█████     | 630/1248 [11:49<11:33,  1.12s/it]
 51%|█████     | 631/1248 [11:50<11:23,  1.11s/it]
 51%|█████     | 632/1248 [11:51<11:18,  1.10s/it]
 51%|█████     | 633/1248 [11:52<11:08,  1.09s/it]
 51%|█████     | 634/1248 [11:53<11:31,  1.13s/it]
 51%|█████     | 635/1248 [11:54<11:26,  1.12s/it]
 51%|█████     | 636/1248 [11:55<11:10,  1.10s/it]
 51%|█████     | 637/1248 [11:56<11:09,  1.09s/it]
 51%|█████     | 638/1248 [11:58<11:55,  1.17s/it]
 51%|█████     | 639/1248 [11:59<11:29,  1.13s/it]
 51%|█████▏    | 640/1248 [12:00<11:15,  1.11s/it]
                                                  

 51%|█████▏    | 640/1248 [12:00<11:15,  1.11s/it]
 51%|█████▏    | 641/1248 [12:01<11:02,  1.09s/it]
 51%|█████▏    | 642/1248 [12:02<11:22,  1.13s/it]
 52%|█████▏    | 643/1248 [12:03<11:05,  1.10s/it]
 52%|█████▏    | 644/1248 [12:04<10:59,  1.09s/it]
 52%|█████▏    | 645/1248 [12:05<10:49,  1.08s/it]
 52%|█████▏    | 646/1248 [12:07<11:14,  1.12s/it]
 52%|█████▏    | 647/1248 [12:08<11:06,  1.11s/it]
 52%|█████▏    | 648/1248 [12:09<10:54,  1.09s/it]
 52%|█████▏    | 649/1248 [12:10<11:29,  1.15s/it]
 52%|█████▏    | 650/1248 [12:11<11:08,  1.12s/it]
                                                  

 52%|█████▏    | 650/1248 [12:11<11:08,  1.12s/it]
 52%|█████▏    | 651/1248 [12:12<10:57,  1.10s/it]
 52%|█████▏    | 652/1248 [12:13<10:46,  1.08s/it]
 52%|█████▏    | 653/1248 [12:14<11:15,  1.14s/it]
 52%|█████▏    | 654/1248 [12:16<11:21,  1.15s/it]
 52%|█████▏    | 655/1248 [12:17<10:59,  1.11s/it]
 53%|█████▎    | 656/1248 [12:18<10:46,  1.09s/it]
 53%|█████▎    | 657/1248 [12:19<11:13,  1.14s/it]
 53%|█████▎    | 658/1248 [12:20<11:02,  1.12s/it]
 53%|█████▎    | 659/1248 [12:21<10:46,  1.10s/it]
 53%|█████▎    | 660/1248 [12:22<10:38,  1.09s/it]
                                                  

 53%|█████▎    | 660/1248 [12:22<10:38,  1.09s/it]
 53%|█████▎    | 661/1248 [12:23<11:09,  1.14s/it]
 53%|█████▎    | 662/1248 [12:24<10:54,  1.12s/it]
 53%|█████▎    | 663/1248 [12:25<10:41,  1.10s/it]
 53%|█████▎    | 664/1248 [12:27<11:01,  1.13s/it]
 53%|█████▎    | 665/1248 [12:28<10:43,  1.10s/it]
 53%|█████▎    | 666/1248 [12:29<10:34,  1.09s/it]
 53%|█████▎    | 667/1248 [12:30<10:25,  1.08s/it]
 54%|█████▎    | 668/1248 [12:31<10:52,  1.13s/it]
 54%|█████▎    | 669/1248 [12:32<10:42,  1.11s/it]
 54%|█████▎    | 670/1248 [12:33<10:38,  1.10s/it]
                                                  

 54%|█████▎    | 670/1248 [12:33<10:38,  1.10s/it]
 54%|█████▍    | 671/1248 [12:34<10:35,  1.10s/it]
 54%|█████▍    | 672/1248 [12:36<10:56,  1.14s/it]
 54%|█████▍    | 673/1248 [12:37<10:37,  1.11s/it]
 54%|█████▍    | 674/1248 [12:38<10:27,  1.09s/it]
 54%|█████▍    | 675/1248 [12:39<10:16,  1.08s/it]
 54%|█████▍    | 676/1248 [12:40<10:48,  1.13s/it]
 54%|█████▍    | 677/1248 [12:41<10:35,  1.11s/it]
 54%|█████▍    | 678/1248 [12:42<10:23,  1.09s/it]
 54%|█████▍    | 679/1248 [12:43<10:12,  1.08s/it]
 54%|█████▍    | 680/1248 [12:44<10:35,  1.12s/it]
                                                  

 54%|█████▍    | 680/1248 [12:44<10:35,  1.12s/it]
 55%|█████▍    | 681/1248 [12:45<10:44,  1.14s/it]
 55%|█████▍    | 682/1248 [12:47<10:28,  1.11s/it]
 55%|█████▍    | 683/1248 [12:48<10:15,  1.09s/it]
 55%|█████▍    | 684/1248 [12:49<10:36,  1.13s/it]
 55%|█████▍    | 685/1248 [12:50<10:22,  1.11s/it]
 55%|█████▍    | 686/1248 [12:51<10:10,  1.09s/it]
 55%|█████▌    | 687/1248 [12:52<10:09,  1.09s/it]
 55%|█████▌    | 688/1248 [12:53<10:34,  1.13s/it]
 55%|█████▌    | 689/1248 [12:54<10:20,  1.11s/it]
 55%|█████▌    | 690/1248 [12:55<10:24,  1.12s/it]
                                                  

 55%|█████▌    | 690/1248 [12:55<10:24,  1.12s/it]
 55%|█████▌    | 691/1248 [12:57<10:48,  1.16s/it]
 55%|█████▌    | 692/1248 [12:58<10:26,  1.13s/it]
 56%|█████▌    | 693/1248 [12:59<10:10,  1.10s/it]
 56%|█████▌    | 694/1248 [13:00<10:04,  1.09s/it]
 56%|█████▌    | 695/1248 [13:01<10:22,  1.13s/it]
 56%|█████▌    | 696/1248 [13:02<10:09,  1.10s/it]
 56%|█████▌    | 697/1248 [13:03<10:04,  1.10s/it]
 56%|█████▌    | 698/1248 [13:04<10:02,  1.10s/it]
 56%|█████▌    | 699/1248 [13:06<10:26,  1.14s/it]
 56%|█████▌    | 700/1248 [13:07<10:10,  1.11s/it]
                                                  

 56%|█████▌    | 700/1248 [13:07<10:10,  1.11s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 56%|█████▌    | 701/1248 [13:09<13:43,  1.51s/it]
 56%|█████▋    | 702/1248 [13:10<12:32,  1.38s/it]
 56%|█████▋    | 703/1248 [13:11<12:09,  1.34s/it]
 56%|█████▋    | 704/1248 [13:12<11:18,  1.25s/it]
 56%|█████▋    | 705/1248 [13:13<10:41,  1.18s/it]
 57%|█████▋    | 706/1248 [13:14<10:22,  1.15s/it]
 57%|█████▋    | 707/1248 [13:16<10:30,  1.16s/it]
 57%|█████▋    | 708/1248 [13:17<10:07,  1.12s/it]
 57%|█████▋    | 709/1248 [13:18<09:58,  1.11s/it]
 57%|█████▋    | 710/1248 [13:19<10:15,  1.14s/it]
                                                  

 57%|█████▋    | 710/1248 [13:19<10:15,  1.14s/it]
 57%|█████▋    | 711/1248 [13:20<09:59,  1.12s/it]
 57%|█████▋    | 712/1248 [13:21<09:47,  1.10s/it]
 57%|█████▋    | 713/1248 [13:22<09:35,  1.07s/it]
 57%|█████▋    | 714/1248 [13:23<09:54,  1.11s/it]
 57%|█████▋    | 715/1248 [13:24<09:42,  1.09s/it]
 57%|█████▋    | 716/1248 [13:25<09:32,  1.08s/it]
 57%|█████▋    | 717/1248 [13:27<09:46,  1.10s/it]
 58%|█████▊    | 718/1248 [13:28<10:24,  1.18s/it]
 58%|█████▊    | 719/1248 [13:29<10:04,  1.14s/it]
 58%|█████▊    | 720/1248 [13:30<09:55,  1.13s/it]
                                                  

 58%|█████▊    | 720/1248 [13:30<09:55,  1.13s/it]
 58%|█████▊    | 721/1248 [13:31<09:40,  1.10s/it]
 58%|█████▊    | 722/1248 [13:32<09:59,  1.14s/it]
 58%|█████▊    | 723/1248 [13:33<09:54,  1.13s/it]
 58%|█████▊    | 724/1248 [13:34<09:41,  1.11s/it]
 58%|█████▊    | 725/1248 [13:36<09:35,  1.10s/it]
 58%|█████▊    | 726/1248 [13:37<09:56,  1.14s/it]
 58%|█████▊    | 727/1248 [13:38<10:00,  1.15s/it]
 58%|█████▊    | 728/1248 [13:39<09:48,  1.13s/it]
 58%|█████▊    | 729/1248 [13:40<10:05,  1.17s/it]
 58%|█████▊    | 730/1248 [13:41<09:47,  1.13s/it]
                                                  

 58%|█████▊    | 730/1248 [13:41<09:47,  1.13s/it]
 59%|█████▊    | 731/1248 [13:42<09:34,  1.11s/it]
 59%|█████▊    | 732/1248 [13:44<09:26,  1.10s/it]
 59%|█████▊    | 733/1248 [13:45<09:45,  1.14s/it]
 59%|█████▉    | 734/1248 [13:46<09:27,  1.10s/it]
 59%|█████▉    | 735/1248 [13:47<09:20,  1.09s/it]
 59%|█████▉    | 736/1248 [13:48<09:20,  1.10s/it]
 59%|█████▉    | 737/1248 [13:49<09:39,  1.13s/it]
 59%|█████▉    | 738/1248 [13:50<09:22,  1.10s/it]
 59%|█████▉    | 739/1248 [13:51<09:21,  1.10s/it]
 59%|█████▉    | 740/1248 [13:52<09:16,  1.10s/it]
                                                  

 59%|█████▉    | 740/1248 [13:52<09:16,  1.10s/it]
 59%|█████▉    | 741/1248 [13:54<09:38,  1.14s/it]
 59%|█████▉    | 742/1248 [13:55<09:21,  1.11s/it]
 60%|█████▉    | 743/1248 [13:56<09:11,  1.09s/it]
 60%|█████▉    | 744/1248 [13:57<09:01,  1.08s/it]
 60%|█████▉    | 745/1248 [13:58<09:23,  1.12s/it]
 60%|█████▉    | 746/1248 [13:59<09:10,  1.10s/it]
 60%|█████▉    | 747/1248 [14:00<09:01,  1.08s/it]
 60%|█████▉    | 748/1248 [14:01<08:52,  1.06s/it]
 60%|██████    | 749/1248 [14:02<09:12,  1.11s/it]
 60%|██████    | 750/1248 [14:03<09:03,  1.09s/it]
                                                  

 60%|██████    | 750/1248 [14:03<09:03,  1.09s/it]
 60%|██████    | 751/1248 [14:04<09:13,  1.11s/it]
 60%|██████    | 752/1248 [14:06<09:33,  1.16s/it]
 60%|██████    | 753/1248 [14:07<09:17,  1.13s/it]
 60%|██████    | 754/1248 [14:08<09:06,  1.11s/it]
 60%|██████    | 755/1248 [14:09<08:58,  1.09s/it]
 61%|██████    | 756/1248 [14:10<09:15,  1.13s/it]
 61%|██████    | 757/1248 [14:11<09:00,  1.10s/it]
 61%|██████    | 758/1248 [14:12<08:59,  1.10s/it]
 61%|██████    | 759/1248 [14:13<08:50,  1.09s/it]
 61%|██████    | 760/1248 [14:15<09:09,  1.13s/it]
                                                  

 61%|██████    | 760/1248 [14:15<09:09,  1.13s/it]
 61%|██████    | 761/1248 [14:16<08:57,  1.10s/it]
 61%|██████    | 762/1248 [14:17<08:50,  1.09s/it]
 61%|██████    | 763/1248 [14:18<09:07,  1.13s/it]
 61%|██████    | 764/1248 [14:19<08:55,  1.11s/it]
 61%|██████▏   | 765/1248 [14:20<08:49,  1.10s/it]
 61%|██████▏   | 766/1248 [14:21<08:49,  1.10s/it]
 61%|██████▏   | 767/1248 [14:22<09:05,  1.13s/it]
 62%|██████▏   | 768/1248 [14:23<08:51,  1.11s/it]
 62%|██████▏   | 769/1248 [14:24<08:43,  1.09s/it]
 62%|██████▏   | 770/1248 [14:25<08:37,  1.08s/it]
                                                  

 62%|██████▏   | 770/1248 [14:25<08:37,  1.08s/it]
 62%|██████▏   | 771/1248 [14:27<08:56,  1.13s/it]
 62%|██████▏   | 772/1248 [14:28<08:44,  1.10s/it]
 62%|██████▏   | 773/1248 [14:29<08:38,  1.09s/it]
 62%|██████▏   | 774/1248 [14:30<08:32,  1.08s/it]
 62%|██████▏   | 775/1248 [14:31<08:49,  1.12s/it]
 62%|██████▏   | 776/1248 [14:32<08:34,  1.09s/it]
 62%|██████▏   | 777/1248 [14:33<08:23,  1.07s/it]
 62%|██████▏   | 778/1248 [14:34<08:17,  1.06s/it]
 62%|██████▏   | 779/1248 [14:35<08:39,  1.11s/it]
 62%|██████▎   | 780/1248 [14:36<08:32,  1.09s/it]
                                                  

 62%|██████▎   | 780/1248 [14:36<08:32,  1.09s/it]
 63%|██████▎   | 781/1248 [14:38<08:31,  1.10s/it]
 63%|██████▎   | 782/1248 [14:39<08:23,  1.08s/it]
 63%|██████▎   | 783/1248 [14:40<08:52,  1.14s/it]
 63%|██████▎   | 784/1248 [14:41<08:38,  1.12s/it]
 63%|██████▎   | 785/1248 [14:42<08:29,  1.10s/it]
 63%|██████▎   | 786/1248 [14:43<08:47,  1.14s/it]
 63%|██████▎   | 787/1248 [14:44<08:33,  1.11s/it]
 63%|██████▎   | 788/1248 [14:45<08:23,  1.09s/it]
 63%|██████▎   | 789/1248 [14:46<08:17,  1.08s/it]
 63%|██████▎   | 790/1248 [14:48<08:37,  1.13s/it]
                                                  

 63%|██████▎   | 790/1248 [14:48<08:37,  1.13s/it]
 63%|██████▎   | 791/1248 [14:49<08:24,  1.10s/it]
 63%|██████▎   | 792/1248 [14:50<08:13,  1.08s/it]
 64%|██████▎   | 793/1248 [14:51<08:11,  1.08s/it]
 64%|██████▎   | 794/1248 [14:52<08:31,  1.13s/it]
 64%|██████▎   | 795/1248 [14:53<08:22,  1.11s/it]
 64%|██████▍   | 796/1248 [14:54<08:16,  1.10s/it]
 64%|██████▍   | 797/1248 [14:55<08:07,  1.08s/it]
 64%|██████▍   | 798/1248 [14:56<08:26,  1.12s/it]
 64%|██████▍   | 799/1248 [14:57<08:14,  1.10s/it]
 64%|██████▍   | 800/1248 [14:59<08:10,  1.10s/it]
                                                  

 64%|██████▍   | 800/1248 [14:59<08:10,  1.10s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 64%|██████▍   | 801/1248 [15:01<10:36,  1.42s/it]
 64%|██████▍   | 802/1248 [15:02<10:07,  1.36s/it]
 64%|██████▍   | 803/1248 [15:03<09:24,  1.27s/it]
 64%|██████▍   | 804/1248 [15:04<09:00,  1.22s/it]
 65%|██████▍   | 805/1248 [15:05<08:57,  1.21s/it]
 65%|██████▍   | 806/1248 [15:06<08:35,  1.17s/it]
 65%|██████▍   | 807/1248 [15:08<08:35,  1.17s/it]
 65%|██████▍   | 808/1248 [15:09<08:16,  1.13s/it]
 65%|██████▍   | 809/1248 [15:10<08:27,  1.16s/it]
 65%|██████▍   | 810/1248 [15:11<08:20,  1.14s/it]
                                                  

 65%|██████▍   | 810/1248 [15:11<08:20,  1.14s/it]
 65%|██████▍   | 811/1248 [15:12<08:09,  1.12s/it]
 65%|██████▌   | 812/1248 [15:13<08:00,  1.10s/it]
 65%|██████▌   | 813/1248 [15:14<08:15,  1.14s/it]
 65%|██████▌   | 814/1248 [15:15<08:02,  1.11s/it]
 65%|██████▌   | 815/1248 [15:16<07:52,  1.09s/it]
 65%|██████▌   | 816/1248 [15:17<07:50,  1.09s/it]
 65%|██████▌   | 817/1248 [15:19<08:04,  1.12s/it]
 66%|██████▌   | 818/1248 [15:20<07:51,  1.10s/it]
 66%|██████▌   | 819/1248 [15:21<07:43,  1.08s/it]
 66%|██████▌   | 820/1248 [15:22<07:37,  1.07s/it]
                                                  

 66%|██████▌   | 820/1248 [15:22<07:37,  1.07s/it]
 66%|██████▌   | 821/1248 [15:23<07:57,  1.12s/it]
 66%|██████▌   | 822/1248 [15:24<07:47,  1.10s/it]
 66%|██████▌   | 823/1248 [15:25<07:36,  1.08s/it]
 66%|██████▌   | 824/1248 [15:26<07:52,  1.11s/it]
 66%|██████▌   | 825/1248 [15:27<07:43,  1.10s/it]
 66%|██████▌   | 826/1248 [15:28<07:36,  1.08s/it]
 66%|██████▋   | 827/1248 [15:29<07:32,  1.08s/it]
 66%|██████▋   | 828/1248 [15:31<07:50,  1.12s/it]
 66%|██████▋   | 829/1248 [15:32<07:40,  1.10s/it]
 67%|██████▋   | 830/1248 [15:33<07:39,  1.10s/it]
                                                  

 67%|██████▋   | 830/1248 [15:33<07:39,  1.10s/it]
 67%|██████▋   | 831/1248 [15:34<07:33,  1.09s/it]
 67%|██████▋   | 832/1248 [15:35<08:05,  1.17s/it]
 67%|██████▋   | 833/1248 [15:36<07:52,  1.14s/it]
 67%|██████▋   | 834/1248 [15:37<07:42,  1.12s/it]
 67%|██████▋   | 835/1248 [15:38<07:41,  1.12s/it]
 67%|██████▋   | 836/1248 [15:40<07:51,  1.15s/it]
 67%|██████▋   | 837/1248 [15:41<07:37,  1.11s/it]
 67%|██████▋   | 838/1248 [15:42<07:28,  1.09s/it]
 67%|██████▋   | 839/1248 [15:43<07:22,  1.08s/it]
 67%|██████▋   | 840/1248 [15:44<07:42,  1.13s/it]
                                                  

 67%|██████▋   | 840/1248 [15:44<07:42,  1.13s/it]
 67%|██████▋   | 841/1248 [15:45<07:34,  1.12s/it]
 67%|██████▋   | 842/1248 [15:46<07:24,  1.10s/it]
 68%|██████▊   | 843/1248 [15:47<07:27,  1.11s/it]
 68%|██████▊   | 844/1248 [15:49<07:41,  1.14s/it]
 68%|██████▊   | 845/1248 [15:50<07:29,  1.12s/it]
 68%|██████▊   | 846/1248 [15:51<07:21,  1.10s/it]
 68%|██████▊   | 847/1248 [15:52<07:40,  1.15s/it]
 68%|██████▊   | 848/1248 [15:53<07:30,  1.13s/it]
 68%|██████▊   | 849/1248 [15:54<07:21,  1.11s/it]
 68%|██████▊   | 850/1248 [15:55<07:17,  1.10s/it]
                                                  

 68%|██████▊   | 850/1248 [15:55<07:17,  1.10s/it]
 68%|██████▊   | 851/1248 [15:56<07:29,  1.13s/it]
 68%|██████▊   | 852/1248 [15:57<07:19,  1.11s/it]
 68%|██████▊   | 853/1248 [15:58<07:10,  1.09s/it]
 68%|██████▊   | 854/1248 [16:00<07:05,  1.08s/it]
 69%|██████▊   | 855/1248 [16:01<07:25,  1.13s/it]
 69%|██████▊   | 856/1248 [16:02<07:15,  1.11s/it]
 69%|██████▊   | 857/1248 [16:03<07:12,  1.11s/it]
 69%|██████▉   | 858/1248 [16:04<07:03,  1.09s/it]
 69%|██████▉   | 859/1248 [16:05<07:18,  1.13s/it]
 69%|██████▉   | 860/1248 [16:06<07:07,  1.10s/it]
                                                  

 69%|██████▉   | 860/1248 [16:06<07:07,  1.10s/it]
 69%|██████▉   | 861/1248 [16:07<07:02,  1.09s/it]
 69%|██████▉   | 862/1248 [16:09<07:31,  1.17s/it]
 69%|██████▉   | 863/1248 [16:10<07:17,  1.14s/it]
 69%|██████▉   | 864/1248 [16:11<07:04,  1.11s/it]
 69%|██████▉   | 865/1248 [16:12<07:05,  1.11s/it]
 69%|██████▉   | 866/1248 [16:13<07:16,  1.14s/it]
 69%|██████▉   | 867/1248 [16:14<07:03,  1.11s/it]
 70%|██████▉   | 868/1248 [16:15<07:08,  1.13s/it]
 70%|██████▉   | 869/1248 [16:16<06:58,  1.10s/it]
 70%|██████▉   | 870/1248 [16:18<07:10,  1.14s/it]
                                                  

 70%|██████▉   | 870/1248 [16:18<07:10,  1.14s/it]
 70%|██████▉   | 871/1248 [16:19<07:03,  1.12s/it]
 70%|██████▉   | 872/1248 [16:20<07:07,  1.14s/it]
 70%|██████▉   | 873/1248 [16:21<06:56,  1.11s/it]
 70%|███████   | 874/1248 [16:22<07:09,  1.15s/it]
 70%|███████   | 875/1248 [16:23<06:55,  1.11s/it]
 70%|███████   | 876/1248 [16:24<06:52,  1.11s/it]
 70%|███████   | 877/1248 [16:25<06:43,  1.09s/it]
 70%|███████   | 878/1248 [16:27<07:03,  1.15s/it]
 70%|███████   | 879/1248 [16:28<06:59,  1.14s/it]
 71%|███████   | 880/1248 [16:29<06:48,  1.11s/it]
                                                  

 71%|███████   | 880/1248 [16:29<06:48,  1.11s/it]
 71%|███████   | 881/1248 [16:30<07:02,  1.15s/it]
 71%|███████   | 882/1248 [16:31<06:51,  1.12s/it]
 71%|███████   | 883/1248 [16:32<06:41,  1.10s/it]
 71%|███████   | 884/1248 [16:33<06:34,  1.08s/it]
 71%|███████   | 885/1248 [16:34<06:47,  1.12s/it]
 71%|███████   | 886/1248 [16:35<06:39,  1.10s/it]
 71%|███████   | 887/1248 [16:37<06:38,  1.10s/it]
 71%|███████   | 888/1248 [16:38<06:31,  1.09s/it]
 71%|███████   | 889/1248 [16:39<06:44,  1.13s/it]
 71%|███████▏  | 890/1248 [16:40<06:34,  1.10s/it]
                                                  

 71%|███████▏  | 890/1248 [16:40<06:34,  1.10s/it]
 71%|███████▏  | 891/1248 [16:41<06:35,  1.11s/it]
 71%|███████▏  | 892/1248 [16:42<06:27,  1.09s/it]
 72%|███████▏  | 893/1248 [16:43<06:43,  1.14s/it]
 72%|███████▏  | 894/1248 [16:44<06:32,  1.11s/it]
 72%|███████▏  | 895/1248 [16:45<06:26,  1.09s/it]
 72%|███████▏  | 896/1248 [16:46<06:25,  1.10s/it]
 72%|███████▏  | 897/1248 [16:48<06:37,  1.13s/it]
 72%|███████▏  | 898/1248 [16:49<06:32,  1.12s/it]
 72%|███████▏  | 899/1248 [16:50<06:23,  1.10s/it]
 72%|███████▏  | 900/1248 [16:51<06:37,  1.14s/it]
                                                  

 72%|███████▏  | 900/1248 [16:51<06:37,  1.14s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 72%|███████▏  | 901/1248 [16:53<08:28,  1.47s/it]
 72%|███████▏  | 902/1248 [16:54<07:57,  1.38s/it]
 72%|███████▏  | 903/1248 [16:55<07:20,  1.28s/it]
 72%|███████▏  | 904/1248 [16:57<07:16,  1.27s/it]
 73%|███████▎  | 905/1248 [16:58<07:02,  1.23s/it]
 73%|███████▎  | 906/1248 [16:59<06:40,  1.17s/it]
 73%|███████▎  | 907/1248 [17:00<06:23,  1.12s/it]
 73%|███████▎  | 908/1248 [17:01<06:30,  1.15s/it]
 73%|███████▎  | 909/1248 [17:02<06:16,  1.11s/it]
 73%|███████▎  | 910/1248 [17:03<06:08,  1.09s/it]
                                                  

 73%|███████▎  | 910/1248 [17:03<06:08,  1.09s/it]
 73%|███████▎  | 911/1248 [17:04<06:03,  1.08s/it]
 73%|███████▎  | 912/1248 [17:05<06:15,  1.12s/it]
 73%|███████▎  | 913/1248 [17:06<06:06,  1.09s/it]
 73%|███████▎  | 914/1248 [17:08<05:59,  1.08s/it]
 73%|███████▎  | 915/1248 [17:09<05:52,  1.06s/it]
 73%|███████▎  | 916/1248 [17:10<06:04,  1.10s/it]
 73%|███████▎  | 917/1248 [17:11<05:56,  1.08s/it]
 74%|███████▎  | 918/1248 [17:12<05:52,  1.07s/it]
 74%|███████▎  | 919/1248 [17:13<05:48,  1.06s/it]
 74%|███████▎  | 920/1248 [17:14<06:11,  1.13s/it]
                                                  

 74%|███████▎  | 920/1248 [17:14<06:11,  1.13s/it]
 74%|███████▍  | 921/1248 [17:15<06:00,  1.10s/it]
 74%|███████▍  | 922/1248 [17:16<05:55,  1.09s/it]
 74%|███████▍  | 923/1248 [17:17<06:09,  1.14s/it]
 74%|███████▍  | 924/1248 [17:19<06:04,  1.13s/it]
 74%|███████▍  | 925/1248 [17:20<05:56,  1.11s/it]
 74%|███████▍  | 926/1248 [17:21<05:50,  1.09s/it]
 74%|███████▍  | 927/1248 [17:22<06:06,  1.14s/it]
 74%|███████▍  | 928/1248 [17:23<05:58,  1.12s/it]
 74%|███████▍  | 929/1248 [17:24<05:50,  1.10s/it]
 75%|███████▍  | 930/1248 [17:25<05:46,  1.09s/it]
                                                  

 75%|███████▍  | 930/1248 [17:25<05:46,  1.09s/it]
 75%|███████▍  | 931/1248 [17:26<05:57,  1.13s/it]
 75%|███████▍  | 932/1248 [17:27<05:48,  1.10s/it]
 75%|███████▍  | 933/1248 [17:28<05:44,  1.09s/it]
 75%|███████▍  | 934/1248 [17:30<05:38,  1.08s/it]
 75%|███████▍  | 935/1248 [17:31<05:51,  1.12s/it]
 75%|███████▌  | 936/1248 [17:32<05:46,  1.11s/it]
 75%|███████▌  | 937/1248 [17:33<05:38,  1.09s/it]
 75%|███████▌  | 938/1248 [17:34<06:01,  1.17s/it]
 75%|███████▌  | 939/1248 [17:35<06:02,  1.17s/it]
 75%|███████▌  | 940/1248 [17:36<05:52,  1.15s/it]
                                                  

 75%|███████▌  | 940/1248 [17:36<05:52,  1.15s/it]
 75%|███████▌  | 941/1248 [17:38<05:41,  1.11s/it]
 75%|███████▌  | 942/1248 [17:39<05:51,  1.15s/it]
 76%|███████▌  | 943/1248 [17:40<05:44,  1.13s/it]
 76%|███████▌  | 944/1248 [17:41<05:35,  1.10s/it]
 76%|███████▌  | 945/1248 [17:42<05:28,  1.08s/it]
 76%|███████▌  | 946/1248 [17:43<05:41,  1.13s/it]
 76%|███████▌  | 947/1248 [17:44<05:31,  1.10s/it]
 76%|███████▌  | 948/1248 [17:45<05:24,  1.08s/it]
 76%|███████▌  | 949/1248 [17:46<05:20,  1.07s/it]
 76%|███████▌  | 950/1248 [17:48<05:33,  1.12s/it]
                                                  

 76%|███████▌  | 950/1248 [17:48<05:33,  1.12s/it]
 76%|███████▌  | 951/1248 [17:49<05:25,  1.09s/it]
 76%|███████▋  | 952/1248 [17:50<05:23,  1.09s/it]
 76%|███████▋  | 953/1248 [17:51<05:20,  1.09s/it]
 76%|███████▋  | 954/1248 [17:52<05:32,  1.13s/it]
 77%|███████▋  | 955/1248 [17:53<05:27,  1.12s/it]
 77%|███████▋  | 956/1248 [17:54<05:22,  1.10s/it]
 77%|███████▋  | 957/1248 [17:55<05:36,  1.16s/it]
 77%|███████▋  | 958/1248 [17:56<05:26,  1.13s/it]
 77%|███████▋  | 959/1248 [17:57<05:19,  1.11s/it]
 77%|███████▋  | 960/1248 [17:59<05:13,  1.09s/it]
                                                  

 77%|███████▋  | 960/1248 [17:59<05:13,  1.09s/it]
 77%|███████▋  | 961/1248 [18:00<05:22,  1.12s/it]
 77%|███████▋  | 962/1248 [18:01<05:15,  1.10s/it]
 77%|███████▋  | 963/1248 [18:02<05:10,  1.09s/it]
 77%|███████▋  | 964/1248 [18:03<05:05,  1.08s/it]
 77%|███████▋  | 965/1248 [18:04<05:19,  1.13s/it]
 77%|███████▋  | 966/1248 [18:05<05:13,  1.11s/it]
 77%|███████▋  | 967/1248 [18:06<05:06,  1.09s/it]
 78%|███████▊  | 968/1248 [18:07<05:04,  1.09s/it]
 78%|███████▊  | 969/1248 [18:09<05:20,  1.15s/it]
 78%|███████▊  | 970/1248 [18:10<05:11,  1.12s/it]
                                                  

 78%|███████▊  | 970/1248 [18:10<05:11,  1.12s/it]
 78%|███████▊  | 971/1248 [18:11<05:04,  1.10s/it]
 78%|███████▊  | 972/1248 [18:12<04:58,  1.08s/it]
 78%|███████▊  | 973/1248 [18:13<05:11,  1.13s/it]
 78%|███████▊  | 974/1248 [18:14<05:03,  1.11s/it]
 78%|███████▊  | 975/1248 [18:15<05:00,  1.10s/it]
 78%|███████▊  | 976/1248 [18:16<05:08,  1.13s/it]
 78%|███████▊  | 977/1248 [18:17<04:59,  1.11s/it]
 78%|███████▊  | 978/1248 [18:19<04:56,  1.10s/it]
 78%|███████▊  | 979/1248 [18:20<04:50,  1.08s/it]
 79%|███████▊  | 980/1248 [18:21<05:03,  1.13s/it]
                                                  

 79%|███████▊  | 980/1248 [18:21<05:03,  1.13s/it]
 79%|███████▊  | 981/1248 [18:22<04:55,  1.11s/it]
 79%|███████▊  | 982/1248 [18:23<04:50,  1.09s/it]
 79%|███████▉  | 983/1248 [18:24<04:46,  1.08s/it]
 79%|███████▉  | 984/1248 [18:25<04:57,  1.13s/it]
 79%|███████▉  | 985/1248 [18:26<04:48,  1.10s/it]
 79%|███████▉  | 986/1248 [18:27<04:42,  1.08s/it]
 79%|███████▉  | 987/1248 [18:28<04:39,  1.07s/it]
 79%|███████▉  | 988/1248 [18:30<04:50,  1.12s/it]
 79%|███████▉  | 989/1248 [18:31<04:43,  1.09s/it]
 79%|███████▉  | 990/1248 [18:32<04:40,  1.09s/it]
                                                  

 79%|███████▉  | 990/1248 [18:32<04:40,  1.09s/it]
 79%|███████▉  | 991/1248 [18:33<04:35,  1.07s/it]
 79%|███████▉  | 992/1248 [18:34<04:45,  1.12s/it]
 80%|███████▉  | 993/1248 [18:35<04:38,  1.09s/it]
 80%|███████▉  | 994/1248 [18:36<04:35,  1.08s/it]
 80%|███████▉  | 995/1248 [18:37<04:45,  1.13s/it]
 80%|███████▉  | 996/1248 [18:38<04:38,  1.11s/it]
 80%|███████▉  | 997/1248 [18:39<04:35,  1.10s/it]
 80%|███████▉  | 998/1248 [18:40<04:31,  1.08s/it]
 80%|████████  | 999/1248 [18:42<04:44,  1.14s/it]
 80%|████████  | 1000/1248 [18:43<04:38,  1.12s/it]
                                                   

 80%|████████  | 1000/1248 [18:43<04:38,  1.12s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 80%|████████  | 1001/1248 [18:45<05:46,  1.40s/it]
 80%|████████  | 1002/1248 [18:46<05:20,  1.30s/it]
 80%|████████  | 1003/1248 [18:47<05:12,  1.27s/it]
 80%|████████  | 1004/1248 [18:48<04:55,  1.21s/it]
 81%|████████  | 1005/1248 [18:49<04:47,  1.18s/it]
 81%|████████  | 1006/1248 [18:50<04:38,  1.15s/it]
 81%|████████  | 1007/1248 [18:52<04:42,  1.17s/it]
 81%|████████  | 1008/1248 [18:53<04:31,  1.13s/it]
 81%|████████  | 1009/1248 [18:54<04:25,  1.11s/it]
 81%|████████  | 1010/1248 [18:55<04:19,  1.09s/it]
                                                   

 81%|████████  | 1010/1248 [18:55<04:19,  1.09s/it]
 81%|████████  | 1011/1248 [18:56<04:27,  1.13s/it]
 81%|████████  | 1012/1248 [18:57<04:29,  1.14s/it]
 81%|████████  | 1013/1248 [18:58<04:20,  1.11s/it]
 81%|████████▏ | 1014/1248 [18:59<04:26,  1.14s/it]
 81%|████████▏ | 1015/1248 [19:00<04:21,  1.12s/it]
 81%|████████▏ | 1016/1248 [19:01<04:14,  1.10s/it]
 81%|████████▏ | 1017/1248 [19:03<04:11,  1.09s/it]
 82%|████████▏ | 1018/1248 [19:04<04:18,  1.12s/it]
 82%|████████▏ | 1019/1248 [19:05<04:12,  1.10s/it]
 82%|████████▏ | 1020/1248 [19:06<04:16,  1.13s/it]
                                                   

 82%|████████▏ | 1020/1248 [19:06<04:16,  1.13s/it]
 82%|████████▏ | 1021/1248 [19:07<04:11,  1.11s/it]
 82%|████████▏ | 1022/1248 [19:08<04:18,  1.14s/it]
 82%|████████▏ | 1023/1248 [19:09<04:11,  1.12s/it]
 82%|████████▏ | 1024/1248 [19:10<04:06,  1.10s/it]
 82%|████████▏ | 1025/1248 [19:11<04:01,  1.08s/it]
 82%|████████▏ | 1026/1248 [19:13<04:16,  1.15s/it]
 82%|████████▏ | 1027/1248 [19:14<04:09,  1.13s/it]
 82%|████████▏ | 1028/1248 [19:15<04:04,  1.11s/it]
 82%|████████▏ | 1029/1248 [19:16<04:00,  1.10s/it]
 83%|████████▎ | 1030/1248 [19:17<04:08,  1.14s/it]
                                                   

 83%|████████▎ | 1030/1248 [19:17<04:08,  1.14s/it]
 83%|████████▎ | 1031/1248 [19:18<04:01,  1.11s/it]
 83%|████████▎ | 1032/1248 [19:19<03:58,  1.10s/it]
 83%|████████▎ | 1033/1248 [19:21<04:05,  1.14s/it]
 83%|████████▎ | 1034/1248 [19:22<03:57,  1.11s/it]
 83%|████████▎ | 1035/1248 [19:23<03:52,  1.09s/it]
 83%|████████▎ | 1036/1248 [19:24<03:47,  1.07s/it]
 83%|████████▎ | 1037/1248 [19:25<03:57,  1.13s/it]
 83%|████████▎ | 1038/1248 [19:26<03:50,  1.10s/it]
 83%|████████▎ | 1039/1248 [19:27<03:48,  1.09s/it]
 83%|████████▎ | 1040/1248 [19:28<03:44,  1.08s/it]
                                                   

 83%|████████▎ | 1040/1248 [19:28<03:44,  1.08s/it]
 83%|████████▎ | 1041/1248 [19:29<03:52,  1.12s/it]
 83%|████████▎ | 1042/1248 [19:30<03:45,  1.10s/it]
 84%|████████▎ | 1043/1248 [19:31<03:41,  1.08s/it]
 84%|████████▎ | 1044/1248 [19:32<03:38,  1.07s/it]
 84%|████████▎ | 1045/1248 [19:34<03:46,  1.12s/it]
 84%|████████▍ | 1046/1248 [19:35<03:40,  1.09s/it]
 84%|████████▍ | 1047/1248 [19:36<03:37,  1.08s/it]
 84%|████████▍ | 1048/1248 [19:37<03:34,  1.07s/it]
 84%|████████▍ | 1049/1248 [19:38<03:42,  1.12s/it]
 84%|████████▍ | 1050/1248 [19:39<03:37,  1.10s/it]
                                                   

 84%|████████▍ | 1050/1248 [19:39<03:37,  1.10s/it]
 84%|████████▍ | 1051/1248 [19:40<03:32,  1.08s/it]
 84%|████████▍ | 1052/1248 [19:41<03:39,  1.12s/it]
 84%|████████▍ | 1053/1248 [19:42<03:36,  1.11s/it]
 84%|████████▍ | 1054/1248 [19:43<03:34,  1.10s/it]
 85%|████████▍ | 1055/1248 [19:45<03:30,  1.09s/it]
 85%|████████▍ | 1056/1248 [19:46<03:37,  1.13s/it]
 85%|████████▍ | 1057/1248 [19:47<03:40,  1.15s/it]
 85%|████████▍ | 1058/1248 [19:48<03:32,  1.12s/it]
 85%|████████▍ | 1059/1248 [19:49<03:26,  1.09s/it]
 85%|████████▍ | 1060/1248 [19:50<03:32,  1.13s/it]
                                                   

 85%|████████▍ | 1060/1248 [19:50<03:32,  1.13s/it]
 85%|████████▌ | 1061/1248 [19:51<03:28,  1.12s/it]
 85%|████████▌ | 1062/1248 [19:52<03:24,  1.10s/it]
 85%|████████▌ | 1063/1248 [19:53<03:20,  1.08s/it]
 85%|████████▌ | 1064/1248 [19:55<03:27,  1.13s/it]
 85%|████████▌ | 1065/1248 [19:56<03:23,  1.11s/it]
 85%|████████▌ | 1066/1248 [19:57<03:18,  1.09s/it]
 85%|████████▌ | 1067/1248 [19:58<03:14,  1.07s/it]
 86%|████████▌ | 1068/1248 [19:59<03:22,  1.12s/it]
 86%|████████▌ | 1069/1248 [20:00<03:16,  1.10s/it]
 86%|████████▌ | 1070/1248 [20:01<03:11,  1.08s/it]
                                                   

 86%|████████▌ | 1070/1248 [20:01<03:11,  1.08s/it]
 86%|████████▌ | 1071/1248 [20:02<03:09,  1.07s/it]
 86%|████████▌ | 1072/1248 [20:03<03:16,  1.12s/it]
 86%|████████▌ | 1073/1248 [20:05<03:12,  1.10s/it]
 86%|████████▌ | 1074/1248 [20:06<03:11,  1.10s/it]
 86%|████████▌ | 1075/1248 [20:07<03:23,  1.18s/it]
 86%|████████▌ | 1076/1248 [20:08<03:16,  1.14s/it]
 86%|████████▋ | 1077/1248 [20:09<03:10,  1.11s/it]
 86%|████████▋ | 1078/1248 [20:10<03:05,  1.09s/it]
 86%|████████▋ | 1079/1248 [20:11<03:10,  1.13s/it]
 87%|████████▋ | 1080/1248 [20:12<03:06,  1.11s/it]
                                                   

 87%|████████▋ | 1080/1248 [20:12<03:06,  1.11s/it]
 87%|████████▋ | 1081/1248 [20:13<03:01,  1.09s/it]
 87%|████████▋ | 1082/1248 [20:14<03:00,  1.09s/it]
 87%|████████▋ | 1083/1248 [20:16<03:06,  1.13s/it]
 87%|████████▋ | 1084/1248 [20:17<03:01,  1.10s/it]
 87%|████████▋ | 1085/1248 [20:18<02:57,  1.09s/it]
 87%|████████▋ | 1086/1248 [20:19<03:08,  1.16s/it]
 87%|████████▋ | 1087/1248 [20:20<03:01,  1.13s/it]
 87%|████████▋ | 1088/1248 [20:21<03:02,  1.14s/it]
 87%|████████▋ | 1089/1248 [20:22<02:55,  1.11s/it]
 87%|████████▋ | 1090/1248 [20:24<03:05,  1.18s/it]
                                                   

 87%|████████▋ | 1090/1248 [20:24<03:05,  1.18s/it]
 87%|████████▋ | 1091/1248 [20:25<02:59,  1.14s/it]
 88%|████████▊ | 1092/1248 [20:26<02:53,  1.11s/it]
 88%|████████▊ | 1093/1248 [20:27<02:50,  1.10s/it]
 88%|████████▊ | 1094/1248 [20:28<02:54,  1.14s/it]
 88%|████████▊ | 1095/1248 [20:29<02:55,  1.14s/it]
 88%|████████▊ | 1096/1248 [20:30<02:49,  1.12s/it]
 88%|████████▊ | 1097/1248 [20:31<02:45,  1.10s/it]
 88%|████████▊ | 1098/1248 [20:33<02:50,  1.14s/it]
 88%|████████▊ | 1099/1248 [20:34<02:47,  1.12s/it]
 88%|████████▊ | 1100/1248 [20:35<02:42,  1.10s/it]
                                                   

 88%|████████▊ | 1100/1248 [20:35<02:42,  1.10s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 88%|████████▊ | 1101/1248 [20:37<03:36,  1.47s/it]
 88%|████████▊ | 1102/1248 [20:38<03:23,  1.40s/it]
 88%|████████▊ | 1103/1248 [20:39<03:07,  1.29s/it]
 88%|████████▊ | 1104/1248 [20:40<02:57,  1.23s/it]
 89%|████████▊ | 1105/1248 [20:42<02:48,  1.18s/it]
 89%|████████▊ | 1106/1248 [20:43<02:48,  1.19s/it]
 89%|████████▊ | 1107/1248 [20:44<02:42,  1.15s/it]
 89%|████████▉ | 1108/1248 [20:45<02:38,  1.13s/it]
 89%|████████▉ | 1109/1248 [20:46<02:41,  1.16s/it]
 89%|████████▉ | 1110/1248 [20:47<02:37,  1.14s/it]
                                                   

 89%|████████▉ | 1110/1248 [20:47<02:37,  1.14s/it]
 89%|████████▉ | 1111/1248 [20:48<02:34,  1.13s/it]
 89%|████████▉ | 1112/1248 [20:49<02:29,  1.10s/it]
 89%|████████▉ | 1113/1248 [20:51<02:34,  1.14s/it]
 89%|████████▉ | 1114/1248 [20:52<02:28,  1.11s/it]
 89%|████████▉ | 1115/1248 [20:53<02:26,  1.10s/it]
 89%|████████▉ | 1116/1248 [20:54<02:23,  1.08s/it]
 90%|████████▉ | 1117/1248 [20:55<02:28,  1.13s/it]
 90%|████████▉ | 1118/1248 [20:56<02:23,  1.11s/it]
 90%|████████▉ | 1119/1248 [20:57<02:21,  1.09s/it]
 90%|████████▉ | 1120/1248 [20:58<02:17,  1.07s/it]
                                                   

 90%|████████▉ | 1120/1248 [20:58<02:17,  1.07s/it]
 90%|████████▉ | 1121/1248 [20:59<02:23,  1.13s/it]
 90%|████████▉ | 1122/1248 [21:00<02:19,  1.10s/it]
 90%|████████▉ | 1123/1248 [21:01<02:16,  1.09s/it]
 90%|█████████ | 1124/1248 [21:03<02:13,  1.08s/it]
 90%|█████████ | 1125/1248 [21:04<02:18,  1.13s/it]
 90%|█████████ | 1126/1248 [21:05<02:15,  1.11s/it]
 90%|█████████ | 1127/1248 [21:06<02:11,  1.09s/it]
 90%|█████████ | 1128/1248 [21:07<02:15,  1.13s/it]
 90%|█████████ | 1129/1248 [21:08<02:13,  1.12s/it]
 91%|█████████ | 1130/1248 [21:09<02:08,  1.09s/it]
                                                   

 91%|█████████ | 1130/1248 [21:09<02:08,  1.09s/it]
 91%|█████████ | 1131/1248 [21:10<02:06,  1.08s/it]
 91%|█████████ | 1132/1248 [21:11<02:10,  1.12s/it]
 91%|█████████ | 1133/1248 [21:13<02:07,  1.11s/it]
 91%|█████████ | 1134/1248 [21:14<02:05,  1.10s/it]
 91%|█████████ | 1135/1248 [21:15<02:01,  1.08s/it]
 91%|█████████ | 1136/1248 [21:16<02:05,  1.12s/it]
 91%|█████████ | 1137/1248 [21:17<02:02,  1.10s/it]
 91%|█████████ | 1138/1248 [21:18<01:58,  1.08s/it]
 91%|█████████▏| 1139/1248 [21:19<01:56,  1.07s/it]
 91%|█████████▏| 1140/1248 [21:20<02:01,  1.13s/it]
                                                   

 91%|█████████▏| 1140/1248 [21:20<02:01,  1.13s/it]
 91%|█████████▏| 1141/1248 [21:21<01:58,  1.10s/it]
 92%|█████████▏| 1142/1248 [21:22<01:55,  1.09s/it]
 92%|█████████▏| 1143/1248 [21:24<01:57,  1.12s/it]
 92%|█████████▏| 1144/1248 [21:25<01:59,  1.15s/it]
 92%|█████████▏| 1145/1248 [21:26<01:55,  1.13s/it]
 92%|█████████▏| 1146/1248 [21:27<01:52,  1.10s/it]
 92%|█████████▏| 1147/1248 [21:28<01:55,  1.15s/it]
 92%|█████████▏| 1148/1248 [21:29<01:51,  1.12s/it]
 92%|█████████▏| 1149/1248 [21:30<01:49,  1.10s/it]
 92%|█████████▏| 1150/1248 [21:31<01:46,  1.09s/it]
                                                   

 92%|█████████▏| 1150/1248 [21:31<01:46,  1.09s/it]
 92%|█████████▏| 1151/1248 [21:33<01:49,  1.12s/it]
 92%|█████████▏| 1152/1248 [21:34<01:45,  1.10s/it]
 92%|█████████▏| 1153/1248 [21:35<01:42,  1.08s/it]
 92%|█████████▏| 1154/1248 [21:36<01:40,  1.07s/it]
 93%|█████████▎| 1155/1248 [21:37<01:43,  1.12s/it]
 93%|█████████▎| 1156/1248 [21:38<01:42,  1.11s/it]
 93%|█████████▎| 1157/1248 [21:39<01:40,  1.11s/it]
 93%|█████████▎| 1158/1248 [21:40<01:38,  1.10s/it]
 93%|█████████▎| 1159/1248 [21:41<01:40,  1.13s/it]
 93%|█████████▎| 1160/1248 [21:42<01:37,  1.11s/it]
                                                   

 93%|█████████▎| 1160/1248 [21:42<01:37,  1.11s/it]
 93%|█████████▎| 1161/1248 [21:44<01:37,  1.13s/it]
 93%|█████████▎| 1162/1248 [21:45<01:40,  1.17s/it]
 93%|█████████▎| 1163/1248 [21:46<01:36,  1.13s/it]
 93%|█████████▎| 1164/1248 [21:47<01:32,  1.10s/it]
 93%|█████████▎| 1165/1248 [21:48<01:30,  1.09s/it]
 93%|█████████▎| 1166/1248 [21:49<01:32,  1.13s/it]
 94%|█████████▎| 1167/1248 [21:50<01:30,  1.11s/it]
 94%|█████████▎| 1168/1248 [21:51<01:27,  1.09s/it]
 94%|█████████▎| 1169/1248 [21:52<01:25,  1.08s/it]
 94%|█████████▍| 1170/1248 [21:54<01:27,  1.12s/it]
                                                   

 94%|█████████▍| 1170/1248 [21:54<01:27,  1.12s/it]
 94%|█████████▍| 1171/1248 [21:55<01:24,  1.09s/it]
 94%|█████████▍| 1172/1248 [21:56<01:22,  1.08s/it]
 94%|█████████▍| 1173/1248 [21:57<01:20,  1.07s/it]
 94%|█████████▍| 1174/1248 [21:58<01:22,  1.12s/it]
 94%|█████████▍| 1175/1248 [21:59<01:22,  1.13s/it]
 94%|█████████▍| 1176/1248 [22:00<01:19,  1.10s/it]
 94%|█████████▍| 1177/1248 [22:01<01:20,  1.14s/it]
 94%|█████████▍| 1178/1248 [22:02<01:17,  1.11s/it]
 94%|█████████▍| 1179/1248 [22:03<01:15,  1.09s/it]
 95%|█████████▍| 1180/1248 [22:05<01:14,  1.09s/it]
                                                   

 95%|█████████▍| 1180/1248 [22:05<01:14,  1.09s/it]
 95%|█████████▍| 1181/1248 [22:06<01:15,  1.13s/it]
 95%|█████████▍| 1182/1248 [22:07<01:12,  1.11s/it]
 95%|█████████▍| 1183/1248 [22:08<01:11,  1.10s/it]
 95%|█████████▍| 1184/1248 [22:09<01:09,  1.09s/it]
 95%|█████████▍| 1185/1248 [22:10<01:11,  1.14s/it]
 95%|█████████▌| 1186/1248 [22:11<01:11,  1.15s/it]
 95%|█████████▌| 1187/1248 [22:13<01:09,  1.13s/it]
 95%|█████████▌| 1188/1248 [22:14<01:06,  1.11s/it]
 95%|█████████▌| 1189/1248 [22:15<01:08,  1.16s/it]
 95%|█████████▌| 1190/1248 [22:16<01:05,  1.13s/it]
                                                   

 95%|█████████▌| 1190/1248 [22:16<01:05,  1.13s/it]
 95%|█████████▌| 1191/1248 [22:17<01:03,  1.11s/it]
 96%|█████████▌| 1192/1248 [22:18<01:04,  1.16s/it]
 96%|█████████▌| 1193/1248 [22:19<01:02,  1.14s/it]
 96%|█████████▌| 1194/1248 [22:20<01:00,  1.11s/it]
 96%|█████████▌| 1195/1248 [22:21<00:57,  1.09s/it]
 96%|█████████▌| 1196/1248 [22:23<00:57,  1.11s/it]
 96%|█████████▌| 1197/1248 [22:24<00:58,  1.15s/it]
 96%|█████████▌| 1198/1248 [22:25<00:56,  1.12s/it]
 96%|█████████▌| 1199/1248 [22:26<00:54,  1.10s/it]
 96%|█████████▌| 1200/1248 [22:27<00:54,  1.15s/it]
                                                   

 96%|█████████▌| 1200/1248 [22:27<00:54,  1.15s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 96%|█████████▌| 1201/1248 [22:29<01:08,  1.47s/it]
 96%|█████████▋| 1202/1248 [22:30<01:01,  1.35s/it]
 96%|█████████▋| 1203/1248 [22:32<00:56,  1.26s/it]
 96%|█████████▋| 1204/1248 [22:33<00:54,  1.25s/it]
 97%|█████████▋| 1205/1248 [22:34<00:51,  1.19s/it]
 97%|█████████▋| 1206/1248 [22:35<00:48,  1.15s/it]
 97%|█████████▋| 1207/1248 [22:36<00:48,  1.17s/it]
 97%|█████████▋| 1208/1248 [22:37<00:45,  1.13s/it]
 97%|█████████▋| 1209/1248 [22:38<00:43,  1.10s/it]
 97%|█████████▋| 1210/1248 [22:39<00:41,  1.09s/it]
                                                   

 97%|█████████▋| 1210/1248 [22:39<00:41,  1.09s/it]
 97%|█████████▋| 1211/1248 [22:40<00:41,  1.13s/it]
 97%|█████████▋| 1212/1248 [22:41<00:39,  1.10s/it]
 97%|█████████▋| 1213/1248 [22:43<00:38,  1.10s/it]
 97%|█████████▋| 1214/1248 [22:44<00:38,  1.14s/it]
 97%|█████████▋| 1215/1248 [22:45<00:38,  1.16s/it]
 97%|█████████▋| 1216/1248 [22:46<00:36,  1.13s/it]
 98%|█████████▊| 1217/1248 [22:47<00:34,  1.10s/it]
 98%|█████████▊| 1218/1248 [22:48<00:32,  1.08s/it]
 98%|█████████▊| 1219/1248 [22:49<00:32,  1.13s/it]
 98%|█████████▊| 1220/1248 [22:51<00:31,  1.14s/it]
                                                   

 98%|█████████▊| 1220/1248 [22:51<00:31,  1.14s/it]
 98%|█████████▊| 1221/1248 [22:52<00:30,  1.11s/it]
 98%|█████████▊| 1222/1248 [22:53<00:29,  1.14s/it]
 98%|█████████▊| 1223/1248 [22:54<00:28,  1.12s/it]
 98%|█████████▊| 1224/1248 [22:55<00:26,  1.11s/it]
 98%|█████████▊| 1225/1248 [22:56<00:25,  1.10s/it]
 98%|█████████▊| 1226/1248 [22:57<00:25,  1.14s/it]
 98%|█████████▊| 1227/1248 [22:58<00:23,  1.12s/it]
 98%|█████████▊| 1228/1248 [22:59<00:22,  1.13s/it]
 98%|█████████▊| 1229/1248 [23:01<00:21,  1.14s/it]
 99%|█████████▊| 1230/1248 [23:02<00:21,  1.18s/it]
                                                   

 99%|█████████▊| 1230/1248 [23:02<00:21,  1.18s/it]
 99%|█████████▊| 1231/1248 [23:03<00:19,  1.14s/it]
 99%|█████████▊| 1232/1248 [23:04<00:17,  1.11s/it]
 99%|█████████▉| 1233/1248 [23:05<00:16,  1.10s/it]
 99%|█████████▉| 1234/1248 [23:06<00:15,  1.13s/it]
 99%|█████████▉| 1235/1248 [23:07<00:14,  1.11s/it]
 99%|█████████▉| 1236/1248 [23:08<00:13,  1.09s/it]
 99%|█████████▉| 1237/1248 [23:09<00:11,  1.08s/it]
 99%|█████████▉| 1238/1248 [23:11<00:11,  1.12s/it]
 99%|█████████▉| 1239/1248 [23:12<00:09,  1.10s/it]
 99%|█████████▉| 1240/1248 [23:13<00:08,  1.08s/it]
                                                   

 99%|█████████▉| 1240/1248 [23:13<00:08,  1.08s/it]
 99%|█████████▉| 1241/1248 [23:14<00:07,  1.07s/it]
100%|█████████▉| 1242/1248 [23:15<00:06,  1.12s/it]
100%|█████████▉| 1243/1248 [23:16<00:05,  1.10s/it]
100%|█████████▉| 1244/1248 [23:17<00:04,  1.09s/it]
100%|█████████▉| 1245/1248 [23:18<00:03,  1.13s/it]
100%|█████████▉| 1246/1248 [23:19<00:02,  1.11s/it]
100%|█████████▉| 1247/1248 [23:20<00:01,  1.09s/it]
100%|██████████| 1248/1248 [23:22<00:00,  1.07s/it]
                                                   

100%|██████████| 1248/1248 [23:23<00:00,  1.07s/it]
100%|██████████| 1248/1248 [23:23<00:00,  1.12s/it]
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:   eval_dataset_size ▁
wandb:    test_split_ratio ▁
wandb:  total_dataset_size ▁
wandb:    total_parameters ▁
wandb:         train/epoch ▁▁▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▄▅▅▅▅▅▅▅▆▆▆▇▇▇▇▇▇▇▇████
wandb:   train/global_step ▁▁▁▁▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▅▅▆▆▆▆▆▆▇▇▇▇▇▇▇█
wandb:     train/grad_norm ▁▁▁▁▁▁▁▁▂▁▂▂▃▂▂▂▃▃▃▃▅▄▅▄▄▅▄▅▅█▅▆▆▅▅▆▆▆▅▆
wandb: train/learning_rate ████▇▇▇▆▆▆▆▆▆▆▆▅▅▅▅▅▄▄▄▄▄▃▃▃▃▃▂▂▂▂▂▁▁▁▁▁
wandb:          train/loss █▆▆▆▆▆▆▆▆▆▆▆▆▆▆▄▄▄▄▄▄▄▄▂▂▂▂▂▂▂▂▂▁▁▁▁▁▁▁▁
wandb:  train_dataset_size ▁
wandb: 
wandb: Run summary:
wandb:        eval_dataset_size 1000
wandb:         final_output_dir /home/<USER>/m/maryam....
wandb:         test_split_ratio 0.1
wandb:       total_dataset_size 10000
wandb:               total_flos 2.732847240904704e+17
wandb:         total_parameters 7298355200
wandb:              train/epoch 3.9936
wandb:        train/global_step 1248
wandb:          train/grad_norm 4.46822
wandb:      train/learning_rate 0.0
wandb:               train/loss 0.3924
wandb:       train_dataset_size 9000
wandb:               train_loss 1.09813
wandb:            train_runtime 1403.0564
wandb: train_samples_per_second 28.509
wandb:   train_steps_per_second 0.889
wandb:       training_completed True
wandb: 
wandb: 🚀 View run eager-jazz-3 at: https://wandb.ai/sarath-chandar/lora-finetuning/runs/cds3cm77
wandb: ⭐️ View project at: https://wandb.ai/sarath-chandar/lora-finetuning
wandb: Synced 5 W&B file(s), 0 media file(s), 0 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250625_164026-cds3cm77/logs
