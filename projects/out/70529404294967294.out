[2025-06-24 13:26:37,350] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:26:42,866] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
Configuration:Configuration:

experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

[2025-06-24 13:26:52,017] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:26:52,021] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:26:52,024] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:26:52,027] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:26:55,526] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:26:55,543] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-24 13:26:55,544] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:26:55,545] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:26:55,559] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-24 13:26:55,559] [INFO] [comm.py:706:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-06-24 13:26:55,568] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-24 13:26:55,650] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-24 13:26:55,663] [INFO] [comm.py:675:init_distributed] cdb=None
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-24 13:27:19,517][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 13:27:19,566][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 13:27:19,590][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 13:27:19,610][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
checkpoint saved at data/mistral_lora_moe
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
Loading model...
Total weights: 611, Router weights: 96
Loading dataset...
Total weights: 611, Router weights: 96
Loading dataset...
Total weights: 611, Router weights: 96
Loading dataset...
Total weights: 611, Router weights: 96
Loading dataset...
Dataset sizes - Train: 100, Val: 100, Test: 100
Dataset sizes - Train: 100, Val: 100, Test: 100
Dataset sizes - Train: 100, Val: 100, Test: 100
Dataset sizes - Train: 100, Val: 100, Test: 100
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mmistral-customer-support-moe[0m at: [34mhttps://wandb.ai/sarath-chandar/mistral-moe-training/runs/dpet7xvx[0m
[1;34mwandb[0m: Find logs at: [1;35m../../../../../../../../network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250624_132655-dpet7xvx/logs[0m
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Tue Jun 24 13:29:02 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 2
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 787150
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7498 MiB
            Time                          : 128197 ms
            Is Running                    : 0
        Process ID                        : 787148
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7498 MiB
            Time                          : 128346 ms
            Is Running                    : 0
        Process ID                        : 787149
            GPU Utilization               : 2 %
            Memory Utilization            : 0 %
            Max memory usage              : 7544 MiB
            Time                          : 128441 ms
            Is Running                    : 0
        Process ID                        : 787147
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7544 MiB
            Time                          : 128819 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 787150
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7518 MiB
            Time                          : 128196 ms
            Is Running                    : 0
        Process ID                        : 787148
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7518 MiB
            Time                          : 128345 ms
            Is Running                    : 0
        Process ID                        : 787149
            GPU Utilization               : 2 %
            Memory Utilization            : 0 %
            Max memory usage              : 7396 MiB
            Time                          : 128440 ms
            Is Running                    : 0
        Process ID                        : 787147
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7396 MiB
            Time                          : 128818 ms
            Is Running                    : 0

Tue Jun 24 13:29:02 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-40GB          On  | ********:01:00.0 Off |                    0 |
| N/A   37C    P0             103W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-40GB          On  | ********:81:00.0 Off |                    0 |
| N/A   39C    P0             106W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
