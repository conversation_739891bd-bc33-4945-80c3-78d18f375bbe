[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0624 19:57:34.297000 821506 torch/distributed/run.py:766] 
W0624 19:57:34.297000 821506 torch/distributed/run.py:766] *****************************************
W0624 19:57:34.297000 821506 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0624 19:57:34.297000 821506 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250624_195744-3wjwi3gt
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-customer-support-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/3wjwi3gt

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:14<00:14, 14.05s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:14<00:14, 14.09s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:14<00:14, 14.62s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:14<00:14, 14.85s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.15s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.89s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:20<00:00,  9.57s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:20<00:00, 10.36s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:20<00:00,  9.49s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:20<00:00,  9.72s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:20<00:00, 10.26s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:20<00:00, 10.37s/it]


  0%|          | 0/547 [00:00<?, ?it/s]
  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 235940.38it/s]

100%|██████████| 547/547 [00:00<00:00, 256230.10it/s]

  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 252563.22it/s]

  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 248379.81it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.49s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.88s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:11<00:11, 11.17s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.58s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.47s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.88s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.53s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.40s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.89s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.73s/it]


Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.60s/it]Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.88s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank2]:[W624 19:59:26.933210499 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank3]:[W624 19:59:26.067520973 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank1]:[W624 19:59:26.090844402 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/24184 [00:00<?, ? examples/s]
Map:   4%|▍         | 1000/24184 [00:00<00:02, 9588.12 examples/s]
Map:  12%|█▏        | 3000/24184 [00:00<00:01, 12449.06 examples/s]
Map:  21%|██        | 5000/24184 [00:00<00:01, 13064.58 examples/s]
Map:  29%|██▉       | 7000/24184 [00:00<00:01, 13394.62 examples/s]
Map:  37%|███▋      | 9000/24184 [00:00<00:01, 13466.16 examples/s]
Map:  45%|████▌     | 11000/24184 [00:00<00:00, 13554.26 examples/s]
Map:  54%|█████▍    | 13000/24184 [00:00<00:00, 13671.32 examples/s]
Map:  62%|██████▏   | 15000/24184 [00:01<00:00, 13893.75 examples/s]
Map:  70%|███████   | 17000/24184 [00:01<00:00, 13950.65 examples/s]
Map:  79%|███████▊  | 19000/24184 [00:01<00:00, 13962.35 examples/s]
Map:  87%|████████▋ | 21000/24184 [00:01<00:00, 13968.19 examples/s]
Map:  95%|█████████▌| 23000/24184 [00:01<00:00, 13995.88 examples/s]
Map: 100%|██████████| 24184/24184 [00:01<00:00, 13572.28 examples/s]

Map:   0%|          | 0/806 [00:00<?, ? examples/s]
Map: 100%|██████████| 806/806 [00:00<00:00, 13327.74 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank0]:[W624 19:59:28.013103908 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/24184 [00:00<?, ? examples/s]
Map:   0%|          | 0/24184 [00:00<?, ? examples/s]
Map:   0%|          | 0/24184 [00:00<?, ? examples/s]
Map:   4%|▍         | 1000/24184 [00:00<00:03, 6482.48 examples/s]
Map:   4%|▍         | 1000/24184 [00:00<00:03, 6074.19 examples/s]
Map:   4%|▍         | 1000/24184 [00:00<00:04, 5530.22 examples/s]
Map:   8%|▊         | 2000/24184 [00:00<00:03, 7376.92 examples/s]
Map:   8%|▊         | 2000/24184 [00:00<00:03, 6726.89 examples/s]
Map:   8%|▊         | 2000/24184 [00:00<00:03, 6465.58 examples/s]
Map:  12%|█▏        | 3000/24184 [00:00<00:03, 6954.79 examples/s]
Map:  12%|█▏        | 3000/24184 [00:00<00:03, 6692.85 examples/s]
Map:  17%|█▋        | 4000/24184 [00:00<00:02, 8612.94 examples/s]
Map:  17%|█▋        | 4000/24184 [00:00<00:02, 6834.33 examples/s]
Map:  17%|█▋        | 4000/24184 [00:00<00:03, 6718.40 examples/s]
Map:  21%|██        | 5000/24184 [00:00<00:02, 8229.11 examples/s]
Map:  21%|██        | 5000/24184 [00:00<00:02, 6521.59 examples/s]
Map:  21%|██        | 5000/24184 [00:00<00:03, 6282.18 examples/s]
Map:  25%|██▍       | 6000/24184 [00:00<00:02, 7153.65 examples/s]
Map:  25%|██▍       | 6000/24184 [00:00<00:02, 6515.10 examples/s]
Map:  29%|██▉       | 7000/24184 [00:00<00:02, 6966.59 examples/s]
Map:  25%|██▍       | 6000/24184 [00:00<00:02, 6091.29 examples/s]
Map:  29%|██▉       | 7000/24184 [00:01<00:02, 6943.50 examples/s]
Map:  33%|███▎      | 8000/24184 [00:01<00:02, 6969.83 examples/s]
Map:  29%|██▉       | 7000/24184 [00:01<00:02, 6165.16 examples/s]
Map:  33%|███▎      | 8000/24184 [00:01<00:02, 7454.79 examples/s]
Map:  37%|███▋      | 9000/24184 [00:01<00:02, 6979.39 examples/s]
Map:  33%|███▎      | 8000/24184 [00:01<00:02, 6308.06 examples/s]
Map:  37%|███▋      | 9000/24184 [00:01<00:02, 7312.78 examples/s]
Map:  41%|████▏     | 10000/24184 [00:01<00:02, 6950.21 examples/s]
Map:  37%|███▋      | 9000/24184 [00:01<00:02, 6439.69 examples/s]
Map:  41%|████▏     | 10000/24184 [00:01<00:01, 7283.24 examples/s]
Map:  45%|████▌     | 11000/24184 [00:01<00:01, 7387.95 examples/s]
Map:  45%|████▌     | 11000/24184 [00:01<00:01, 7114.69 examples/s]
Map:  41%|████▏     | 10000/24184 [00:01<00:02, 5995.06 examples/s]
Map:  54%|█████▍    | 13000/24184 [00:01<00:01, 8681.26 examples/s]
Map:  50%|████▉     | 12000/24184 [00:01<00:01, 7630.85 examples/s]
Map:  45%|████▌     | 11000/24184 [00:01<00:02, 6493.46 examples/s]
Map:  58%|█████▊    | 14000/24184 [00:01<00:01, 8965.20 examples/s]
Map:  62%|██████▏   | 15000/24184 [00:01<00:01, 8887.76 examples/s]
Map:  54%|█████▍    | 13000/24184 [00:01<00:01, 8189.08 examples/s]
Map:  58%|█████▊    | 14000/24184 [00:01<00:01, 8343.86 examples/s]
Map:  66%|██████▌   | 16000/24184 [00:02<00:00, 8820.55 examples/s]
Map:  62%|██████▏   | 15000/24184 [00:02<00:01, 8444.37 examples/s]
Map:  58%|█████▊    | 14000/24184 [00:02<00:01, 8305.07 examples/s]
Map:  70%|███████   | 17000/24184 [00:02<00:00, 8768.21 examples/s]
Map:  66%|██████▌   | 16000/24184 [00:02<00:00, 8466.75 examples/s]
Map:  62%|██████▏   | 15000/24184 [00:02<00:01, 8250.32 examples/s]
Map:  66%|██████▌   | 16000/24184 [00:02<00:00, 8601.92 examples/s]
Map:  70%|███████   | 17000/24184 [00:02<00:00, 8533.08 examples/s]
Map:  74%|███████▍  | 18000/24184 [00:02<00:00, 8594.65 examples/s]
Map:  70%|███████   | 17000/24184 [00:02<00:00, 8579.28 examples/s]
Map:  79%|███████▊  | 19000/24184 [00:02<00:00, 8761.27 examples/s]
Map:  74%|███████▍  | 18000/24184 [00:02<00:00, 8546.63 examples/s]
Map:  74%|███████▍  | 18000/24184 [00:02<00:00, 8770.39 examples/s]
Map:  79%|███████▊  | 19000/24184 [00:02<00:00, 8610.83 examples/s]
Map:  83%|████████▎ | 20000/24184 [00:02<00:00, 8650.65 examples/s]
Map:  79%|███████▊  | 19000/24184 [00:02<00:00, 8782.43 examples/s]
Map:  87%|████████▋ | 21000/24184 [00:02<00:00, 8947.60 examples/s]
Map:  83%|████████▎ | 20000/24184 [00:02<00:00, 8469.20 examples/s]
Map:  83%|████████▎ | 20000/24184 [00:02<00:00, 8667.87 examples/s]
Map:  91%|█████████ | 22000/24184 [00:02<00:00, 8776.91 examples/s]
Map:  87%|████████▋ | 21000/24184 [00:02<00:00, 8828.25 examples/s]
Map:  95%|█████████▌| 23000/24184 [00:02<00:00, 8786.15 examples/s]
Map:  87%|████████▋ | 21000/24184 [00:02<00:00, 8689.44 examples/s]
Map:  91%|█████████ | 22000/24184 [00:02<00:00, 8796.49 examples/s]
Map:  95%|█████████▌| 23000/24184 [00:02<00:00, 8828.76 examples/s]
Map:  99%|█████████▉| 24000/24184 [00:02<00:00, 8687.34 examples/s]
Map:  91%|█████████ | 22000/24184 [00:02<00:00, 8553.51 examples/s]
Map: 100%|██████████| 24184/24184 [00:02<00:00, 8094.94 examples/s]

Map:   0%|          | 0/806 [00:00<?, ? examples/s]
Map:  99%|█████████▉| 24000/24184 [00:03<00:00, 9091.40 examples/s]
Map: 100%|██████████| 24184/24184 [00:03<00:00, 7871.77 examples/s]

Map:   0%|          | 0/806 [00:00<?, ? examples/s]
Map: 100%|██████████| 806/806 [00:00<00:00, 8463.01 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:  99%|█████████▉| 24000/24184 [00:03<00:00, 9590.34 examples/s]
Map: 100%|██████████| 24184/24184 [00:03<00:00, 7680.73 examples/s]

Map: 100%|██████████| 806/806 [00:00<00:00, 11042.65 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/806 [00:00<?, ? examples/s]
Map: 100%|██████████| 806/806 [00:00<00:00, 11499.41 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

  0%|          | 0/1511 [00:00<?, ?it/s]
  0%|          | 1/1511 [00:02<1:06:32,  2.64s/it]
                                                  

  0%|          | 1/1511 [00:02<1:06:32,  2.64s/it]
  0%|          | 2/1511 [00:04<58:35,  2.33s/it]  
  0%|          | 3/1511 [00:06<55:53,  2.22s/it]
  0%|          | 4/1511 [00:08<54:21,  2.16s/it]
  0%|          | 5/1511 [00:11<54:34,  2.17s/it]
  0%|          | 6/1511 [00:13<53:46,  2.14s/it]
  0%|          | 7/1511 [00:15<53:12,  2.12s/it]
  1%|          | 8/1511 [00:17<52:55,  2.11s/it]
  1%|          | 9/1511 [00:19<52:39,  2.10s/it]
  1%|          | 10/1511 [00:21<52:22,  2.09s/it]
  1%|          | 11/1511 [00:23<52:05,  2.08s/it]
  1%|          | 12/1511 [00:25<53:54,  2.16s/it]
  1%|          | 13/1511 [00:28<53:23,  2.14s/it]
  1%|          | 14/1511 [00:30<52:50,  2.12s/it]
  1%|          | 15/1511 [00:32<52:26,  2.10s/it]
  1%|          | 16/1511 [00:34<52:12,  2.10s/it]
  1%|          | 17/1511 [00:36<52:01,  2.09s/it]
  1%|          | 18/1511 [00:38<51:49,  2.08s/it]
  1%|▏         | 19/1511 [00:40<51:42,  2.08s/it]
  1%|▏         | 20/1511 [00:42<51:37,  2.08s/it]
  1%|▏         | 21/1511 [00:44<51:29,  2.07s/it]
  1%|▏         | 22/1511 [00:46<51:27,  2.07s/it]
  2%|▏         | 23/1511 [00:48<51:23,  2.07s/it]
  2%|▏         | 24/1511 [00:50<51:21,  2.07s/it]
  2%|▏         | 25/1511 [00:52<51:15,  2.07s/it]
                                                 

  2%|▏         | 25/1511 [00:52<51:15,  2.07s/it]
  2%|▏         | 26/1511 [00:54<51:17,  2.07s/it]
  2%|▏         | 27/1511 [00:57<51:15,  2.07s/it]
  2%|▏         | 28/1511 [00:59<51:09,  2.07s/it]
  2%|▏         | 29/1511 [01:01<51:06,  2.07s/it]
  2%|▏         | 30/1511 [01:03<51:05,  2.07s/it]
  2%|▏         | 31/1511 [01:05<51:03,  2.07s/it]
  2%|▏         | 32/1511 [01:07<51:01,  2.07s/it]
  2%|▏         | 33/1511 [01:09<50:58,  2.07s/it]
  2%|▏         | 34/1511 [01:11<51:00,  2.07s/it]
  2%|▏         | 35/1511 [01:13<50:59,  2.07s/it]
  2%|▏         | 36/1511 [01:15<50:55,  2.07s/it]
  2%|▏         | 37/1511 [01:17<51:02,  2.08s/it]
  3%|▎         | 38/1511 [01:19<50:58,  2.08s/it]
  3%|▎         | 39/1511 [01:21<50:54,  2.07s/it]
  3%|▎         | 40/1511 [01:24<51:24,  2.10s/it]
  3%|▎         | 41/1511 [01:26<51:20,  2.10s/it]
  3%|▎         | 42/1511 [01:28<51:18,  2.10s/it]
  3%|▎         | 43/1511 [01:30<51:06,  2.09s/it]
  3%|▎         | 44/1511 [01:32<50:52,  2.08s/it]
  3%|▎         | 45/1511 [01:34<50:45,  2.08s/it]
  3%|▎         | 46/1511 [01:36<50:39,  2.08s/it]
  3%|▎         | 47/1511 [01:38<51:24,  2.11s/it]
  3%|▎         | 48/1511 [01:40<51:19,  2.11s/it]
  3%|▎         | 49/1511 [01:42<51:03,  2.10s/it]
  3%|▎         | 50/1511 [01:44<50:49,  2.09s/it]
                                                 

  3%|▎         | 50/1511 [01:44<50:49,  2.09s/it]
  3%|▎         | 51/1511 [01:46<50:36,  2.08s/it]
  3%|▎         | 52/1511 [01:49<50:31,  2.08s/it]
  4%|▎         | 53/1511 [01:51<50:27,  2.08s/it]
  4%|▎         | 54/1511 [01:53<50:22,  2.07s/it]
  4%|▎         | 55/1511 [01:55<50:16,  2.07s/it]
  4%|▎         | 56/1511 [01:57<50:19,  2.08s/it]
  4%|▍         | 57/1511 [01:59<50:14,  2.07s/it]
  4%|▍         | 58/1511 [02:01<50:13,  2.07s/it]
  4%|▍         | 59/1511 [02:03<50:05,  2.07s/it]
  4%|▍         | 60/1511 [02:05<49:59,  2.07s/it]
  4%|▍         | 61/1511 [02:07<50:05,  2.07s/it]
  4%|▍         | 62/1511 [02:09<50:01,  2.07s/it]
  4%|▍         | 63/1511 [02:11<50:02,  2.07s/it]
  4%|▍         | 64/1511 [02:13<49:56,  2.07s/it]
  4%|▍         | 65/1511 [02:15<49:56,  2.07s/it]
  4%|▍         | 66/1511 [02:18<50:49,  2.11s/it]
  4%|▍         | 67/1511 [02:20<50:48,  2.11s/it]
  5%|▍         | 68/1511 [02:22<51:06,  2.13s/it]
  5%|▍         | 69/1511 [02:24<50:54,  2.12s/it]
  5%|▍         | 70/1511 [02:26<50:35,  2.11s/it]
  5%|▍         | 71/1511 [02:28<50:16,  2.09s/it]
  5%|▍         | 72/1511 [02:30<50:06,  2.09s/it]
  5%|▍         | 73/1511 [02:32<49:55,  2.08s/it]
  5%|▍         | 74/1511 [02:34<49:46,  2.08s/it]
  5%|▍         | 75/1511 [02:36<49:40,  2.08s/it]
                                                 

  5%|▍         | 75/1511 [02:36<49:40,  2.08s/it]
  5%|▌         | 76/1511 [02:39<49:32,  2.07s/it]
  5%|▌         | 77/1511 [02:41<49:30,  2.07s/it]
  5%|▌         | 78/1511 [02:43<49:28,  2.07s/it]
  5%|▌         | 79/1511 [02:45<49:28,  2.07s/it]
  5%|▌         | 80/1511 [02:47<49:23,  2.07s/it]
  5%|▌         | 81/1511 [02:49<49:17,  2.07s/it]
  5%|▌         | 82/1511 [02:51<49:15,  2.07s/it]
  5%|▌         | 83/1511 [02:53<49:10,  2.07s/it]
  6%|▌         | 84/1511 [02:55<49:12,  2.07s/it]
  6%|▌         | 85/1511 [02:57<49:10,  2.07s/it]
  6%|▌         | 86/1511 [02:59<49:03,  2.07s/it]
  6%|▌         | 87/1511 [03:01<49:02,  2.07s/it]
  6%|▌         | 88/1511 [03:04<50:48,  2.14s/it]
  6%|▌         | 89/1511 [03:06<50:28,  2.13s/it]
  6%|▌         | 90/1511 [03:08<49:57,  2.11s/it]
  6%|▌         | 91/1511 [03:10<49:35,  2.10s/it]
  6%|▌         | 92/1511 [03:12<49:22,  2.09s/it]
  6%|▌         | 93/1511 [03:14<49:09,  2.08s/it]
  6%|▌         | 94/1511 [03:16<49:08,  2.08s/it]
  6%|▋         | 95/1511 [03:18<49:00,  2.08s/it]
  6%|▋         | 96/1511 [03:20<48:57,  2.08s/it]
  6%|▋         | 97/1511 [03:22<48:50,  2.07s/it]
  6%|▋         | 98/1511 [03:24<48:48,  2.07s/it]
  7%|▋         | 99/1511 [03:26<48:43,  2.07s/it]
  7%|▋         | 100/1511 [03:28<48:41,  2.07s/it]
                                                  

  7%|▋         | 100/1511 [03:28<48:41,  2.07s/it]
  7%|▋         | 101/1511 [03:31<48:37,  2.07s/it]
  7%|▋         | 102/1511 [03:33<48:37,  2.07s/it]
  7%|▋         | 103/1511 [03:35<48:38,  2.07s/it]
  7%|▋         | 104/1511 [03:37<48:32,  2.07s/it]
  7%|▋         | 105/1511 [03:39<48:34,  2.07s/it]
  7%|▋         | 106/1511 [03:41<48:28,  2.07s/it]
  7%|▋         | 107/1511 [03:43<48:23,  2.07s/it]
  7%|▋         | 108/1511 [03:45<48:22,  2.07s/it]
  7%|▋         | 109/1511 [03:47<48:20,  2.07s/it]
  7%|▋         | 110/1511 [03:49<48:20,  2.07s/it]
  7%|▋         | 111/1511 [03:51<48:19,  2.07s/it]
  7%|▋         | 112/1511 [03:53<48:15,  2.07s/it]
  7%|▋         | 113/1511 [03:55<48:12,  2.07s/it]
  8%|▊         | 114/1511 [03:57<48:11,  2.07s/it]
  8%|▊         | 115/1511 [04:00<48:07,  2.07s/it]
  8%|▊         | 116/1511 [04:02<48:03,  2.07s/it]
  8%|▊         | 117/1511 [04:04<48:02,  2.07s/it]
  8%|▊         | 118/1511 [04:06<48:03,  2.07s/it]
  8%|▊         | 119/1511 [04:08<48:01,  2.07s/it]
  8%|▊         | 120/1511 [04:10<47:57,  2.07s/it]
  8%|▊         | 121/1511 [04:12<47:58,  2.07s/it]
  8%|▊         | 122/1511 [04:14<47:53,  2.07s/it]
  8%|▊         | 123/1511 [04:16<47:51,  2.07s/it]
  8%|▊         | 124/1511 [04:18<47:49,  2.07s/it]
  8%|▊         | 125/1511 [04:20<47:47,  2.07s/it]
                                                  

  8%|▊         | 125/1511 [04:20<47:47,  2.07s/it]
  8%|▊         | 126/1511 [04:22<47:46,  2.07s/it]
  8%|▊         | 127/1511 [04:24<47:45,  2.07s/it]
  8%|▊         | 128/1511 [04:26<47:42,  2.07s/it]
  9%|▊         | 129/1511 [04:28<47:38,  2.07s/it]
  9%|▊         | 130/1511 [04:31<47:39,  2.07s/it]
  9%|▊         | 131/1511 [04:33<47:35,  2.07s/it]
  9%|▊         | 132/1511 [04:35<47:36,  2.07s/it]
  9%|▉         | 133/1511 [04:37<47:31,  2.07s/it]
  9%|▉         | 134/1511 [04:39<47:26,  2.07s/it]
  9%|▉         | 135/1511 [04:41<47:28,  2.07s/it]
  9%|▉         | 136/1511 [04:43<47:26,  2.07s/it]
  9%|▉         | 137/1511 [04:45<47:22,  2.07s/it]
  9%|▉         | 138/1511 [04:47<47:24,  2.07s/it]
  9%|▉         | 139/1511 [04:49<47:23,  2.07s/it]
  9%|▉         | 140/1511 [04:51<47:18,  2.07s/it]
  9%|▉         | 141/1511 [04:53<47:16,  2.07s/it]
  9%|▉         | 142/1511 [04:55<47:12,  2.07s/it]
  9%|▉         | 143/1511 [04:57<47:10,  2.07s/it]
 10%|▉         | 144/1511 [05:00<47:09,  2.07s/it]
 10%|▉         | 145/1511 [05:02<47:10,  2.07s/it]
 10%|▉         | 146/1511 [05:04<47:07,  2.07s/it]
 10%|▉         | 147/1511 [05:06<47:08,  2.07s/it]
 10%|▉         | 148/1511 [05:08<47:04,  2.07s/it]
 10%|▉         | 149/1511 [05:10<47:01,  2.07s/it]
 10%|▉         | 150/1511 [05:12<46:56,  2.07s/it]
                                                  

 10%|▉         | 150/1511 [05:12<46:56,  2.07s/it]
 10%|▉         | 151/1511 [05:14<46:51,  2.07s/it]
 10%|█         | 152/1511 [05:16<46:53,  2.07s/it]
 10%|█         | 153/1511 [05:18<46:50,  2.07s/it]
 10%|█         | 154/1511 [05:20<46:49,  2.07s/it]
 10%|█         | 155/1511 [05:22<46:49,  2.07s/it]
 10%|█         | 156/1511 [05:24<46:46,  2.07s/it]
 10%|█         | 157/1511 [05:27<47:15,  2.09s/it]
 10%|█         | 158/1511 [05:29<47:14,  2.09s/it]
 11%|█         | 159/1511 [05:31<48:39,  2.16s/it]
 11%|█         | 160/1511 [05:33<48:00,  2.13s/it]
 11%|█         | 161/1511 [05:35<49:03,  2.18s/it]
 11%|█         | 162/1511 [05:37<48:18,  2.15s/it]
 11%|█         | 163/1511 [05:40<48:19,  2.15s/it]
 11%|█         | 164/1511 [05:42<48:00,  2.14s/it]
 11%|█         | 165/1511 [05:44<47:36,  2.12s/it]
 11%|█         | 166/1511 [05:46<47:12,  2.11s/it]
 11%|█         | 167/1511 [05:48<46:54,  2.09s/it]
 11%|█         | 168/1511 [05:50<46:41,  2.09s/it]
 11%|█         | 169/1511 [05:52<46:32,  2.08s/it]
 11%|█▏        | 170/1511 [05:54<46:33,  2.08s/it]
 11%|█▏        | 171/1511 [05:56<47:00,  2.10s/it]
 11%|█▏        | 172/1511 [05:58<46:58,  2.10s/it]
 11%|█▏        | 173/1511 [06:00<46:36,  2.09s/it]
 12%|█▏        | 174/1511 [06:02<46:31,  2.09s/it]
 12%|█▏        | 175/1511 [06:05<46:27,  2.09s/it]
                                                  

 12%|█▏        | 175/1511 [06:05<46:27,  2.09s/it]
 12%|█▏        | 176/1511 [06:07<46:17,  2.08s/it]
 12%|█▏        | 177/1511 [06:09<46:12,  2.08s/it]
 12%|█▏        | 178/1511 [06:11<46:09,  2.08s/it]
 12%|█▏        | 179/1511 [06:13<46:04,  2.08s/it]
 12%|█▏        | 180/1511 [06:15<46:03,  2.08s/it]
 12%|█▏        | 181/1511 [06:17<46:02,  2.08s/it]
 12%|█▏        | 182/1511 [06:19<45:56,  2.07s/it]
 12%|█▏        | 183/1511 [06:21<45:50,  2.07s/it]
 12%|█▏        | 184/1511 [06:23<45:46,  2.07s/it]
 12%|█▏        | 185/1511 [06:25<45:44,  2.07s/it]
 12%|█▏        | 186/1511 [06:27<45:45,  2.07s/it]
 12%|█▏        | 187/1511 [06:29<45:45,  2.07s/it]
 12%|█▏        | 188/1511 [06:31<45:40,  2.07s/it]
 13%|█▎        | 189/1511 [06:34<45:36,  2.07s/it]
 13%|█▎        | 190/1511 [06:36<45:34,  2.07s/it]
 13%|█▎        | 191/1511 [06:38<45:32,  2.07s/it]
 13%|█▎        | 192/1511 [06:40<45:33,  2.07s/it]
 13%|█▎        | 193/1511 [06:42<45:32,  2.07s/it]
 13%|█▎        | 194/1511 [06:44<46:45,  2.13s/it]
 13%|█▎        | 195/1511 [06:46<46:19,  2.11s/it]
 13%|█▎        | 196/1511 [06:48<46:03,  2.10s/it]
 13%|█▎        | 197/1511 [06:50<45:54,  2.10s/it]
 13%|█▎        | 198/1511 [06:52<45:38,  2.09s/it]
 13%|█▎        | 199/1511 [06:54<45:32,  2.08s/it]
 13%|█▎        | 200/1511 [06:57<45:24,  2.08s/it]
                                                  

 13%|█▎        | 200/1511 [06:57<45:24,  2.08s/it]
 13%|█▎        | 201/1511 [06:59<45:18,  2.08s/it]
 13%|█▎        | 202/1511 [07:01<45:13,  2.07s/it]
 13%|█▎        | 203/1511 [07:03<45:07,  2.07s/it]
 14%|█▎        | 204/1511 [07:05<45:06,  2.07s/it]
 14%|█▎        | 205/1511 [07:07<45:09,  2.07s/it]
 14%|█▎        | 206/1511 [07:09<45:05,  2.07s/it]
 14%|█▎        | 207/1511 [07:11<44:58,  2.07s/it]
 14%|█▍        | 208/1511 [07:13<45:00,  2.07s/it]
 14%|█▍        | 209/1511 [07:15<45:00,  2.07s/it]
 14%|█▍        | 210/1511 [07:17<44:58,  2.07s/it]
 14%|█▍        | 211/1511 [07:19<44:52,  2.07s/it]
 14%|█▍        | 212/1511 [07:21<44:47,  2.07s/it]
 14%|█▍        | 213/1511 [07:23<44:48,  2.07s/it]
 14%|█▍        | 214/1511 [07:26<44:46,  2.07s/it]
 14%|█▍        | 215/1511 [07:28<44:45,  2.07s/it]
 14%|█▍        | 216/1511 [07:30<44:46,  2.07s/it]
 14%|█▍        | 217/1511 [07:32<44:40,  2.07s/it]
 14%|█▍        | 218/1511 [07:34<44:39,  2.07s/it]
 14%|█▍        | 219/1511 [07:36<44:37,  2.07s/it]
 15%|█▍        | 220/1511 [07:38<44:33,  2.07s/it]
 15%|█▍        | 221/1511 [07:40<44:33,  2.07s/it]
 15%|█▍        | 222/1511 [07:42<44:34,  2.07s/it]
 15%|█▍        | 223/1511 [07:44<44:33,  2.08s/it]
 15%|█▍        | 224/1511 [07:46<44:28,  2.07s/it]
 15%|█▍        | 225/1511 [07:48<44:22,  2.07s/it]
                                                  

 15%|█▍        | 225/1511 [07:48<44:22,  2.07s/it]
 15%|█▍        | 226/1511 [07:50<44:21,  2.07s/it]
 15%|█▌        | 227/1511 [07:52<44:18,  2.07s/it]
 15%|█▌        | 228/1511 [07:55<44:18,  2.07s/it]
 15%|█▌        | 229/1511 [07:57<44:17,  2.07s/it]
 15%|█▌        | 230/1511 [07:59<44:17,  2.07s/it]
 15%|█▌        | 231/1511 [08:01<44:11,  2.07s/it]
 15%|█▌        | 232/1511 [08:03<44:09,  2.07s/it]
 15%|█▌        | 233/1511 [08:05<44:09,  2.07s/it]
 15%|█▌        | 234/1511 [08:07<44:08,  2.07s/it]
 16%|█▌        | 235/1511 [08:09<44:04,  2.07s/it]
 16%|█▌        | 236/1511 [08:11<44:07,  2.08s/it]
 16%|█▌        | 237/1511 [08:13<44:10,  2.08s/it]
 16%|█▌        | 238/1511 [08:15<44:02,  2.08s/it]
 16%|█▌        | 239/1511 [08:17<43:58,  2.07s/it]
 16%|█▌        | 240/1511 [08:19<43:54,  2.07s/it]
 16%|█▌        | 241/1511 [08:22<43:53,  2.07s/it]
 16%|█▌        | 242/1511 [08:24<43:54,  2.08s/it]
 16%|█▌        | 243/1511 [08:26<43:46,  2.07s/it]
 16%|█▌        | 244/1511 [08:28<43:43,  2.07s/it]
 16%|█▌        | 245/1511 [08:30<43:42,  2.07s/it]
 16%|█▋        | 246/1511 [08:32<43:39,  2.07s/it]
 16%|█▋        | 247/1511 [08:34<43:41,  2.07s/it]
 16%|█▋        | 248/1511 [08:36<43:38,  2.07s/it]
 16%|█▋        | 249/1511 [08:38<43:35,  2.07s/it]
 17%|█▋        | 250/1511 [08:40<43:40,  2.08s/it]
                                                  

 17%|█▋        | 250/1511 [08:40<43:40,  2.08s/it]
 17%|█▋        | 251/1511 [08:42<43:37,  2.08s/it]
 17%|█▋        | 252/1511 [08:44<43:33,  2.08s/it]
 17%|█▋        | 253/1511 [08:46<43:25,  2.07s/it]
 17%|█▋        | 254/1511 [08:48<43:20,  2.07s/it]
 17%|█▋        | 255/1511 [08:51<43:18,  2.07s/it]
 17%|█▋        | 256/1511 [08:53<43:17,  2.07s/it]
 17%|█▋        | 257/1511 [08:55<43:19,  2.07s/it]
 17%|█▋        | 258/1511 [08:57<43:22,  2.08s/it]
 17%|█▋        | 259/1511 [08:59<43:18,  2.08s/it]
 17%|█▋        | 260/1511 [09:01<43:14,  2.07s/it]
 17%|█▋        | 261/1511 [09:03<43:11,  2.07s/it]
 17%|█▋        | 262/1511 [09:05<43:13,  2.08s/it]
 17%|█▋        | 263/1511 [09:07<43:04,  2.07s/it]
 17%|█▋        | 264/1511 [09:09<42:58,  2.07s/it]
 18%|█▊        | 265/1511 [09:11<42:55,  2.07s/it]
 18%|█▊        | 266/1511 [09:13<42:51,  2.07s/it]
 18%|█▊        | 267/1511 [09:15<42:51,  2.07s/it]
 18%|█▊        | 268/1511 [09:17<42:52,  2.07s/it]
 18%|█▊        | 269/1511 [09:20<42:49,  2.07s/it]
 18%|█▊        | 270/1511 [09:22<42:47,  2.07s/it]
 18%|█▊        | 271/1511 [09:24<42:44,  2.07s/it]
 18%|█▊        | 272/1511 [09:26<42:42,  2.07s/it]
 18%|█▊        | 273/1511 [09:28<42:44,  2.07s/it]
 18%|█▊        | 274/1511 [09:30<42:44,  2.07s/it]
 18%|█▊        | 275/1511 [09:32<42:39,  2.07s/it]
                                                  

 18%|█▊        | 275/1511 [09:32<42:39,  2.07s/it]
 18%|█▊        | 276/1511 [09:34<43:16,  2.10s/it]
 18%|█▊        | 277/1511 [09:36<43:07,  2.10s/it]
 18%|█▊        | 278/1511 [09:38<42:58,  2.09s/it]
 18%|█▊        | 279/1511 [09:40<42:51,  2.09s/it]
 19%|█▊        | 280/1511 [09:42<42:43,  2.08s/it]
 19%|█▊        | 281/1511 [09:45<42:34,  2.08s/it]
 19%|█▊        | 282/1511 [09:47<42:28,  2.07s/it]
 19%|█▊        | 283/1511 [09:49<42:21,  2.07s/it]
 19%|█▉        | 284/1511 [09:51<42:22,  2.07s/it]
 19%|█▉        | 285/1511 [09:53<42:17,  2.07s/it]
 19%|█▉        | 286/1511 [09:55<42:18,  2.07s/it]
 19%|█▉        | 287/1511 [09:57<42:16,  2.07s/it]
 19%|█▉        | 288/1511 [09:59<42:12,  2.07s/it]
 19%|█▉        | 289/1511 [10:01<42:09,  2.07s/it]
 19%|█▉        | 290/1511 [10:03<42:07,  2.07s/it]
 19%|█▉        | 291/1511 [10:05<42:07,  2.07s/it]
 19%|█▉        | 292/1511 [10:07<42:07,  2.07s/it]
 19%|█▉        | 293/1511 [10:09<42:05,  2.07s/it]
 19%|█▉        | 294/1511 [10:11<42:00,  2.07s/it]
 20%|█▉        | 295/1511 [10:13<41:56,  2.07s/it]
 20%|█▉        | 296/1511 [10:16<41:54,  2.07s/it]
 20%|█▉        | 297/1511 [10:18<41:53,  2.07s/it]
 20%|█▉        | 298/1511 [10:20<41:54,  2.07s/it]
 20%|█▉        | 299/1511 [10:22<41:52,  2.07s/it]
 20%|█▉        | 300/1511 [10:24<41:51,  2.07s/it]
                                                  

 20%|█▉        | 300/1511 [10:24<41:51,  2.07s/it]
 20%|█▉        | 301/1511 [10:26<41:47,  2.07s/it]
 20%|█▉        | 302/1511 [10:28<41:47,  2.07s/it]
 20%|██        | 303/1511 [10:30<41:42,  2.07s/it]
 20%|██        | 304/1511 [10:32<41:38,  2.07s/it]
 20%|██        | 305/1511 [10:34<41:34,  2.07s/it]
 20%|██        | 306/1511 [10:36<41:31,  2.07s/it]
 20%|██        | 307/1511 [10:38<41:28,  2.07s/it]
 20%|██        | 308/1511 [10:40<41:26,  2.07s/it]
 20%|██        | 309/1511 [10:42<41:24,  2.07s/it]
 21%|██        | 310/1511 [10:45<41:25,  2.07s/it]
 21%|██        | 311/1511 [10:47<41:24,  2.07s/it]
 21%|██        | 312/1511 [10:49<41:22,  2.07s/it]
 21%|██        | 313/1511 [10:51<41:21,  2.07s/it]
 21%|██        | 314/1511 [10:53<41:18,  2.07s/it]
 21%|██        | 315/1511 [10:55<41:14,  2.07s/it]
 21%|██        | 316/1511 [10:57<41:11,  2.07s/it]
 21%|██        | 317/1511 [10:59<41:09,  2.07s/it]
 21%|██        | 318/1511 [11:01<41:09,  2.07s/it]
 21%|██        | 319/1511 [11:03<41:06,  2.07s/it]
 21%|██        | 320/1511 [11:05<41:02,  2.07s/it]
 21%|██        | 321/1511 [11:07<41:01,  2.07s/it]
 21%|██▏       | 322/1511 [11:09<40:58,  2.07s/it]
 21%|██▏       | 323/1511 [11:11<40:55,  2.07s/it]
 21%|██▏       | 324/1511 [11:14<40:56,  2.07s/it]
 22%|██▏       | 325/1511 [11:16<40:55,  2.07s/it]
                                                  

 22%|██▏       | 325/1511 [11:16<40:55,  2.07s/it]
 22%|██▏       | 326/1511 [11:18<40:52,  2.07s/it]
 22%|██▏       | 327/1511 [11:20<40:51,  2.07s/it]
 22%|██▏       | 328/1511 [11:22<40:45,  2.07s/it]
 22%|██▏       | 329/1511 [11:24<40:42,  2.07s/it]
 22%|██▏       | 330/1511 [11:26<40:42,  2.07s/it]
 22%|██▏       | 331/1511 [11:28<40:42,  2.07s/it]
 22%|██▏       | 332/1511 [11:30<40:40,  2.07s/it]
 22%|██▏       | 333/1511 [11:32<40:38,  2.07s/it]
 22%|██▏       | 334/1511 [11:34<40:35,  2.07s/it]
 22%|██▏       | 335/1511 [11:36<40:37,  2.07s/it]
 22%|██▏       | 336/1511 [11:38<40:33,  2.07s/it]
 22%|██▏       | 337/1511 [11:40<40:31,  2.07s/it]
 22%|██▏       | 338/1511 [11:42<40:28,  2.07s/it]
 22%|██▏       | 339/1511 [11:45<40:28,  2.07s/it]
 23%|██▎       | 340/1511 [11:47<40:28,  2.07s/it]
 23%|██▎       | 341/1511 [11:49<40:25,  2.07s/it]
 23%|██▎       | 342/1511 [11:51<40:27,  2.08s/it]
 23%|██▎       | 343/1511 [11:53<40:21,  2.07s/it]
 23%|██▎       | 344/1511 [11:55<40:17,  2.07s/it]
 23%|██▎       | 345/1511 [11:57<40:12,  2.07s/it]
 23%|██▎       | 346/1511 [11:59<40:09,  2.07s/it]
 23%|██▎       | 347/1511 [12:01<40:09,  2.07s/it]
 23%|██▎       | 348/1511 [12:03<40:04,  2.07s/it]
 23%|██▎       | 349/1511 [12:05<40:04,  2.07s/it]
 23%|██▎       | 350/1511 [12:07<40:04,  2.07s/it]
                                                  

 23%|██▎       | 350/1511 [12:07<40:04,  2.07s/it]
 23%|██▎       | 351/1511 [12:09<40:04,  2.07s/it]
 23%|██▎       | 352/1511 [12:11<39:59,  2.07s/it]
 23%|██▎       | 353/1511 [12:14<39:57,  2.07s/it]
 23%|██▎       | 354/1511 [12:16<39:55,  2.07s/it]
 23%|██▎       | 355/1511 [12:18<39:55,  2.07s/it]
 24%|██▎       | 356/1511 [12:20<39:55,  2.07s/it]
 24%|██▎       | 357/1511 [12:22<39:51,  2.07s/it]
 24%|██▎       | 358/1511 [12:24<39:51,  2.07s/it]
 24%|██▍       | 359/1511 [12:26<39:48,  2.07s/it]
 24%|██▍       | 360/1511 [12:28<39:44,  2.07s/it]
 24%|██▍       | 361/1511 [12:30<39:41,  2.07s/it]
 24%|██▍       | 362/1511 [12:32<39:38,  2.07s/it]
 24%|██▍       | 363/1511 [12:34<39:38,  2.07s/it]
 24%|██▍       | 364/1511 [12:36<39:35,  2.07s/it]
 24%|██▍       | 365/1511 [12:38<39:34,  2.07s/it]
 24%|██▍       | 366/1511 [12:40<39:31,  2.07s/it]
 24%|██▍       | 367/1511 [12:43<39:26,  2.07s/it]
 24%|██▍       | 368/1511 [12:45<39:23,  2.07s/it]
 24%|██▍       | 369/1511 [12:47<39:22,  2.07s/it]
 24%|██▍       | 370/1511 [12:49<39:19,  2.07s/it]
 25%|██▍       | 371/1511 [12:51<39:16,  2.07s/it]
 25%|██▍       | 372/1511 [12:53<39:14,  2.07s/it]
 25%|██▍       | 373/1511 [12:55<39:16,  2.07s/it]
 25%|██▍       | 374/1511 [12:57<39:14,  2.07s/it]
 25%|██▍       | 375/1511 [12:59<39:09,  2.07s/it]
                                                  

 25%|██▍       | 375/1511 [12:59<39:09,  2.07s/it]
 25%|██▍       | 376/1511 [13:01<39:08,  2.07s/it]
 25%|██▍       | 377/1511 [13:03<39:12,  2.07s/it]
 25%|██▌       | 378/1511 [13:05<39:11,  2.08s/it]
 25%|██▌       | 379/1511 [13:07<39:06,  2.07s/it]
 25%|██▌       | 380/1511 [13:09<39:02,  2.07s/it]
 25%|██▌       | 381/1511 [13:12<39:01,  2.07s/it]
 25%|██▌       | 382/1511 [13:14<38:59,  2.07s/it]
 25%|██▌       | 383/1511 [13:16<38:55,  2.07s/it]
 25%|██▌       | 384/1511 [13:18<38:53,  2.07s/it]
 25%|██▌       | 385/1511 [13:20<38:50,  2.07s/it]
 26%|██▌       | 386/1511 [13:22<38:45,  2.07s/it]
 26%|██▌       | 387/1511 [13:24<38:47,  2.07s/it]
 26%|██▌       | 388/1511 [13:26<38:43,  2.07s/it]
 26%|██▌       | 389/1511 [13:28<38:42,  2.07s/it]
 26%|██▌       | 390/1511 [13:30<38:41,  2.07s/it]
 26%|██▌       | 391/1511 [13:32<38:39,  2.07s/it]
 26%|██▌       | 392/1511 [13:34<39:08,  2.10s/it]
 26%|██▌       | 393/1511 [13:36<38:58,  2.09s/it]
 26%|██▌       | 394/1511 [13:39<39:03,  2.10s/it]
 26%|██▌       | 395/1511 [13:41<38:51,  2.09s/it]
 26%|██▌       | 396/1511 [13:43<38:45,  2.09s/it]
 26%|██▋       | 397/1511 [13:45<38:37,  2.08s/it]
 26%|██▋       | 398/1511 [13:47<38:32,  2.08s/it]
 26%|██▋       | 399/1511 [13:49<38:25,  2.07s/it]
 26%|██▋       | 400/1511 [13:51<38:23,  2.07s/it]
                                                  

 26%|██▋       | 400/1511 [13:51<38:23,  2.07s/it]
 27%|██▋       | 401/1511 [13:53<38:22,  2.07s/it]
 27%|██▋       | 402/1511 [13:55<38:16,  2.07s/it]
 27%|██▋       | 403/1511 [13:57<38:16,  2.07s/it]
 27%|██▋       | 404/1511 [13:59<38:19,  2.08s/it]
 27%|██▋       | 405/1511 [14:01<38:14,  2.07s/it]
 27%|██▋       | 406/1511 [14:03<38:08,  2.07s/it]
 27%|██▋       | 407/1511 [14:06<38:10,  2.07s/it]
 27%|██▋       | 408/1511 [14:08<38:08,  2.07s/it]
 27%|██▋       | 409/1511 [14:10<38:08,  2.08s/it]
 27%|██▋       | 410/1511 [14:12<38:05,  2.08s/it]
 27%|██▋       | 411/1511 [14:14<37:58,  2.07s/it]
 27%|██▋       | 412/1511 [14:16<37:55,  2.07s/it]
 27%|██▋       | 413/1511 [14:18<37:52,  2.07s/it]
 27%|██▋       | 414/1511 [14:20<37:52,  2.07s/it]
 27%|██▋       | 415/1511 [14:22<37:48,  2.07s/it]
 28%|██▊       | 416/1511 [14:24<37:50,  2.07s/it]
 28%|██▊       | 417/1511 [14:26<37:47,  2.07s/it]
 28%|██▊       | 418/1511 [14:28<37:42,  2.07s/it]
 28%|██▊       | 419/1511 [14:30<37:46,  2.08s/it]
 28%|██▊       | 420/1511 [14:32<37:42,  2.07s/it]
 28%|██▊       | 421/1511 [14:35<37:44,  2.08s/it]
 28%|██▊       | 422/1511 [14:37<37:43,  2.08s/it]
 28%|██▊       | 423/1511 [14:39<37:36,  2.07s/it]
 28%|██▊       | 424/1511 [14:41<37:36,  2.08s/it]
 28%|██▊       | 425/1511 [14:43<37:31,  2.07s/it]
                                                  

 28%|██▊       | 425/1511 [14:43<37:31,  2.07s/it]
 28%|██▊       | 426/1511 [14:45<37:30,  2.07s/it]
 28%|██▊       | 427/1511 [14:47<37:24,  2.07s/it]
 28%|██▊       | 428/1511 [14:49<37:20,  2.07s/it]
 28%|██▊       | 429/1511 [14:51<37:25,  2.07s/it]
 28%|██▊       | 430/1511 [14:53<37:18,  2.07s/it]
 29%|██▊       | 431/1511 [14:55<37:51,  2.10s/it]
 29%|██▊       | 432/1511 [14:57<37:49,  2.10s/it]
 29%|██▊       | 433/1511 [15:00<37:35,  2.09s/it]
 29%|██▊       | 434/1511 [15:02<37:30,  2.09s/it]
 29%|██▉       | 435/1511 [15:04<37:19,  2.08s/it]
 29%|██▉       | 436/1511 [15:06<37:15,  2.08s/it]
 29%|██▉       | 437/1511 [15:08<37:13,  2.08s/it]
 29%|██▉       | 438/1511 [15:10<37:12,  2.08s/it]
 29%|██▉       | 439/1511 [15:12<37:06,  2.08s/it]
 29%|██▉       | 440/1511 [15:14<37:03,  2.08s/it]
 29%|██▉       | 441/1511 [15:16<37:03,  2.08s/it]
 29%|██▉       | 442/1511 [15:18<36:59,  2.08s/it]
 29%|██▉       | 443/1511 [15:20<36:58,  2.08s/it]
 29%|██▉       | 444/1511 [15:22<36:53,  2.07s/it]
 29%|██▉       | 445/1511 [15:24<36:49,  2.07s/it]
 30%|██▉       | 446/1511 [15:27<36:44,  2.07s/it]
 30%|██▉       | 447/1511 [15:29<36:44,  2.07s/it]
 30%|██▉       | 448/1511 [15:31<36:43,  2.07s/it]
 30%|██▉       | 449/1511 [15:33<36:41,  2.07s/it]Error executing job with overrides: []
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 183, in main
    trainer.train()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/trainer.py", line 2123, in train
    return inner_training_loop(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/trainer.py", line 2481, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/trainer.py", line 3612, in training_step
    self.accelerator.backward(loss, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/accelerator.py", line 2465, in backward
    self.deepspeed_engine_wrapped.backward(loss, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 266, in backward
    self.engine.backward(loss, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/utils/nvtx.py", line 18, in wrapped_fn
    ret_val = func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2020, in backward
    self.optimizer.backward(loss, retain_graph=retain_graph)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py", line 2058, in backward
    self.loss_scaler.backward(loss.float(), retain_graph=retain_graph)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/fp16/loss_scaler.py", line 63, in backward
    scaled_loss.backward(retain_graph=retain_graph)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/autograd/__init__.py", line 353, in backward
    _engine_run_backward(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py", line 909, in reduce_partition_and_remove_grads
    self.reduce_ready_partitions_and_remove_grads(param, i)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py", line 1418, in reduce_ready_partitions_and_remove_grads
    self.reduce_independent_p_g_buckets_and_remove_grads(param, i)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py", line 938, in reduce_independent_p_g_buckets_and_remove_grads
    self.reduce_ipg_grads()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py", line 1405, in reduce_ipg_grads
    self.copy_grads_in_partition(param)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/deepspeed/runtime/zero/stage_1_and_2.py", line 1345, in copy_grads_in_partition
    self.grads_in_partition = torch.empty(int(total_size),
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 898.00 MiB. GPU 2 has a total capacity of 44.64 GiB of which 319.19 MiB is free. Process 821612 has 4.11 GiB memory in use. Process 821613 has 4.11 GiB memory in use. Including non-PyTorch memory, this process has 31.97 GiB memory in use. Process 821615 has 4.11 GiB memory in use. Of the allocated memory 27.86 GiB is allocated by PyTorch, and 3.46 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
W0624 20:15:18.520000 821506 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 821612 closing signal SIGTERM
W0624 20:15:18.521000 821506 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 821613 closing signal SIGTERM
W0624 20:15:18.521000 821506 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 821615 closing signal SIGTERM
E0624 20:15:19.401000 821506 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 2 (pid: 821614) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
exmp_1.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-06-24_20:15:18
  host      : cn-l044.server.mila.quebec
  rank      : 2 (local_rank: 2)
  exitcode  : 1 (pid: 821614)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
