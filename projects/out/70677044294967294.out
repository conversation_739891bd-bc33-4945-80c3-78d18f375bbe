[2025-06-26 14:22:07,007] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:22:11,949] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
Configuration:
Configuration:
Configuration:
Configuration:
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

[2025-06-26 14:22:19,635] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:22:19,660] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:22:19,661] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:22:19,676] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:22:22,803] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-26 14:22:22,814] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-26 14:22:22,815] [INFO] [comm.py:706:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-06-26 14:22:22,938] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-26 14:22:22,948] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-26 14:22:22,948] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-26 14:22:22,949] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-26 14:22:22,958] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-26 14:22:22,959] [INFO] [comm.py:675:init_distributed] cdb=None
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-26 14:22:45,051][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 14:22:45,066][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 14:22:45,070][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 14:22:45,070][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480count_averaged_layers : 195

count_router_layers : 480
count_total_router_layers : 480count_total_router_layers : 480

The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
Loading model...
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
cn-g012:2500222:2500222 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2500222:2500222 [0] NCCL INFO cudaDriverVersion 12020
cn-g012:2500222:2500222 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2500222:2500222 [0] NCCL INFO Comm config Blocking set to 1
cn-g012:2500223:2500223 [1] NCCL INFO cudaDriverVersion 12020
cn-g012:2500223:2500223 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2500225:2500225 [3] NCCL INFO cudaDriverVersion 12020
cn-g012:2500223:2500223 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2500224:2500224 [2] NCCL INFO cudaDriverVersion 12020
cn-g012:2500225:2500225 [3] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2500224:2500224 [2] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2500225:2500225 [3] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2500224:2500224 [2] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2500223:2500223 [1] NCCL INFO Comm config Blocking set to 1
cn-g012:2500224:2500224 [2] NCCL INFO Comm config Blocking set to 1
cn-g012:2500225:2500225 [3] NCCL INFO Comm config Blocking set to 1
cn-g012:2500223:2501149 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2500222:2501148 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2500224:2501150 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2500225:2501152 [3] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2500222:2501148 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2500222:2501148 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2500222:2501148 [0] NCCL INFO Using network IB
cn-g012:2500223:2501149 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2500223:2501149 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2500223:2501149 [1] NCCL INFO Using network IB
cn-g012:2500222:2501148 [0] NCCL INFO ncclCommInitRankConfig comm 0x55c7682f43c0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xf55e45ffdeb478cb - Init START
cn-g012:2500223:2501149 [1] NCCL INFO ncclCommInitRankConfig comm 0x55559af88190 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xf55e45ffdeb478cb - Init START
cn-g012:2500224:2501150 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2500224:2501150 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2500224:2501150 [2] NCCL INFO Using network IB
cn-g012:2500224:2501150 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a024b7c9c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xf55e45ffdeb478cb - Init START
cn-g012:2500223:2501149 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2500225:2501152 [3] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2500225:2501152 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2500225:2501152 [3] NCCL INFO Using network IB
cn-g012:2500225:2501152 [3] NCCL INFO ncclCommInitRankConfig comm 0x55cc2789c650 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xf55e45ffdeb478cb - Init START
cn-g012:2500224:2501150 [2] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2500222:2501148 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2500225:2501152 [3] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2500225:2501152 [3] NCCL INFO Bootstrap timings total 0.000965 (create 0.000042, send 0.000148, recv 0.000148, ring 0.000044, delay 0.000000)
cn-g012:2500222:2501148 [0] NCCL INFO Bootstrap timings total 0.020260 (create 0.000039, send 0.000144, recv 0.001602, ring 0.000159, delay 0.000000)
cn-g012:2500224:2501150 [2] NCCL INFO Bootstrap timings total 0.015176 (create 0.000042, send 0.000123, recv 0.014288, ring 0.000266, delay 0.000000)
cn-g012:2500223:2501149 [1] NCCL INFO Bootstrap timings total 0.018828 (create 0.000039, send 0.000229, recv 0.003633, ring 0.014448, delay 0.000000)
cn-g012:2500222:2501148 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g012:2500225:2501152 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g012:2500224:2501150 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g012:2500223:2501149 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g012:2500223:2501149 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g012:2500222:2501148 [0] NCCL INFO comm 0x55c7682f43c0 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g012:2500224:2501150 [2] NCCL INFO comm 0x55a024b7c9c0 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g012:2500225:2501152 [3] NCCL INFO comm 0x55cc2789c650 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g012:2500223:2501149 [1] NCCL INFO comm 0x55559af88190 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g012:2500222:2501148 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g012:2500224:2501150 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g012:2500224:2501150 [2] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500225:2501152 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g012:2500225:2501152 [3] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500223:2501149 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g012:2500223:2501149 [1] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500222:2501148 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g012:2500222:2501148 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g012:2500222:2501148 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g012:2500222:2501148 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g012:2500222:2501148 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g012:2500222:2501148 [0] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500224:2501167 [2] NCCL INFO [Proxy Service] Device 2 CPU core 15
cn-g012:2500225:2501169 [3] NCCL INFO [Proxy Service] Device 3 CPU core 7
cn-g012:2500224:2501168 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 3
cn-g012:2500225:2501170 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 9
cn-g012:2500222:2501148 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g012:2500222:2501171 [0] NCCL INFO [Proxy Service] Device 0 CPU core 6
cn-g012:2500222:2501172 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 1
cn-g012:2500223:2501174 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 10
cn-g012:2500223:2501173 [1] NCCL INFO [Proxy Service] Device 1 CPU core 5
cn-g012:2500224:2501150 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500224:2501150 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500225:2501152 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500225:2501152 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500222:2501148 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500222:2501148 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500222:2501148 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g012:2500223:2501149 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500223:2501149 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500222:2501148 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2500222:2501148 [0] NCCL INFO ncclCommInitRankConfig comm 0x55c7682f43c0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xf55e45ffdeb478cb - Init COMPLETE
cn-g012:2500224:2501150 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2500223:2501149 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2500222:2501148 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 0.35 (kernels 0.17, alloc 0.04, bootstrap 0.02, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2500225:2501152 [3] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2500225:2501152 [3] NCCL INFO ncclCommInitRankConfig comm 0x55cc2789c650 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xf55e45ffdeb478cb - Init COMPLETE
cn-g012:2500224:2501150 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a024b7c9c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xf55e45ffdeb478cb - Init COMPLETE
cn-g012:2500223:2501149 [1] NCCL INFO ncclCommInitRankConfig comm 0x55559af88190 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xf55e45ffdeb478cb - Init COMPLETE
cn-g012:2500223:2501149 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.35 (kernels 0.16, alloc 0.04, bootstrap 0.02, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2500225:2501152 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.35 (kernels 0.17, alloc 0.05, bootstrap 0.00, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2500224:2501150 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.35 (kernels 0.17, alloc 0.03, bootstrap 0.02, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2500225:2501177 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501178 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501175 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501177 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500224:2501176 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2500225:2501177 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2500223:2501175 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2500222:2501178 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-06-26 14:24:23,801] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed info: version=0.17.1, git-hash=unknown, git-branch=unknown
[2025-06-26 14:24:23,802] [INFO] [config.py:655:__init__] Config mesh_device None world_size = 4
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
[2025-06-26 14:24:25,443] [INFO] [engine.py:1325:_configure_distributed_model] ********** distributed groups summary **********
	 self.dp_world_size=4
	 self.mp_world_size=1
	 self.seq_dp_world_size=4
	 self.sequence_parallel_size=1
***********************************************
cn-g012:2500222:2500222 [0] NCCL INFO Comm config Blocking set to 1
cn-g012:2500222:2501238 [0] NCCL INFO Using network IB
cn-g012:2500222:2501238 [0] NCCL INFO ncclCommInitRankConfig comm 0x55c7692f44c0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xe615b2e7d25bdc03 - Init START
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
cn-g012:2500223:2500223 [1] NCCL INFO Comm config Blocking set to 1
cn-g012:2500223:2501247 [1] NCCL INFO Using network IB
cn-g012:2500223:2501247 [1] NCCL INFO ncclCommInitRankConfig comm 0x5555aa4b9280 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xe615b2e7d25bdc03 - Init START
cn-g012:2500225:2500225 [3] NCCL INFO Comm config Blocking set to 1
cn-g012:2500225:2501253 [3] NCCL INFO Using network IB
cn-g012:2500225:2501253 [3] NCCL INFO ncclCommInitRankConfig comm 0x55cbbbdc8440 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xe615b2e7d25bdc03 - Init START
cn-g012:2500224:2500224 [2] NCCL INFO Comm config Blocking set to 1
cn-g012:2500224:2501256 [2] NCCL INFO Using network IB
cn-g012:2500224:2501256 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a02533c9c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xe615b2e7d25bdc03 - Init START
cn-g012:2500225:2501253 [3] NCCL INFO Bootstrap timings total 0.029091 (create 0.000041, send 0.000085, recv 0.000082, ring 0.000116, delay 0.000000)
cn-g012:2500224:2501256 [2] NCCL INFO Bootstrap timings total 0.000480 (create 0.000043, send 0.000098, recv 0.000106, ring 0.000070, delay 0.000000)
cn-g012:2500222:2501238 [0] NCCL INFO Bootstrap timings total 2.777246 (create 0.000033, send 0.000106, recv 2.535231, ring 0.028698, delay 0.000000)
cn-g012:2500223:2501247 [1] NCCL INFO Bootstrap timings total 0.242201 (create 0.000048, send 0.000107, recv 0.241829, ring 0.000040, delay 0.000000)
cn-g012:2500225:2501253 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g012:2500222:2501238 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g012:2500224:2501256 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g012:2500223:2501247 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g012:2500223:2501247 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g012:2500225:2501253 [3] NCCL INFO comm 0x55cbbbdc8440 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g012:2500224:2501256 [2] NCCL INFO comm 0x55a02533c9c0 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g012:2500223:2501247 [1] NCCL INFO comm 0x5555aa4b9280 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g012:2500222:2501238 [0] NCCL INFO comm 0x55c7692f44c0 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g012:2500225:2501253 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g012:2500225:2501253 [3] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500224:2501256 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g012:2500224:2501256 [2] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500223:2501247 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g012:2500223:2501247 [1] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500222:2501238 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g012:2500222:2501238 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g012:2500222:2501238 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g012:2500222:2501238 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g012:2500222:2501238 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g012:2500222:2501238 [0] NCCL INFO P2P Chunksize set to 524288
cn-g012:2500225:2501257 [3] NCCL INFO [Proxy Service] Device 3 CPU core 12
cn-g012:2500223:2501258 [1] NCCL INFO [Proxy Service] Device 1 CPU core 5
cn-g012:2500222:2501238 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g012:2500225:2501259 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 10
cn-g012:2500223:2501260 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 1
cn-g012:2500222:2501261 [0] NCCL INFO [Proxy Service] Device 0 CPU core 9
cn-g012:2500222:2501262 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 7
cn-g012:2500224:2501263 [2] NCCL INFO [Proxy Service] Device 2 CPU core 6
cn-g012:2500224:2501264 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 7
cn-g012:2500225:2501253 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500225:2501253 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500224:2501256 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500224:2501256 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500223:2501247 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500223:2501247 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500222:2501238 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2500222:2501238 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2500222:2501238 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g012:2500225:2501253 [3] NCCL INFO ncclCommInitRankConfig comm 0x55cbbbdc8440 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xe615b2e7d25bdc03 - Init COMPLETE
cn-g012:2500223:2501247 [1] NCCL INFO ncclCommInitRankConfig comm 0x5555aa4b9280 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xe615b2e7d25bdc03 - Init COMPLETE
cn-g012:2500224:2501256 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a02533c9c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xe615b2e7d25bdc03 - Init COMPLETE
cn-g012:2500224:2501256 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.14 (kernels 0.00, alloc 0.00, bootstrap 0.00, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.03)
cn-g012:2500222:2501238 [0] NCCL INFO ncclCommInitRankConfig comm 0x55c7692f44c0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xe615b2e7d25bdc03 - Init COMPLETE
cn-g012:2500225:2501253 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.17 (kernels 0.00, alloc 0.00, bootstrap 0.03, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.02)
cn-g012:2500223:2501247 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.38 (kernels 0.00, alloc 0.00, bootstrap 0.24, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.06, rest 0.02)
cn-g012:2500222:2501238 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 2.92 (kernels 0.00, alloc 0.00, bootstrap 2.78, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.06, rest 0.02)
cn-g012:2500224:2501265 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2500223:2501267 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500224:2501265 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2500222:2501268 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2500225:2501266 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2500224:2501265 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2500223:2501267 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2500222:2501268 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
[2025-06-26 14:24:28,519] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-06-26 14:24:28,523] [INFO] [logging.py:107:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-06-26 14:24:28,523] [INFO] [logging.py:107:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-06-26 14:24:28,543] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-06-26 14:24:28,543] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-06-26 14:24:28,544] [INFO] [logging.py:107:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:151:__init__] Reduce bucket size 5********
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:152:__init__] Allgather bucket size 2********
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:153:__init__] CPU Offload: False
[2025-06-26 14:24:28,544] [INFO] [stage_1_and_2.py:154:__init__] Round robin gradient partitioning: False
[2025-06-26 14:24:32,211] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-06-26 14:24:32,213] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB 
[2025-06-26 14:24:32,213] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 38.21 GB, percent = 3.8%
[2025-06-26 14:24:32,412] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-06-26 14:24:32,413] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-06-26 14:24:32,414] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 38.13 GB, percent = 3.8%
[2025-06-26 14:24:32,414] [INFO] [stage_1_and_2.py:573:__init__] optimizer state initialized
[2025-06-26 14:24:32,594] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-06-26 14:24:32,595] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-06-26 14:24:32,595] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 38.13 GB, percent = 3.8%
[2025-06-26 14:24:32,597] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-06-26 14:24:32,598] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-06-26 14:24:32,598] [INFO] [logging.py:107:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-06-26 14:24:32,598] [INFO] [logging.py:107:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-06-26 14:24:32,602] [INFO] [logging.py:107:log_dist] [Rank 0] [TorchCheckpointEngine] Initialized with serialization = True
[2025-06-26 14:24:32,602] [INFO] [config.py:921:print] DeepSpeedEngine configuration:
[2025-06-26 14:24:32,602] [INFO] [config.py:925:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'intra_op_parallelism': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   amp_enabled .................. False
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   amp_params ................... False
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-06-26 14:24:32,603] [INFO] [config.py:925:print]   bfloat16_config .............. enabled=True immediate_grad_update=False check_grad_overflow=False
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_config ............ {'tag_validation': 'WARN', 'checkpoint_serialization': True, 'writer': None}
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_parallel_write_pipeline  False
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_tag_validation_enabled  True
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   checkpoint_tag_validation_fail  False
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f8ec5f722c0>
[2025-06-26 14:24:32,604] [INFO] [config.py:925:print]   communication_data_type ...... None
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   compile_config ............... deepcompile=False free_activation=False offload_activation=False offload_opt_states=False double_buffer=True symmetric_memory=False debug_log=False offload_parameters=False sync_before_reduce=False sync_after_reduce=False sync_before_allgather=False sync_after_allgather=False
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   curriculum_enabled_legacy .... False
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   curriculum_params_legacy ..... False
[2025-06-26 14:24:32,605] [INFO] [config.py:925:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'pin_memory': False, 'curriculum_learning': {'enabled': False}, 'dynamic_batching': {'enabled': False, 'lr_scaling_method': 'linear', 'min_batch_size': 1, 'max_batch_size': None, 'sequence_picking_order': 'dataloader', 'verbose': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   data_efficiency_enabled ...... False
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   dataloader_drop_last ......... False
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   disable_allgather ............ False
[2025-06-26 14:24:32,606] [INFO] [config.py:925:print]   dump_state ................... False
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_enabled ........... False
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_gas_boundary_resolution  1
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_layer_num ......... 0
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_max_iter .......... 100
[2025-06-26 14:24:32,607] [INFO] [config.py:925:print]   eigenvalue_stability ......... 1e-06
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   eigenvalue_tol ............... 0.01
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   eigenvalue_verbose ........... False
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   elasticity_enabled ........... False
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   float16_config ............... enabled=False auto_cast=False loss_scale=0.0 initial_scale_power=16 loss_scale_window=1000 hysteresis=2 consecutive_hysteresis=False min_loss_scale=1 fp16_master_weights_and_grads=False
[2025-06-26 14:24:32,608] [INFO] [config.py:925:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   global_rank .................. 0
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   grad_accum_dtype ............. None
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   gradient_accumulation_steps .. 4
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   gradient_clipping ............ 1.0
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   gradient_predivide_factor .... 1.0
[2025-06-26 14:24:32,609] [INFO] [config.py:925:print]   graph_harvesting ............. False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   load_universal_checkpoint .... False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   memory_breakdown ............. False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   mics_hierarchial_params_gather  False
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   mics_shard_size .............. -1
[2025-06-26 14:24:32,610] [INFO] [config.py:925:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   optimizer_legacy_fusion ...... False
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   optimizer_name ............... None
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   optimizer_params ............. None
[2025-06-26 14:24:32,611] [INFO] [config.py:925:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   pld_enabled .................. False
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   pld_params ................... False
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   prescale_gradients ........... False
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   scheduler_name ............... None
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   scheduler_params ............. None
[2025-06-26 14:24:32,612] [INFO] [config.py:925:print]   seq_parallel_communication_data_type  torch.float32
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   sparse_attention ............. None
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   sparse_gradients_enabled ..... False
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   steps_per_print .............. inf
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   tensor_parallel_config ....... dtype=torch.float16 autotp_size=0 tp_overlap_comm=False tensor_parallel=TPConfig(tp_size=1, tp_grain_size=1, mpu=None, tp_group=None) injection_policy_tuple=None keep_module_on_host=False replace_with_kernel_inject=False
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   timers_config ................ enabled=True synchronized=True
[2025-06-26 14:24:32,613] [INFO] [config.py:925:print]   train_batch_size ............. 16
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   train_micro_batch_size_per_gpu  1
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   use_data_before_expert_parallel_  False
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   use_node_local_storage ....... False
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   wall_clock_breakdown ......... False
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   weight_quantization_config ... None
[2025-06-26 14:24:32,614] [INFO] [config.py:925:print]   world_size ................... 4
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_allow_untested_optimizer  True
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=5******** use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=2******** overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1********0 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1********0 max_reuse_distance=1********0 gather_16bit_weights_on_model_save=False module_granularity_threshold=0 use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False zeropp_loco_param=None mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True log_trace_cache_warnings=False
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_enabled ................. True
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_force_ds_cpu_optimizer .. True
[2025-06-26 14:24:32,615] [INFO] [config.py:925:print]   zero_optimization_stage ...... 2
[2025-06-26 14:24:32,616] [INFO] [config.py:911:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2, 
        "allgather_partitions": true, 
        "allgather_bucket_size": 2.000000e+08, 
        "overlap_comm": true, 
        "reduce_scatter": true, 
        "contiguous_gradients": true
    }, 
    "gradient_accumulation_steps": 4, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 16, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "bf16": {
        "enabled": true
    }, 
    "fp16": {
        "enabled": false
    }, 
    "zero_allow_untested_optimizer": true
}
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mmistral-drug_abuse_pubmedqau-moe[0m at: [34mhttps://wandb.ai/sarath-chandar/mistral-moe-training/runs/j29hoxlo[0m
[1;34mwandb[0m: Find logs at: [1;35m../../../../../../../../network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250626_142223-j29hoxlo/logs[0m
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500224:2501167 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500225:2501169 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500224:2501299 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500224:2501167 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500225:2501301 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500225:2501169 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500223:2501173 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500224:2501167 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500225:2501169 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500222:2501171 [0] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500223:2501173 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500224:2501167 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2500225:2501169 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2500222:2501312 [0] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2500223:2501303 [1] NCCL INFO comm 0x55559af88190 rank 1 nranks 4 cudaDev 1 busId 41000 - Abort COMPLETE
cn-g012:2500224:2501299 [2] NCCL INFO comm 0x55a024b7c9c0 rank 2 nranks 4 cudaDev 2 busId 81000 - Abort COMPLETE
cn-g012:2500225:2501301 [3] NCCL INFO comm 0x55cc2789c650 rank 3 nranks 4 cudaDev 3 busId c1000 - Abort COMPLETE
cn-g012:2500222:2501312 [0] NCCL INFO comm 0x55c7682f43c0 rank 0 nranks 4 cudaDev 0 busId 1000 - Abort COMPLETE
cn-g012:2500222:2501261 [0] NCCL INFO [Service thread] Connection closed by localRank 1
cn-g012:2500222:2501261 [0] NCCL INFO [Service thread] Connection closed by localRank 2
cn-g012:2500222:2501261 [0] NCCL INFO [Service thread] Connection closed by localRank 3
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Thu Jun 26 14:24:37 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2500223
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 135995 ms
            Is Running                    : 0
        Process ID                        : 2500224
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 136317 ms
            Is Running                    : 0
        Process ID                        : 2500225
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 136502 ms
            Is Running                    : 0
        Process ID                        : 2500222
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 24258 MiB
            Time                          : 136750 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2500223
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 24862 MiB
            Time                          : 135992 ms
            Is Running                    : 0
        Process ID                        : 2500224
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 136313 ms
            Is Running                    : 0
        Process ID                        : 2500225
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 136501 ms
            Is Running                    : 0
        Process ID                        : 2500222
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 136748 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2500223
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 135990 ms
            Is Running                    : 0
        Process ID                        : 2500224
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 24862 MiB
            Time                          : 136310 ms
            Is Running                    : 0
        Process ID                        : 2500225
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 136499 ms
            Is Running                    : 0
        Process ID                        : 2500222
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 136746 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2500223
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 135987 ms
            Is Running                    : 0
        Process ID                        : 2500224
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 136308 ms
            Is Running                    : 0
        Process ID                        : 2500225
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 24258 MiB
            Time                          : 136497 ms
            Is Running                    : 0
        Process ID                        : 2500222
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 136745 ms
            Is Running                    : 0

Thu Jun 26 14:24:37 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   33C    P0             117W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   27C    P0              85W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   34C    P0             119W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   31C    P0             115W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
