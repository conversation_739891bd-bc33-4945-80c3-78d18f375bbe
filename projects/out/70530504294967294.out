[2025-06-24 13:50:50,548] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:50:53,786] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

Configuration:
Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

Configuration:
experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe

[2025-06-24 13:51:00,097] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:51:00,097] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:51:00,101] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:51:00,101] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-24 13:51:03,285] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:51:03,287] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:51:03,296] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-24 13:51:03,297] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:51:03,299] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-24 13:51:03,308] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-24 13:51:03,309] [INFO] [comm.py:706:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-06-24 13:51:03,339] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-24 13:51:03,351] [INFO] [comm.py:675:init_distributed] cdb=None
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-24 13:51:15,626][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 13:51:15,660][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-24 13:51:15,827][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
[2025-06-24 13:51:16,367][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 227
count_router_layers : 320
count_total_router_layers : 320
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe
checkpoint saved at data/mistral_lora_moe
checkpoint saved at data/mistral_lora_moe
Loading model...
Total weights: 611, Router weights: 96
Loading dataset...
Dataset sizes - Train: 100, Val: 100, Test: 100
Total weights: 611, Router weights: 96
Loading dataset...
Total weights: 611, Router weights: 96
Loading dataset...
Total weights: 611, Router weights: 96
Loading dataset...
Dataset sizes - Train: 100, Val: 100, Test: 100
Dataset sizes - Train: 100, Val: 100, Test: 100
Dataset sizes - Train: 100, Val: 100, Test: 100
cn-k003:796520:796520 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.93<0>
cn-k003:796520:796520 [0] NCCL INFO cudaDriverVersion 12020
cn-k003:796520:796520 [0] NCCL INFO NCCL version 2.27.3+cuda12.9
cn-k003:796520:796520 [0] NCCL INFO Comm config Blocking set to 1
cn-k003:796523:796523 [1] NCCL INFO cudaDriverVersion 12020
cn-k003:796521:796521 [1] NCCL INFO cudaDriverVersion 12020
cn-k003:796523:796523 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.93<0>
cn-k003:796521:796521 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.93<0>
cn-k003:796523:796523 [1] NCCL INFO NCCL version 2.27.3+cuda12.9
cn-k003:796521:796521 [1] NCCL INFO NCCL version 2.27.3+cuda12.9
cn-k003:796521:796521 [1] NCCL INFO Comm config Blocking set to 1
cn-k003:796523:796523 [1] NCCL INFO Comm config Blocking set to 1
cn-k003:796522:796522 [0] NCCL INFO cudaDriverVersion 12020
cn-k003:796522:796522 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.93<0>
cn-k003:796522:796522 [0] NCCL INFO NCCL version 2.27.3+cuda12.9
cn-k003:796522:796522 [0] NCCL INFO Comm config Blocking set to 1
cn-k003:796522:797751 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. 
cn-k003:796521:797749 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. 
cn-k003:796520:797748 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. 
cn-k003:796523:797750 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. 
cn-k003:796523:797750 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.93<0>
cn-k003:796523:797750 [1] NCCL INFO Initialized NET plugin IB
cn-k003:796523:797750 [1] NCCL INFO Assigned NET plugin IB to comm
cn-k003:796523:797750 [1] NCCL INFO Using network IB
cn-k003:796523:797750 [1] NCCL INFO ncclCommInitRankConfig comm 0x55b66540fa80 rank 3 nranks 4 cudaDev 1 nvmlDev 1 busId c1000 commId 0xba050e8683cb2c3e - Init START
cn-k003:796520:797748 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.93<0>
cn-k003:796520:797748 [0] NCCL INFO Initialized NET plugin IB
cn-k003:796521:797749 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.93<0>
cn-k003:796521:797749 [1] NCCL INFO Initialized NET plugin IB
cn-k003:796520:797748 [0] NCCL INFO Assigned NET plugin IB to comm
cn-k003:796520:797748 [0] NCCL INFO Using network IB
cn-k003:796521:797749 [1] NCCL INFO Assigned NET plugin IB to comm
cn-k003:796521:797749 [1] NCCL INFO Using network IB
cn-k003:796522:797751 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.93<0>
cn-k003:796522:797751 [0] NCCL INFO Initialized NET plugin IB
cn-k003:796520:797748 [0] NCCL INFO ncclCommInitRankConfig comm 0x559f63055ea0 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 81000 commId 0xba050e8683cb2c3e - Init START
cn-k003:796522:797751 [0] NCCL INFO Assigned NET plugin IB to comm
cn-k003:796522:797751 [0] NCCL INFO Using network IB
cn-k003:796521:797749 [1] NCCL INFO ncclCommInitRankConfig comm 0x55cf01d72dd0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId c1000 commId 0xba050e8683cb2c3e - Init START
cn-k003:796522:797751 [0] NCCL INFO ncclCommInitRankConfig comm 0x5558a1451f90 rank 2 nranks 4 cudaDev 0 nvmlDev 0 busId 81000 commId 0xba050e8683cb2c3e - Init START
cn-k003:796520:797748 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-k003:796521:797749 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-k003:796522:797751 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-k003:796523:797750 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-k003:796521:797749 [1] NCCL INFO Bootstrap timings total 0.001339 (create 0.000031, send 0.000076, recv 0.000541, ring 0.000266, delay 0.000001)
cn-k003:796522:797751 [0] NCCL INFO Bootstrap timings total 0.000855 (create 0.000021, send 0.000058, recv 0.000104, ring 0.000186, delay 0.000001)
cn-k003:796520:797748 [0] NCCL INFO Bootstrap timings total 0.001710 (create 0.000029, send 0.000088, recv 0.000454, ring 0.000679, delay 0.000001)
cn-k003:796523:797750 [1] NCCL INFO Bootstrap timings total 0.024270 (create 0.000041, send 0.000102, recv 0.022635, ring 0.000046, delay 0.000002)

[2025-06-24 13:52:49] cn-k003:796522:797751 [0] init.cc:737 NCCL WARN Duplicate GPU detected : rank 2 and rank 0 both on CUDA device 81000
cn-k003:796522:797751 [0] NCCL INFO init.cc:1449 -> 5

[2025-06-24 13:52:49] cn-k003:796520:797748 [0] init.cc:737 NCCL WARN Duplicate GPU detected : rank 0 and rank 2 both on CUDA device 81000
cn-k003:796520:797748 [0] NCCL INFO init.cc:1449 -> 5

[2025-06-24 13:52:49] cn-k003:796521:797749 [1] init.cc:737 NCCL WARN Duplicate GPU detected : rank 1 and rank 3 both on CUDA device c1000
cn-k003:796521:797749 [1] NCCL INFO init.cc:1449 -> 5

[2025-06-24 13:52:49] cn-k003:796523:797750 [1] init.cc:737 NCCL WARN Duplicate GPU detected : rank 3 and rank 1 both on CUDA device c1000
cn-k003:796523:797750 [1] NCCL INFO init.cc:1449 -> 5
cn-k003:796522:797751 [0] NCCL INFO group.cc:73 -> 5 [Async thread]
cn-k003:796520:797748 [0] NCCL INFO group.cc:73 -> 5 [Async thread]
cn-k003:796521:797749 [1] NCCL INFO group.cc:73 -> 5 [Async thread]
cn-k003:796523:797750 [1] NCCL INFO group.cc:73 -> 5 [Async thread]
cn-k003:796522:796522 [0] NCCL INFO group.cc:475 -> 5
cn-k003:796520:796520 [0] NCCL INFO group.cc:475 -> 5
cn-k003:796520:796520 [0] NCCL INFO group.cc:694 -> 5
cn-k003:796521:796521 [1] NCCL INFO group.cc:475 -> 5
cn-k003:796523:796523 [1] NCCL INFO group.cc:475 -> 5
cn-k003:796523:796523 [1] NCCL INFO group.cc:694 -> 5
cn-k003:796522:796522 [0] NCCL INFO group.cc:694 -> 5
cn-k003:796522:796522 [0] NCCL INFO init.cc:1958 -> 5
cn-k003:796520:796520 [0] NCCL INFO init.cc:1958 -> 5
cn-k003:796521:796521 [1] NCCL INFO group.cc:694 -> 5
cn-k003:796521:796521 [1] NCCL INFO init.cc:1958 -> 5
cn-k003:796523:796523 [1] NCCL INFO init.cc:1958 -> 5
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mmistral-customer-support-moe[0m at: [34mhttps://wandb.ai/sarath-chandar/mistral-moe-training/runs/1f8wkmu1[0m
[1;34mwandb[0m: Find logs at: [1;35m../../../../../../../../network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250624_135103-1f8wkmu1/logs[0m
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Tue Jun 24 13:52:53 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 2
GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 796521
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7498 MiB
            Time                          : 111793 ms
            Is Running                    : 0
        Process ID                        : 796523
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7498 MiB
            Time                          : 111900 ms
            Is Running                    : 0
        Process ID                        : 796522
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7544 MiB
            Time                          : 112009 ms
            Is Running                    : 0
        Process ID                        : 796520
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 7544 MiB
            Time                          : 112116 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 796521
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7518 MiB
            Time                          : 111788 ms
            Is Running                    : 0
        Process ID                        : 796523
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7518 MiB
            Time                          : 111895 ms
            Is Running                    : 0
        Process ID                        : 796522
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 7396 MiB
            Time                          : 112004 ms
            Is Running                    : 0
        Process ID                        : 796520
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 7396 MiB
            Time                          : 112112 ms
            Is Running                    : 0

Tue Jun 24 13:52:53 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-40GB          On  | ********:81:00.0 Off |                    0 |
| N/A   38C    P0             106W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-40GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   29C    P0             100W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
