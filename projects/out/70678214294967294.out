[2025-06-26 14:36:12,530] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
Configuration:Configuration:

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3


[2025-06-26 14:36:21,438] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:36:21,438] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:36:21,447] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_3
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3

[2025-06-26 14:36:21,958] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 14:36:24,178] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 14:36:24,190] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 14:36:24,201] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 14:36:24,461] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 14:36:24,461] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-26 14:36:37,095][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 14:36:37,355][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 14:36:37,358][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
[2025-06-26 14:36:38,053][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_3/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
Loading model...
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_3
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
cn-g012:2506661:2506661 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2506661:2506661 [0] NCCL INFO cudaDriverVersion 12020
cn-g012:2506661:2506661 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2506661:2506661 [0] NCCL INFO Comm config Blocking set to 1
cn-g012:2506663:2506663 [2] NCCL INFO cudaDriverVersion 12020
cn-g012:2506663:2506663 [2] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2506662:2506662 [1] NCCL INFO cudaDriverVersion 12020
cn-g012:2506664:2506664 [3] NCCL INFO cudaDriverVersion 12020
cn-g012:2506663:2506663 [2] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2506662:2506662 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2506664:2506664 [3] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.22<0>
cn-g012:2506662:2506662 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2506664:2506664 [3] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g012:2506663:2506663 [2] NCCL INFO Comm config Blocking set to 1
cn-g012:2506662:2506662 [1] NCCL INFO Comm config Blocking set to 1
cn-g012:2506664:2506664 [3] NCCL INFO Comm config Blocking set to 1
cn-g012:2506661:2507315 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2506662:2507317 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2506662:2507317 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2506663:2507316 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2506664:2507318 [3] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g012:2506662:2507317 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2506662:2507317 [1] NCCL INFO Using network IB
cn-g012:2506662:2507317 [1] NCCL INFO ncclCommInitRankConfig comm 0x56519c763740 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xee4c9d71208c4046 - Init START
cn-g012:2506663:2507316 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2506664:2507318 [3] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2506663:2507316 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2506663:2507316 [2] NCCL INFO Using network IB
cn-g012:2506664:2507318 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2506664:2507318 [3] NCCL INFO Using network IB
cn-g012:2506663:2507316 [2] NCCL INFO ncclCommInitRankConfig comm 0x55d080ead250 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xee4c9d71208c4046 - Init START
cn-g012:2506664:2507318 [3] NCCL INFO ncclCommInitRankConfig comm 0x5639d4d260c0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xee4c9d71208c4046 - Init START
cn-g012:2506663:2507316 [2] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2506661:2507315 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.22<0>
cn-g012:2506661:2507315 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g012:2506661:2507315 [0] NCCL INFO Using network IB
cn-g012:2506661:2507315 [0] NCCL INFO ncclCommInitRankConfig comm 0x5644bcc27680 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xee4c9d71208c4046 - Init START
cn-g012:2506661:2507315 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2506664:2507318 [3] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2506662:2507317 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g012:2506662:2507317 [1] NCCL INFO Bootstrap timings total 0.035181 (create 0.000043, send 0.000153, recv 0.010172, ring 0.000045, delay 0.000000)
cn-g012:2506664:2507318 [3] NCCL INFO Bootstrap timings total 0.024830 (create 0.000045, send 0.000160, recv 0.023949, ring 0.000135, delay 0.000000)
cn-g012:2506663:2507316 [2] NCCL INFO Bootstrap timings total 0.025114 (create 0.000040, send 0.000145, recv 0.000399, ring 0.024120, delay 0.000000)
cn-g012:2506661:2507315 [0] NCCL INFO Bootstrap timings total 0.001040 (create 0.000037, send 0.000126, recv 0.000146, ring 0.000281, delay 0.000000)
cn-g012:2506662:2507317 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g012:2506663:2507316 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g012:2506661:2507315 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g012:2506662:2507317 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g012:2506664:2507318 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g012:2506662:2507317 [1] NCCL INFO comm 0x56519c763740 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g012:2506661:2507315 [0] NCCL INFO comm 0x5644bcc27680 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g012:2506664:2507318 [3] NCCL INFO comm 0x5639d4d260c0 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g012:2506663:2507316 [2] NCCL INFO comm 0x55d080ead250 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g012:2506662:2507317 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g012:2506662:2507317 [1] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506661:2507315 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g012:2506664:2507318 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g012:2506664:2507318 [3] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506663:2507316 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g012:2506663:2507316 [2] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506661:2507315 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g012:2506661:2507315 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g012:2506661:2507315 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g012:2506661:2507315 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g012:2506661:2507315 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g012:2506661:2507315 [0] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506664:2507332 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 6
cn-g012:2506663:2507333 [2] NCCL INFO [Proxy Service] Device 2 CPU core 10
cn-g012:2506662:2507334 [1] NCCL INFO [Proxy Service] Device 1 CPU core 0
cn-g012:2506664:2507331 [3] NCCL INFO [Proxy Service] Device 3 CPU core 0
cn-g012:2506663:2507335 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 3
cn-g012:2506662:2507336 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 13
cn-g012:2506661:2507315 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g012:2506661:2507337 [0] NCCL INFO [Proxy Service] Device 0 CPU core 7
cn-g012:2506661:2507338 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 14
cn-g012:2506663:2507316 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506663:2507316 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506661:2507315 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506661:2507315 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506661:2507315 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g012:2506664:2507318 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506664:2507318 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506662:2507317 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506662:2507317 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506662:2507317 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2506662:2507317 [1] NCCL INFO ncclCommInitRankConfig comm 0x56519c763740 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xee4c9d71208c4046 - Init COMPLETE
cn-g012:2506662:2507317 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.30 (kernels 0.12, alloc 0.01, bootstrap 0.04, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.02)
cn-g012:2506661:2507315 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2506661:2507315 [0] NCCL INFO ncclCommInitRankConfig comm 0x5644bcc27680 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xee4c9d71208c4046 - Init COMPLETE
cn-g012:2506663:2507316 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2506661:2507315 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 0.30 (kernels 0.13, alloc 0.05, bootstrap 0.00, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2506663:2507316 [2] NCCL INFO ncclCommInitRankConfig comm 0x55d080ead250 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xee4c9d71208c4046 - Init COMPLETE
cn-g012:2506663:2507316 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.30 (kernels 0.13, alloc 0.01, bootstrap 0.03, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2506664:2507318 [3] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g012:2506664:2507318 [3] NCCL INFO ncclCommInitRankConfig comm 0x5639d4d260c0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xee4c9d71208c4046 - Init COMPLETE
cn-g012:2506664:2507318 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.30 (kernels 0.13, alloc 0.01, bootstrap 0.03, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.02)
cn-g012:2506662:2507339 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506663:2507342 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506662:2507339 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507340 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507341 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2506663:2507342 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2506662:2507339 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2506661:2507340 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-06-26 14:38:02,971] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-06-26 14:38:02,971] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
cn-g012:2506661:2506661 [0] NCCL INFO Comm config Blocking set to 1
cn-g012:2506661:2507400 [0] NCCL INFO Using network IB
cn-g012:2506661:2507400 [0] NCCL INFO ncclCommInitRankConfig comm 0x5644b9c27380 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x74c173d8d0cd1572 - Init START
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
cn-g012:2506662:2506662 [1] NCCL INFO Comm config Blocking set to 1
cn-g012:2506662:2507411 [1] NCCL INFO Using network IB
cn-g012:2506662:2507411 [1] NCCL INFO ncclCommInitRankConfig comm 0x56519cf23740 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x74c173d8d0cd1572 - Init START
cn-g012:2506664:2506664 [3] NCCL INFO Comm config Blocking set to 1
cn-g012:2506664:2507417 [3] NCCL INFO Using network IB
cn-g012:2506664:2507417 [3] NCCL INFO ncclCommInitRankConfig comm 0x5639d2d25ec0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x74c173d8d0cd1572 - Init START
cn-g012:2506663:2506663 [2] NCCL INFO Comm config Blocking set to 1
cn-g012:2506663:2507420 [2] NCCL INFO Using network IB
cn-g012:2506663:2507420 [2] NCCL INFO ncclCommInitRankConfig comm 0x55d00df6e580 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x74c173d8d0cd1572 - Init START
cn-g012:2506663:2507420 [2] NCCL INFO Bootstrap timings total 0.001837 (create 0.000042, send 0.000092, recv 0.000127, ring 0.000065, delay 0.000000)
cn-g012:2506664:2507417 [3] NCCL INFO Bootstrap timings total 0.178075 (create 0.000042, send 0.000100, recv 0.000055, ring 0.001420, delay 0.000000)
cn-g012:2506662:2507411 [1] NCCL INFO Bootstrap timings total 0.348338 (create 0.000043, send 0.000094, recv 0.347990, ring 0.000037, delay 0.000000)
cn-g012:2506661:2507400 [0] NCCL INFO Bootstrap timings total 2.621415 (create 0.000055, send 0.000194, recv 2.273173, ring 0.177708, delay 0.000000)
cn-g012:2506662:2507411 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g012:2506662:2507411 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g012:2506664:2507417 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g012:2506663:2507420 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g012:2506661:2507400 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g012:2506662:2507411 [1] NCCL INFO comm 0x56519cf23740 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g012:2506664:2507417 [3] NCCL INFO comm 0x5639d2d25ec0 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g012:2506661:2507400 [0] NCCL INFO comm 0x5644b9c27380 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g012:2506663:2507420 [2] NCCL INFO comm 0x55d00df6e580 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g012:2506662:2507411 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g012:2506662:2507411 [1] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506664:2507417 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g012:2506664:2507417 [3] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506661:2507400 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g012:2506663:2507420 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g012:2506663:2507420 [2] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506661:2507400 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g012:2506661:2507400 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g012:2506661:2507400 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g012:2506661:2507400 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g012:2506661:2507400 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g012:2506661:2507400 [0] NCCL INFO P2P Chunksize set to 524288
cn-g012:2506663:2507421 [2] NCCL INFO [Proxy Service] Device 2 CPU core 7
cn-g012:2506663:2507422 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 15
cn-g012:2506664:2507423 [3] NCCL INFO [Proxy Service] Device 3 CPU core 11
cn-g012:2506664:2507424 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 15
cn-g012:2506662:2507425 [1] NCCL INFO [Proxy Service] Device 1 CPU core 3
cn-g012:2506662:2507426 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 10
cn-g012:2506661:2507400 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g012:2506661:2507427 [0] NCCL INFO [Proxy Service] Device 0 CPU core 13
cn-g012:2506661:2507428 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 0
cn-g012:2506662:2507411 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506662:2507411 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506664:2507417 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506664:2507417 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506663:2507420 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506663:2507420 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506661:2507400 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g012:2506661:2507400 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g012:2506661:2507400 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g012:2506664:2507417 [3] NCCL INFO ncclCommInitRankConfig comm 0x5639d2d25ec0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x74c173d8d0cd1572 - Init COMPLETE
cn-g012:2506662:2507411 [1] NCCL INFO ncclCommInitRankConfig comm 0x56519cf23740 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x74c173d8d0cd1572 - Init COMPLETE
cn-g012:2506662:2507411 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.48 (kernels 0.00, alloc 0.00, bootstrap 0.35, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.03)
cn-g012:2506663:2507420 [2] NCCL INFO ncclCommInitRankConfig comm 0x55d00df6e580 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x74c173d8d0cd1572 - Init COMPLETE
cn-g012:2506663:2507420 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.14 (kernels 0.00, alloc 0.00, bootstrap 0.00, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.02)
cn-g012:2506661:2507400 [0] NCCL INFO ncclCommInitRankConfig comm 0x5644b9c27380 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x74c173d8d0cd1572 - Init COMPLETE
cn-g012:2506664:2507417 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.31 (kernels 0.00, alloc 0.00, bootstrap 0.18, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.02)
cn-g012:2506661:2507400 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 2.76 (kernels 0.00, alloc 0.00, bootstrap 2.62, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.04, rest 0.03)
cn-g012:2506663:2507430 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506664:2507431 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g012:2506663:2507430 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g012:2506661:2507432 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g012:2506662:2507429 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2506661:2507432 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2506663:2507430 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g012:2506664:2507431 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
[2025-06-26 14:38:07,556] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-06-26 14:38:07,559] [INFO] [logging.py:128:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-06-26 14:38:07,560] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-06-26 14:38:07,580] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-06-26 14:38:07,580] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-06-26 14:38:07,580] [INFO] [logging.py:128:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:149:__init__] Reduce bucket size 5********
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:150:__init__] Allgather bucket size 2********
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:151:__init__] CPU Offload: False
[2025-06-26 14:38:07,581] [INFO] [stage_1_and_2.py:152:__init__] Round robin gradient partitioning: False
[2025-06-26 14:38:11,281] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-06-26 14:38:11,282] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB 
[2025-06-26 14:38:11,283] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 37.11 GB, percent = 3.7%
[2025-06-26 14:38:11,446] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-06-26 14:38:11,447] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-06-26 14:38:11,447] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 37.11 GB, percent = 3.7%
[2025-06-26 14:38:11,448] [INFO] [stage_1_and_2.py:544:__init__] optimizer state initialized
[2025-06-26 14:38:11,626] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-06-26 14:38:11,627] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-06-26 14:38:11,627] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 37.2 GB, percent = 3.7%
[2025-06-26 14:38:11,629] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-06-26 14:38:11,629] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-06-26 14:38:11,629] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-06-26 14:38:11,630] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-06-26 14:38:11,634] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-06-26 14:38:11,634] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-06-26 14:38:11,635] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-06-26 14:38:11,636] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f6371356c20>
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-06-26 14:38:11,637] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-06-26 14:38:11,638] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-06-26 14:38:11,639] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-06-26 14:38:11,640] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-06-26 14:38:11,641] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-06-26 14:38:11,642] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-06-26 14:38:11,643] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-06-26 14:38:11,644] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-06-26 14:38:11,645] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   train_batch_size ............. 16
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-06-26 14:38:11,646] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  True
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=5******** use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=2******** overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1********0 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1********0 max_reuse_distance=1********0 gather_16bit_weights_on_model_save=False use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-06-26 14:38:11,647] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-06-26 14:38:11,648] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 2
[2025-06-26 14:38:11,648] [INFO] [config.py:989:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2, 
        "allgather_partitions": true, 
        "allgather_bucket_size": 2.000000e+08, 
        "overlap_comm": true, 
        "reduce_scatter": true, 
        "contiguous_gradients": true
    }, 
    "gradient_accumulation_steps": 4, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 16, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "bf16": {
        "enabled": true
    }, 
    "fp16": {
        "enabled": false
    }, 
    "zero_allow_untested_optimizer": true
}
{'loss': 1.92, 'grad_norm': 51.04514694213867, 'learning_rate': 9.997442455242967e-05, 'epoch': 0.0}
{'loss': 1.3574, 'grad_norm': 2.478416919708252, 'learning_rate': 9.936061381074169e-05, 'epoch': 0.06}
{'loss': 1.2474, 'grad_norm': 2.743911027908325, 'learning_rate': 9.872122762148338e-05, 'epoch': 0.13}
{'loss': 1.2145, 'grad_norm': 1.9198548793792725, 'learning_rate': 9.808184143222507e-05, 'epoch': 0.19}
{'loss': 1.1658, 'grad_norm': 1.785643219947815, 'learning_rate': 9.744245524296676e-05, 'epoch': 0.26}
{'loss': 1.0941, 'grad_norm': 1.9877444505691528, 'learning_rate': 9.680306905370844e-05, 'epoch': 0.32}
{'loss': 1.0961, 'grad_norm': 1.597650170326233, 'learning_rate': 9.616368286445013e-05, 'epoch': 0.38}
{'loss': 1.0337, 'grad_norm': 1.6794203519821167, 'learning_rate': 9.552429667519182e-05, 'epoch': 0.45}
{'loss': 1.0184, 'grad_norm': 1.3539094924926758, 'learning_rate': 9.488491048593351e-05, 'epoch': 0.51}
{'loss': 1.031, 'grad_norm': 1.4158295392990112, 'learning_rate': 9.42455242966752e-05, 'epoch': 0.58}
{'loss': 1.0093, 'grad_norm': 1.3328862190246582, 'learning_rate': 9.360613810741689e-05, 'epoch': 0.64}
{'loss': 0.949, 'grad_norm': 1.5916404724121094, 'learning_rate': 9.296675191815857e-05, 'epoch': 0.7}
{'loss': 0.8962, 'grad_norm': 1.5085554122924805, 'learning_rate': 9.232736572890026e-05, 'epoch': 0.77}
{'loss': 0.8796, 'grad_norm': 1.5415908098220825, 'learning_rate': 9.168797953964195e-05, 'epoch': 0.83}
{'loss': 0.8412, 'grad_norm': 1.5445359945297241, 'learning_rate': 9.104859335038364e-05, 'epoch': 0.9}
{'loss': 0.8726, 'grad_norm': 1.444787621498108, 'learning_rate': 9.040920716112533e-05, 'epoch': 0.96}
{'loss': 0.8042, 'grad_norm': 1.3148874044418335, 'learning_rate': 8.976982097186701e-05, 'epoch': 1.02}
{'loss': 0.7569, 'grad_norm': 1.1983317136764526, 'learning_rate': 8.91304347826087e-05, 'epoch': 1.09}
{'loss': 0.694, 'grad_norm': 1.3833144903182983, 'learning_rate': 8.849104859335039e-05, 'epoch': 1.15}
{'loss': 0.7175, 'grad_norm': 1.2883570194244385, 'learning_rate': 8.785166240409208e-05, 'epoch': 1.21}
{'loss': 0.6819, 'grad_norm': 1.2545000314712524, 'learning_rate': 8.721227621483377e-05, 'epoch': 1.28}
{'loss': 0.6492, 'grad_norm': 1.2674205303192139, 'learning_rate': 8.657289002557545e-05, 'epoch': 1.34}
{'loss': 0.6671, 'grad_norm': 1.1528080701828003, 'learning_rate': 8.593350383631714e-05, 'epoch': 1.41}
{'loss': 0.6577, 'grad_norm': 1.1249068975448608, 'learning_rate': 8.529411764705883e-05, 'epoch': 1.47}
{'loss': 0.6536, 'grad_norm': 1.0751123428344727, 'learning_rate': 8.465473145780052e-05, 'epoch': 1.53}
{'loss': 0.6296, 'grad_norm': 1.0893763303756714, 'learning_rate': 8.40153452685422e-05, 'epoch': 1.6}
{'loss': 0.5884, 'grad_norm': 1.1417633295059204, 'learning_rate': 8.33759590792839e-05, 'epoch': 1.66}
{'loss': 0.5947, 'grad_norm': 1.2017974853515625, 'learning_rate': 8.273657289002558e-05, 'epoch': 1.73}
{'loss': 0.6155, 'grad_norm': 1.0679086446762085, 'learning_rate': 8.209718670076727e-05, 'epoch': 1.79}
{'loss': 0.5831, 'grad_norm': 1.4074851274490356, 'learning_rate': 8.145780051150896e-05, 'epoch': 1.85}
{'loss': 0.577, 'grad_norm': 1.1386092901229858, 'learning_rate': 8.081841432225065e-05, 'epoch': 1.92}
{'loss': 0.545, 'grad_norm': 1.1608484983444214, 'learning_rate': 8.017902813299233e-05, 'epoch': 1.98}
{'loss': 0.5071, 'grad_norm': 0.9658547043800354, 'learning_rate': 7.953964194373402e-05, 'epoch': 2.05}
{'loss': 0.4577, 'grad_norm': 1.2401636838912964, 'learning_rate': 7.890025575447571e-05, 'epoch': 2.11}
{'loss': 0.4575, 'grad_norm': 1.1377052068710327, 'learning_rate': 7.82608695652174e-05, 'epoch': 2.17}
{'loss': 0.4776, 'grad_norm': 1.1052194833755493, 'learning_rate': 7.762148337595909e-05, 'epoch': 2.24}
{'loss': 0.4801, 'grad_norm': 0.9782165884971619, 'learning_rate': 7.698209718670077e-05, 'epoch': 2.3}
{'loss': 0.4403, 'grad_norm': 1.0849721431732178, 'learning_rate': 7.634271099744246e-05, 'epoch': 2.37}
{'loss': 0.4507, 'grad_norm': 1.2417902946472168, 'learning_rate': 7.570332480818415e-05, 'epoch': 2.43}
{'loss': 0.4032, 'grad_norm': 0.9264167547225952, 'learning_rate': 7.506393861892584e-05, 'epoch': 2.49}
{'loss': 0.4258, 'grad_norm': 0.9667097330093384, 'learning_rate': 7.442455242966753e-05, 'epoch': 2.56}
{'loss': 0.4242, 'grad_norm': 1.2026914358139038, 'learning_rate': 7.378516624040921e-05, 'epoch': 2.62}
{'loss': 0.4185, 'grad_norm': 1.2307852506637573, 'learning_rate': 7.31457800511509e-05, 'epoch': 2.69}
{'loss': 0.3799, 'grad_norm': 0.8642074465751648, 'learning_rate': 7.250639386189259e-05, 'epoch': 2.75}
{'loss': 0.4224, 'grad_norm': 0.8795709609985352, 'learning_rate': 7.186700767263428e-05, 'epoch': 2.81}
{'loss': 0.3656, 'grad_norm': 1.0927730798721313, 'learning_rate': 7.122762148337597e-05, 'epoch': 2.88}
{'loss': 0.3829, 'grad_norm': 0.8372628688812256, 'learning_rate': 7.058823529411765e-05, 'epoch': 2.94}
{'loss': 0.3526, 'grad_norm': 1.096291422843933, 'learning_rate': 6.994884910485934e-05, 'epoch': 3.01}
{'loss': 0.3122, 'grad_norm': 0.8691463470458984, 'learning_rate': 6.930946291560103e-05, 'epoch': 3.07}
{'loss': 0.3207, 'grad_norm': 0.9615539908409119, 'learning_rate': 6.867007672634272e-05, 'epoch': 3.13}
{'loss': 0.3089, 'grad_norm': 0.838830292224884, 'learning_rate': 6.80306905370844e-05, 'epoch': 3.2}
{'loss': 0.3025, 'grad_norm': 1.083067536354065, 'learning_rate': 6.73913043478261e-05, 'epoch': 3.26}
{'loss': 0.2971, 'grad_norm': 1.0977531671524048, 'learning_rate': 6.675191815856778e-05, 'epoch': 3.32}
{'loss': 0.3029, 'grad_norm': 0.8532986044883728, 'learning_rate': 6.611253196930947e-05, 'epoch': 3.39}
{'loss': 0.2896, 'grad_norm': 0.8712836503982544, 'learning_rate': 6.547314578005116e-05, 'epoch': 3.45}
{'loss': 0.2819, 'grad_norm': 0.8685945868492126, 'learning_rate': 6.483375959079285e-05, 'epoch': 3.52}
{'loss': 0.2656, 'grad_norm': 0.6175561547279358, 'learning_rate': 6.419437340153452e-05, 'epoch': 3.58}
{'loss': 0.2745, 'grad_norm': 0.97038334608078, 'learning_rate': 6.355498721227622e-05, 'epoch': 3.64}
{'loss': 0.2619, 'grad_norm': 0.9724594950675964, 'learning_rate': 6.29156010230179e-05, 'epoch': 3.71}
{'loss': 0.2638, 'grad_norm': 1.0277965068817139, 'learning_rate': 6.22762148337596e-05, 'epoch': 3.77}
{'loss': 0.2599, 'grad_norm': 0.9379246830940247, 'learning_rate': 6.163682864450127e-05, 'epoch': 3.84}
{'loss': 0.2793, 'grad_norm': 1.0384514331817627, 'learning_rate': 6.099744245524297e-05, 'epoch': 3.9}
{'loss': 0.2711, 'grad_norm': 0.8246506452560425, 'learning_rate': 6.035805626598465e-05, 'epoch': 3.96}
{'loss': 0.236, 'grad_norm': 0.793634295463562, 'learning_rate': 5.9718670076726344e-05, 'epoch': 4.03}
{'loss': 0.2188, 'grad_norm': 0.8000171184539795, 'learning_rate': 5.9079283887468026e-05, 'epoch': 4.09}
{'loss': 0.2087, 'grad_norm': 0.8009399175643921, 'learning_rate': 5.843989769820972e-05, 'epoch': 4.16}
{'loss': 0.1988, 'grad_norm': 0.9281237721443176, 'learning_rate': 5.78005115089514e-05, 'epoch': 4.22}
{'loss': 0.177, 'grad_norm': 0.5372213125228882, 'learning_rate': 5.7161125319693096e-05, 'epoch': 4.28}
{'loss': 0.2083, 'grad_norm': 0.8120546340942383, 'learning_rate': 5.652173913043478e-05, 'epoch': 4.35}
{'loss': 0.1878, 'grad_norm': 0.7198323607444763, 'learning_rate': 5.588235294117647e-05, 'epoch': 4.41}
{'loss': 0.1862, 'grad_norm': 0.7830379009246826, 'learning_rate': 5.5242966751918154e-05, 'epoch': 4.48}
{'loss': 0.1839, 'grad_norm': 0.6439346671104431, 'learning_rate': 5.460358056265985e-05, 'epoch': 4.54}
{'loss': 0.2019, 'grad_norm': 0.5754552483558655, 'learning_rate': 5.396419437340153e-05, 'epoch': 4.6}
{'loss': 0.1853, 'grad_norm': 0.836511492729187, 'learning_rate': 5.3324808184143225e-05, 'epoch': 4.67}
{'loss': 0.179, 'grad_norm': 0.6660141944885254, 'learning_rate': 5.268542199488491e-05, 'epoch': 4.73}
{'loss': 0.198, 'grad_norm': 0.828519344329834, 'learning_rate': 5.20460358056266e-05, 'epoch': 4.8}
{'loss': 0.1695, 'grad_norm': 0.7320075035095215, 'learning_rate': 5.140664961636829e-05, 'epoch': 4.86}
{'loss': 0.1805, 'grad_norm': 0.656623125076294, 'learning_rate': 5.076726342710998e-05, 'epoch': 4.92}
{'loss': 0.1756, 'grad_norm': 0.7695077657699585, 'learning_rate': 5.0127877237851665e-05, 'epoch': 4.99}
{'loss': 0.15, 'grad_norm': 0.5612486600875854, 'learning_rate': 4.948849104859335e-05, 'epoch': 5.05}
{'loss': 0.1437, 'grad_norm': 0.6725096702575684, 'learning_rate': 4.884910485933504e-05, 'epoch': 5.12}
{'loss': 0.1437, 'grad_norm': 0.5595406293869019, 'learning_rate': 4.820971867007673e-05, 'epoch': 5.18}
{'loss': 0.1561, 'grad_norm': 0.5209560394287109, 'learning_rate': 4.757033248081842e-05, 'epoch': 5.24}
{'loss': 0.1561, 'grad_norm': 0.7302173376083374, 'learning_rate': 4.6930946291560105e-05, 'epoch': 5.31}
{'loss': 0.1286, 'grad_norm': 0.6633211374282837, 'learning_rate': 4.629156010230179e-05, 'epoch': 5.37}
{'loss': 0.1408, 'grad_norm': 0.39233705401420593, 'learning_rate': 4.565217391304348e-05, 'epoch': 5.43}
{'loss': 0.1345, 'grad_norm': 0.6151101589202881, 'learning_rate': 4.501278772378517e-05, 'epoch': 5.5}
{'loss': 0.1379, 'grad_norm': 1.0051714181900024, 'learning_rate': 4.437340153452686e-05, 'epoch': 5.56}
{'loss': 0.126, 'grad_norm': 0.7098011374473572, 'learning_rate': 4.3734015345268545e-05, 'epoch': 5.63}
{'loss': 0.1392, 'grad_norm': 0.6773862242698669, 'learning_rate': 4.309462915601023e-05, 'epoch': 5.69}
{'loss': 0.1259, 'grad_norm': 0.49850451946258545, 'learning_rate': 4.245524296675192e-05, 'epoch': 5.75}
{'loss': 0.1245, 'grad_norm': 0.5925995111465454, 'learning_rate': 4.181585677749361e-05, 'epoch': 5.82}
{'loss': 0.1249, 'grad_norm': 0.5363137722015381, 'learning_rate': 4.11764705882353e-05, 'epoch': 5.88}
{'loss': 0.1191, 'grad_norm': 0.5227335691452026, 'learning_rate': 4.0537084398976985e-05, 'epoch': 5.95}
{'loss': 0.1157, 'grad_norm': 0.5731391310691833, 'learning_rate': 3.989769820971867e-05, 'epoch': 6.01}
{'loss': 0.1065, 'grad_norm': 0.624992847442627, 'learning_rate': 3.925831202046036e-05, 'epoch': 6.07}
{'loss': 0.0997, 'grad_norm': 0.5940295457839966, 'learning_rate': 3.861892583120205e-05, 'epoch': 6.14}
{'loss': 0.1086, 'grad_norm': 0.3888510763645172, 'learning_rate': 3.797953964194374e-05, 'epoch': 6.2}
{'loss': 0.1011, 'grad_norm': 0.490695595741272, 'learning_rate': 3.7340153452685426e-05, 'epoch': 6.27}
{'loss': 0.1064, 'grad_norm': 0.5110863447189331, 'learning_rate': 3.6700767263427114e-05, 'epoch': 6.33}
{'loss': 0.1066, 'grad_norm': 0.575789213180542, 'learning_rate': 3.60613810741688e-05, 'epoch': 6.39}
{'loss': 0.0955, 'grad_norm': 0.5254873633384705, 'learning_rate': 3.542199488491049e-05, 'epoch': 6.46}
{'loss': 0.0962, 'grad_norm': 0.5788753628730774, 'learning_rate': 3.478260869565218e-05, 'epoch': 6.52}
{'loss': 0.1047, 'grad_norm': 0.37460291385650635, 'learning_rate': 3.4143222506393866e-05, 'epoch': 6.59}
{'loss': 0.0927, 'grad_norm': 0.7633658051490784, 'learning_rate': 3.3503836317135554e-05, 'epoch': 6.65}
{'loss': 0.095, 'grad_norm': 0.5634973049163818, 'learning_rate': 3.286445012787724e-05, 'epoch': 6.71}
{'loss': 0.0879, 'grad_norm': 0.526990532875061, 'learning_rate': 3.222506393861893e-05, 'epoch': 6.78}
{'loss': 0.1011, 'grad_norm': 0.6740297079086304, 'learning_rate': 3.158567774936062e-05, 'epoch': 6.84}
{'loss': 0.0914, 'grad_norm': 0.3880011737346649, 'learning_rate': 3.0946291560102306e-05, 'epoch': 6.91}
{'loss': 0.0876, 'grad_norm': 0.3968127369880676, 'learning_rate': 3.030690537084399e-05, 'epoch': 6.97}
{'loss': 0.0921, 'grad_norm': 0.5650238394737244, 'learning_rate': 2.966751918158568e-05, 'epoch': 7.03}
{'loss': 0.081, 'grad_norm': 0.5522066950798035, 'learning_rate': 2.9028132992327367e-05, 'epoch': 7.1}
{'loss': 0.0912, 'grad_norm': 0.5009344816207886, 'learning_rate': 2.8388746803069055e-05, 'epoch': 7.16}
{'loss': 0.0834, 'grad_norm': 0.4166412353515625, 'learning_rate': 2.7749360613810743e-05, 'epoch': 7.23}
{'loss': 0.0782, 'grad_norm': 0.2804437279701233, 'learning_rate': 2.710997442455243e-05, 'epoch': 7.29}
{'loss': 0.0818, 'grad_norm': 0.31494662165641785, 'learning_rate': 2.647058823529412e-05, 'epoch': 7.35}
{'loss': 0.0781, 'grad_norm': 0.3036784827709198, 'learning_rate': 2.5831202046035807e-05, 'epoch': 7.42}
{'loss': 0.0849, 'grad_norm': 0.2896527945995331, 'learning_rate': 2.5191815856777495e-05, 'epoch': 7.48}
{'loss': 0.0821, 'grad_norm': 0.4325106739997864, 'learning_rate': 2.4552429667519183e-05, 'epoch': 7.54}
{'loss': 0.0789, 'grad_norm': 0.2922871708869934, 'learning_rate': 2.391304347826087e-05, 'epoch': 7.61}
{'loss': 0.0751, 'grad_norm': 0.6019728183746338, 'learning_rate': 2.327365728900256e-05, 'epoch': 7.67}
{'loss': 0.0799, 'grad_norm': 0.40585023164749146, 'learning_rate': 2.2634271099744247e-05, 'epoch': 7.74}
{'loss': 0.081, 'grad_norm': 0.45310041308403015, 'learning_rate': 2.1994884910485935e-05, 'epoch': 7.8}
{'loss': 0.0729, 'grad_norm': 0.26771169900894165, 'learning_rate': 2.1355498721227623e-05, 'epoch': 7.86}
{'loss': 0.0752, 'grad_norm': 0.2737603485584259, 'learning_rate': 2.071611253196931e-05, 'epoch': 7.93}
{'loss': 0.0709, 'grad_norm': 0.47309884428977966, 'learning_rate': 2.0076726342711e-05, 'epoch': 7.99}
{'loss': 0.0717, 'grad_norm': 0.23987722396850586, 'learning_rate': 1.9437340153452684e-05, 'epoch': 8.06}
{'loss': 0.0689, 'grad_norm': 0.2890155017375946, 'learning_rate': 1.8797953964194372e-05, 'epoch': 8.12}
{'loss': 0.0654, 'grad_norm': 0.3300662934780121, 'learning_rate': 1.815856777493606e-05, 'epoch': 8.18}
{'loss': 0.0684, 'grad_norm': 0.2691166400909424, 'learning_rate': 1.7519181585677748e-05, 'epoch': 8.25}
{'loss': 0.0658, 'grad_norm': 0.5035260915756226, 'learning_rate': 1.6879795396419436e-05, 'epoch': 8.31}
{'loss': 0.0603, 'grad_norm': 0.4748343825340271, 'learning_rate': 1.6240409207161124e-05, 'epoch': 8.38}
{'loss': 0.0641, 'grad_norm': 0.207920640707016, 'learning_rate': 1.5601023017902812e-05, 'epoch': 8.44}
{'loss': 0.0637, 'grad_norm': 0.19097232818603516, 'learning_rate': 1.4961636828644502e-05, 'epoch': 8.5}
{'loss': 0.0654, 'grad_norm': 0.2891494929790497, 'learning_rate': 1.432225063938619e-05, 'epoch': 8.57}
{'loss': 0.0708, 'grad_norm': 0.4414593279361725, 'learning_rate': 1.3682864450127878e-05, 'epoch': 8.63}
{'loss': 0.0688, 'grad_norm': 0.4285537600517273, 'learning_rate': 1.3043478260869566e-05, 'epoch': 8.7}
{'loss': 0.0661, 'grad_norm': 0.31454744935035706, 'learning_rate': 1.2404092071611254e-05, 'epoch': 8.76}
{'loss': 0.0694, 'grad_norm': 0.21961313486099243, 'learning_rate': 1.1764705882352942e-05, 'epoch': 8.82}
{'loss': 0.0627, 'grad_norm': 0.4640918970108032, 'learning_rate': 1.112531969309463e-05, 'epoch': 8.89}
{'loss': 0.0661, 'grad_norm': 0.25889191031455994, 'learning_rate': 1.0485933503836318e-05, 'epoch': 8.95}
{'loss': 0.0626, 'grad_norm': 0.3358956575393677, 'learning_rate': 9.846547314578006e-06, 'epoch': 9.02}
{'loss': 0.0574, 'grad_norm': 0.1670030951499939, 'learning_rate': 9.207161125319694e-06, 'epoch': 9.08}
{'loss': 0.0569, 'grad_norm': 0.17014889419078827, 'learning_rate': 8.567774936061382e-06, 'epoch': 9.14}
{'loss': 0.0598, 'grad_norm': 0.09686073660850525, 'learning_rate': 7.92838874680307e-06, 'epoch': 9.21}
{'loss': 0.06, 'grad_norm': 0.30939456820487976, 'learning_rate': 7.289002557544758e-06, 'epoch': 9.27}
{'loss': 0.0606, 'grad_norm': 0.1654721051454544, 'learning_rate': 6.649616368286446e-06, 'epoch': 9.34}
{'loss': 0.0572, 'grad_norm': 0.32274121046066284, 'learning_rate': 6.010230179028133e-06, 'epoch': 9.4}
{'loss': 0.0585, 'grad_norm': 0.13703332841396332, 'learning_rate': 5.370843989769821e-06, 'epoch': 9.46}
{'loss': 0.0569, 'grad_norm': 0.5089929699897766, 'learning_rate': 4.731457800511509e-06, 'epoch': 9.53}
{'loss': 0.059, 'grad_norm': 0.16062669456005096, 'learning_rate': 4.092071611253197e-06, 'epoch': 9.59}
{'loss': 0.06, 'grad_norm': 0.1981465071439743, 'learning_rate': 3.4526854219948846e-06, 'epoch': 9.65}
{'loss': 0.0582, 'grad_norm': 0.18919645249843597, 'learning_rate': 2.813299232736573e-06, 'epoch': 9.72}
{'loss': 0.0618, 'grad_norm': 0.1623886674642563, 'learning_rate': 2.173913043478261e-06, 'epoch': 9.78}
{'loss': 0.0634, 'grad_norm': 0.23087430000305176, 'learning_rate': 1.534526854219949e-06, 'epoch': 9.85}
{'loss': 0.059, 'grad_norm': 0.13514994084835052, 'learning_rate': 8.951406649616369e-07, 'epoch': 9.91}
{'loss': 0.0571, 'grad_norm': 0.16097837686538696, 'learning_rate': 2.5575447570332484e-07, 'epoch': 9.97}
[2025-06-26 16:13:10,413] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step3910 is about to be saved!
[2025-06-26 16:13:13,973] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt
[2025-06-26 16:13:13,973] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt...
[2025-06-26 16:13:59,542] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt.
[2025-06-26 16:13:59,554] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt...
[2025-06-26 16:14:10,617] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt.
[2025-06-26 16:14:10,621] [INFO] [engine.py:3536:_save_zero_checkpoint] zero checkpoint saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt
[2025-06-26 16:14:10,622] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step3910 is ready now!
{'train_runtime': 5762.4929, 'train_samples_per_second': 10.853, 'train_steps_per_second': 0.679, 'train_loss': 0.30228340509907364, 'epoch': 10.0}
Training completed!
Running final evaluation...
Evaluation metrics at step 3910: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4691, 'eval_samples_per_second': 32.153, 'eval_steps_per_second': 8.038}Evaluation metrics at step 3910: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4587, 'eval_samples_per_second': 32.205, 'eval_steps_per_second': 8.051}Evaluation metrics at step 3910: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4691, 'eval_samples_per_second': 32.153, 'eval_steps_per_second': 8.038}Evaluation metrics at step 3910: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4586, 'eval_samples_per_second': 32.205, 'eval_steps_per_second': 8.051}



Final evaluation results: {'eval_loss': 0.06868267059326172, 'eval_runtime': 6.4586, 'eval_samples_per_second': 32.205, 'eval_steps_per_second': 8.051, 'epoch': 10.0}
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506662:2507334 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506662:2528147 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506662:2507334 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506663:2507333 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506663:2528149 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506662:2507334 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506663:2507333 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g012:2506664:2528151 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g012:2506664:2507331 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g012:2506661:2507427 [0] NCCL INFO [Service thread] Connection closed by localRank 1
cn-g012:2506661:2507427 [0] NCCL INFO [Service thread] Connection closed by localRank 2
cn-g012:2506661:2507427 [0] NCCL INFO [Service thread] Connection closed by localRank 3
cn-g012:2506661:2528153 [0] NCCL INFO comm 0x5644b9c27380 rank 0 nranks 4 cudaDev 0 busId 1000 - Destroy COMPLETE
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Thu Jun 26 16:36:28 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2506662
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 7204916 ms
            Is Running                    : 0
        Process ID                        : 2506663
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 7205168 ms
            Is Running                    : 0
        Process ID                        : 2506664
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 7205451 ms
            Is Running                    : 0
        Process ID                        : 2506661
            GPU Utilization               : 64 %
            Memory Utilization            : 20 %
            Max memory usage              : 39772 MiB
            Time                          : 7205209 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2506662
            GPU Utilization               : 66 %
            Memory Utilization            : 20 %
            Max memory usage              : 40992 MiB
            Time                          : 7204914 ms
            Is Running                    : 0
        Process ID                        : 2506663
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 7205166 ms
            Is Running                    : 0
        Process ID                        : 2506664
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 7205449 ms
            Is Running                    : 0
        Process ID                        : 2506661
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 7205208 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2506662
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 7204913 ms
            Is Running                    : 0
        Process ID                        : 2506663
            GPU Utilization               : 65 %
            Memory Utilization            : 21 %
            Max memory usage              : 42364 MiB
            Time                          : 7205164 ms
            Is Running                    : 0
        Process ID                        : 2506664
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 7205448 ms
            Is Running                    : 0
        Process ID                        : 2506661
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 7205208 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2506662
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 7204912 ms
            Is Running                    : 0
        Process ID                        : 2506663
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 7205163 ms
            Is Running                    : 0
        Process ID                        : 2506664
            GPU Utilization               : 64 %
            Memory Utilization            : 21 %
            Max memory usage              : 39778 MiB
            Time                          : 7205446 ms
            Is Running                    : 0
        Process ID                        : 2506661
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 7205207 ms
            Is Running                    : 0

Thu Jun 26 16:36:28 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   31C    P0             113W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   27C    P0              85W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   32C    P0             115W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   31C    P0             115W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
