[2025-06-26 16:56:54,498] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
Configuration:Configuration:

Configuration:
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_top1
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1

[2025-06-26 16:57:06,104] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:57:06,105] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:57:06,133] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:57:06,134] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-26 16:57:08,843] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:57:08,843] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:57:08,847] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-06-26 16:57:08,847] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-06-26 16:57:08,859] [INFO] [comm.py:652:init_distributed] cdb=None
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-26 16:57:34,501][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 16:57:34,551][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 16:57:34,553][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-06-26 16:57:34,554][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_top1
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_top1/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_top1/model.safetensors.index.json.
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_top1
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_top1
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_top1
Loading model...
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...Total weights: 771, Router weights: 256

Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
Dataset sizes - Train: 6254, Val: 208, Test: 487
cn-g019:3501092:3501092 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.29<0>
cn-g019:3501092:3501092 [0] NCCL INFO cudaDriverVersion 12020
cn-g019:3501092:3501092 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g019:3501092:3501092 [0] NCCL INFO Comm config Blocking set to 1
cn-g019:3501094:3501094 [2] NCCL INFO cudaDriverVersion 12020
cn-g019:3501093:3501093 [1] NCCL INFO cudaDriverVersion 12020
cn-g019:3501095:3501095 [3] NCCL INFO cudaDriverVersion 12020
cn-g019:3501094:3501094 [2] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.29<0>
cn-g019:3501094:3501094 [2] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g019:3501093:3501093 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.29<0>
cn-g019:3501095:3501095 [3] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.29<0>
cn-g019:3501093:3501093 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g019:3501095:3501095 [3] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g019:3501094:3501094 [2] NCCL INFO Comm config Blocking set to 1
cn-g019:3501093:3501093 [1] NCCL INFO Comm config Blocking set to 1
cn-g019:3501095:3501095 [3] NCCL INFO Comm config Blocking set to 1
cn-g019:3501093:3501792 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g019:3501092:3501790 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g019:3501095:3501793 [3] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g019:3501094:3501791 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g019:3501095:3501793 [3] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.29<0>
cn-g019:3501095:3501793 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g019:3501095:3501793 [3] NCCL INFO Using network IB
cn-g019:3501095:3501793 [3] NCCL INFO ncclCommInitRankConfig comm 0x555f945da6f0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xb33e37e739f47509 - Init START
cn-g019:3501093:3501792 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.29<0>
cn-g019:3501092:3501790 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.29<0>
cn-g019:3501093:3501792 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g019:3501093:3501792 [1] NCCL INFO Using network IB
cn-g019:3501092:3501790 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g019:3501092:3501790 [0] NCCL INFO Using network IB
cn-g019:3501093:3501792 [1] NCCL INFO ncclCommInitRankConfig comm 0x55dfaae3acc0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xb33e37e739f47509 - Init START
cn-g019:3501092:3501790 [0] NCCL INFO ncclCommInitRankConfig comm 0x562e414b1a40 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xb33e37e739f47509 - Init START
cn-g019:3501092:3501790 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g019:3501094:3501791 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.29<0>
cn-g019:3501094:3501791 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g019:3501094:3501791 [2] NCCL INFO Using network IB
cn-g019:3501094:3501791 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a17b651d80 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xb33e37e739f47509 - Init START
cn-g019:3501093:3501792 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g019:3501094:3501791 [2] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g019:3501095:3501793 [3] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g019:3501095:3501793 [3] NCCL INFO Bootstrap timings total 0.043574 (create 0.000041, send 0.000176, recv 0.024018, ring 0.000055, delay 0.000000)
cn-g019:3501093:3501792 [1] NCCL INFO Bootstrap timings total 0.020172 (create 0.000023, send 0.000060, recv 0.019250, ring 0.000231, delay 0.000000)
cn-g019:3501092:3501790 [0] NCCL INFO Bootstrap timings total 0.019710 (create 0.000049, send 0.000179, recv 0.000179, ring 0.018881, delay 0.000000)
cn-g019:3501094:3501791 [2] NCCL INFO Bootstrap timings total 0.001150 (create 0.000038, send 0.000160, recv 0.000120, ring 0.000158, delay 0.000000)
cn-g019:3501092:3501790 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g019:3501095:3501793 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g019:3501093:3501792 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g019:3501094:3501791 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g019:3501093:3501792 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g019:3501092:3501790 [0] NCCL INFO comm 0x562e414b1a40 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g019:3501093:3501792 [1] NCCL INFO comm 0x55dfaae3acc0 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g019:3501094:3501791 [2] NCCL INFO comm 0x55a17b651d80 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g019:3501095:3501793 [3] NCCL INFO comm 0x555f945da6f0 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g019:3501092:3501790 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g019:3501093:3501792 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g019:3501094:3501791 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g019:3501095:3501793 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g019:3501095:3501793 [3] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501092:3501790 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g019:3501093:3501792 [1] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501094:3501791 [2] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501092:3501790 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g019:3501092:3501790 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g019:3501092:3501790 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g019:3501092:3501790 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g019:3501092:3501790 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g019:3501092:3501790 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g019:3501092:3501790 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g019:3501092:3501790 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g019:3501092:3501790 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g019:3501092:3501790 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g019:3501092:3501790 [0] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501093:3501806 [1] NCCL INFO [Proxy Service] Device 1 CPU core 8
cn-g019:3501093:3501807 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 12
cn-g019:3501095:3501811 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 10
cn-g019:3501094:3501810 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 13
cn-g019:3501095:3501809 [3] NCCL INFO [Proxy Service] Device 3 CPU core 0
cn-g019:3501094:3501808 [2] NCCL INFO [Proxy Service] Device 2 CPU core 0
cn-g019:3501092:3501790 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g019:3501092:3501812 [0] NCCL INFO [Proxy Service] Device 0 CPU core 9
cn-g019:3501092:3501813 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 1
cn-g019:3501093:3501792 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501093:3501792 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501094:3501791 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501094:3501791 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501092:3501790 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501095:3501793 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501095:3501793 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501092:3501790 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501092:3501790 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g019:3501092:3501790 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g019:3501092:3501790 [0] NCCL INFO ncclCommInitRankConfig comm 0x562e414b1a40 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xb33e37e739f47509 - Init COMPLETE
cn-g019:3501094:3501791 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g019:3501093:3501792 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g019:3501093:3501792 [1] NCCL INFO ncclCommInitRankConfig comm 0x55dfaae3acc0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xb33e37e739f47509 - Init COMPLETE
cn-g019:3501092:3501790 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 0.38 (kernels 0.17, alloc 0.05, bootstrap 0.02, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.03)
cn-g019:3501094:3501791 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a17b651d80 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xb33e37e739f47509 - Init COMPLETE
cn-g019:3501095:3501793 [3] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g019:3501093:3501792 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.38 (kernels 0.17, alloc 0.05, bootstrap 0.02, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.03)
cn-g019:3501094:3501791 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.38 (kernels 0.17, alloc 0.06, bootstrap 0.00, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.03)
cn-g019:3501095:3501793 [3] NCCL INFO ncclCommInitRankConfig comm 0x555f945da6f0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xb33e37e739f47509 - Init COMPLETE
cn-g019:3501095:3501793 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.38 (kernels 0.17, alloc 0.02, bootstrap 0.04, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.02)
cn-g019:3501095:3501817 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501815 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501816 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501814 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501817 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g019:3501094:3501816 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g019:3501093:3501814 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g019:3501092:3501815 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-06-26 16:58:57,976] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-06-26 16:58:57,977] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
cn-g019:3501092:3501092 [0] NCCL INFO Comm config Blocking set to 1
cn-g019:3501092:3501875 [0] NCCL INFO Using network IB
cn-g019:3501092:3501875 [0] NCCL INFO ncclCommInitRankConfig comm 0x562e5b753b40 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xd98e4fd83ced7098 - Init START
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
cn-g019:3501093:3501093 [1] NCCL INFO Comm config Blocking set to 1
cn-g019:3501093:3501887 [1] NCCL INFO Using network IB
cn-g019:3501093:3501887 [1] NCCL INFO ncclCommInitRankConfig comm 0x55dfab5facc0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xd98e4fd83ced7098 - Init START
cn-g019:3501094:3501094 [2] NCCL INFO Comm config Blocking set to 1
cn-g019:3501094:3501893 [2] NCCL INFO Using network IB
cn-g019:3501094:3501893 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a17a651c80 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xd98e4fd83ced7098 - Init START
cn-g019:3501095:3501095 [3] NCCL INFO Comm config Blocking set to 1
cn-g019:3501095:3501896 [3] NCCL INFO Using network IB
cn-g019:3501095:3501896 [3] NCCL INFO ncclCommInitRankConfig comm 0x555f7f7846c0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xd98e4fd83ced7098 - Init START
cn-g019:3501095:3501896 [3] NCCL INFO Bootstrap timings total 0.000474 (create 0.000040, send 0.000084, recv 0.000131, ring 0.000097, delay 0.000000)
cn-g019:3501092:3501875 [0] NCCL INFO Bootstrap timings total 3.176394 (create 0.000024, send 0.000122, recv 2.822294, ring 0.000047, delay 0.000000)
cn-g019:3501093:3501887 [1] NCCL INFO Bootstrap timings total 0.354264 (create 0.000042, send 0.000087, recv 0.244561, ring 0.109416, delay 0.000000)
cn-g019:3501094:3501893 [2] NCCL INFO Bootstrap timings total 0.111276 (create 0.000032, send 0.000078, recv 0.110922, ring 0.000097, delay 0.000000)
cn-g019:3501094:3501893 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g019:3501093:3501887 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g019:3501093:3501887 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g019:3501095:3501896 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g019:3501092:3501875 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g019:3501095:3501896 [3] NCCL INFO comm 0x555f7f7846c0 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g019:3501094:3501893 [2] NCCL INFO comm 0x55a17a651c80 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g019:3501093:3501887 [1] NCCL INFO comm 0x55dfab5facc0 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g019:3501092:3501875 [0] NCCL INFO comm 0x562e5b753b40 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g019:3501095:3501896 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g019:3501095:3501896 [3] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501094:3501893 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g019:3501094:3501893 [2] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501093:3501887 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g019:3501093:3501887 [1] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501092:3501875 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g019:3501092:3501875 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g019:3501092:3501875 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g019:3501092:3501875 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g019:3501092:3501875 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g019:3501092:3501875 [0] NCCL INFO P2P Chunksize set to 524288
cn-g019:3501092:3501875 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g019:3501092:3501897 [0] NCCL INFO [Proxy Service] Device 0 CPU core 7
cn-g019:3501092:3501898 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 11
cn-g019:3501093:3501900 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 9
cn-g019:3501093:3501899 [1] NCCL INFO [Proxy Service] Device 1 CPU core 1
cn-g019:3501095:3501901 [3] NCCL INFO [Proxy Service] Device 3 CPU core 8
cn-g019:3501095:3501902 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 7
cn-g019:3501094:3501904 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 7
cn-g019:3501094:3501903 [2] NCCL INFO [Proxy Service] Device 2 CPU core 1
cn-g019:3501092:3501875 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501092:3501875 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501092:3501875 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g019:3501095:3501896 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501095:3501896 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501093:3501887 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501093:3501887 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501094:3501893 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g019:3501094:3501893 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g019:3501095:3501896 [3] NCCL INFO ncclCommInitRankConfig comm 0x555f7f7846c0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xd98e4fd83ced7098 - Init COMPLETE
cn-g019:3501095:3501896 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.15 (kernels 0.00, alloc 0.00, bootstrap 0.00, allgathers 0.00, topo 0.07, graphs 0.00, connections 0.06, rest 0.03)
cn-g019:3501093:3501887 [1] NCCL INFO ncclCommInitRankConfig comm 0x55dfab5facc0 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xd98e4fd83ced7098 - Init COMPLETE
cn-g019:3501093:3501887 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.51 (kernels 0.00, alloc 0.00, bootstrap 0.36, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.06, rest 0.02)
cn-g019:3501094:3501893 [2] NCCL INFO ncclCommInitRankConfig comm 0x55a17a651c80 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xd98e4fd83ced7098 - Init COMPLETE
cn-g019:3501094:3501893 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.27 (kernels 0.00, alloc 0.00, bootstrap 0.11, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.03)
cn-g019:3501092:3501875 [0] NCCL INFO ncclCommInitRankConfig comm 0x562e5b753b40 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xd98e4fd83ced7098 - Init COMPLETE
cn-g019:3501092:3501875 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 3.33 (kernels 0.00, alloc 0.00, bootstrap 3.18, allgathers 0.00, topo 0.07, graphs 0.00, connections 0.06, rest 0.02)
cn-g019:3501093:3501906 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501095:3501905 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g019:3501093:3501906 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501094:3501907 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g019:3501092:3501908 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g019:3501093:3501906 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g019:3501095:3501905 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g019:3501094:3501907 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
[2025-06-26 16:59:03,486] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-06-26 16:59:03,491] [INFO] [logging.py:128:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-06-26 16:59:03,492] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-06-26 16:59:03,514] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-06-26 16:59:03,514] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-06-26 16:59:03,514] [INFO] [logging.py:128:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-06-26 16:59:03,515] [INFO] [stage_1_and_2.py:149:__init__] Reduce bucket size 5********
[2025-06-26 16:59:03,515] [INFO] [stage_1_and_2.py:150:__init__] Allgather bucket size 2********
[2025-06-26 16:59:03,515] [INFO] [stage_1_and_2.py:151:__init__] CPU Offload: False
[2025-06-26 16:59:03,515] [INFO] [stage_1_and_2.py:152:__init__] Round robin gradient partitioning: False
[2025-06-26 16:59:07,713] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-06-26 16:59:07,716] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB 
[2025-06-26 16:59:07,716] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 40.89 GB, percent = 4.1%
[2025-06-26 16:59:07,892] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-06-26 16:59:07,893] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-06-26 16:59:07,894] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 40.9 GB, percent = 4.1%
[2025-06-26 16:59:07,894] [INFO] [stage_1_and_2.py:544:__init__] optimizer state initialized
[2025-06-26 16:59:08,065] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-06-26 16:59:08,067] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-06-26 16:59:08,067] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 40.9 GB, percent = 4.1%
[2025-06-26 16:59:08,069] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-06-26 16:59:08,070] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-06-26 16:59:08,070] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-06-26 16:59:08,070] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-06-26 16:59:08,074] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-06-26 16:59:08,075] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-06-26 16:59:08,075] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-06-26 16:59:08,075] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-06-26 16:59:08,076] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-06-26 16:59:08,076] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-06-26 16:59:08,076] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-06-26 16:59:08,076] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-06-26 16:59:08,077] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-06-26 16:59:08,077] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-06-26 16:59:08,077] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-06-26 16:59:08,077] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f6d9d756c50>
[2025-06-26 16:59:08,077] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-06-26 16:59:08,078] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-06-26 16:59:08,078] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-06-26 16:59:08,078] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-06-26 16:59:08,078] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-06-26 16:59:08,079] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-06-26 16:59:08,079] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-06-26 16:59:08,079] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-06-26 16:59:08,080] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-06-26 16:59:08,080] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-06-26 16:59:08,080] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-06-26 16:59:08,080] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-06-26 16:59:08,080] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-06-26 16:59:08,081] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-06-26 16:59:08,081] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-06-26 16:59:08,081] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-06-26 16:59:08,081] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-06-26 16:59:08,081] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-06-26 16:59:08,082] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-06-26 16:59:08,082] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-06-26 16:59:08,082] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-06-26 16:59:08,082] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-06-26 16:59:08,082] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-06-26 16:59:08,083] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-06-26 16:59:08,083] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-06-26 16:59:08,083] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-06-26 16:59:08,083] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-06-26 16:59:08,083] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-06-26 16:59:08,083] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-06-26 16:59:08,084] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-06-26 16:59:08,084] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-06-26 16:59:08,084] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-06-26 16:59:08,084] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-06-26 16:59:08,085] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-06-26 16:59:08,085] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-06-26 16:59:08,085] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-06-26 16:59:08,085] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-06-26 16:59:08,085] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-06-26 16:59:08,086] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-06-26 16:59:08,086] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-06-26 16:59:08,086] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-06-26 16:59:08,086] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-06-26 16:59:08,086] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-06-26 16:59:08,087] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-06-26 16:59:08,087] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-06-26 16:59:08,087] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-06-26 16:59:08,087] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-06-26 16:59:08,087] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-06-26 16:59:08,088] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-06-26 16:59:08,088] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-06-26 16:59:08,088] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-06-26 16:59:08,088] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-06-26 16:59:08,088] [INFO] [config.py:1003:print]   train_batch_size ............. 16
[2025-06-26 16:59:08,089] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-06-26 16:59:08,089] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-06-26 16:59:08,089] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-06-26 16:59:08,089] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-06-26 16:59:08,089] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-06-26 16:59:08,090] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-06-26 16:59:08,090] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  True
[2025-06-26 16:59:08,090] [INFO] [config.py:1003:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=5******** use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=2******** overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1********0 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1********0 max_reuse_distance=1********0 gather_16bit_weights_on_model_save=False use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-06-26 16:59:08,090] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-06-26 16:59:08,090] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-06-26 16:59:08,091] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 2
[2025-06-26 16:59:08,091] [INFO] [config.py:989:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2, 
        "allgather_partitions": true, 
        "allgather_bucket_size": 2.000000e+08, 
        "overlap_comm": true, 
        "reduce_scatter": true, 
        "contiguous_gradients": true
    }, 
    "gradient_accumulation_steps": 4, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 16, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "bf16": {
        "enabled": true
    }, 
    "fp16": {
        "enabled": false
    }, 
    "zero_allow_untested_optimizer": true
}
{'loss': 1.6195, 'grad_norm': 10.632771492004395, 'learning_rate': 9.997442455242967e-05, 'epoch': 0.0}
{'loss': 1.3768, 'grad_norm': 2.5524582862854004, 'learning_rate': 9.936061381074169e-05, 'epoch': 0.06}
{'loss': 1.2742, 'grad_norm': 2.630218267440796, 'learning_rate': 9.872122762148338e-05, 'epoch': 0.13}
{'loss': 1.1997, 'grad_norm': 2.691769599914551, 'learning_rate': 9.808184143222507e-05, 'epoch': 0.19}
{'loss': 1.1547, 'grad_norm': 1.9462693929672241, 'learning_rate': 9.744245524296676e-05, 'epoch': 0.26}
{'loss': 1.1101, 'grad_norm': 1.6229157447814941, 'learning_rate': 9.680306905370844e-05, 'epoch': 0.32}
{'loss': 1.1091, 'grad_norm': 1.5950418710708618, 'learning_rate': 9.616368286445013e-05, 'epoch': 0.38}
{'loss': 1.0402, 'grad_norm': 1.65256667137146, 'learning_rate': 9.552429667519182e-05, 'epoch': 0.45}
{'loss': 1.0141, 'grad_norm': 1.677253007888794, 'learning_rate': 9.488491048593351e-05, 'epoch': 0.51}
{'loss': 0.9808, 'grad_norm': 1.6706504821777344, 'learning_rate': 9.42455242966752e-05, 'epoch': 0.58}
{'loss': 0.9745, 'grad_norm': 1.3093593120574951, 'learning_rate': 9.360613810741689e-05, 'epoch': 0.64}
{'loss': 0.9458, 'grad_norm': 1.3925443887710571, 'learning_rate': 9.296675191815857e-05, 'epoch': 0.7}
{'loss': 0.904, 'grad_norm': 1.508933663368225, 'learning_rate': 9.232736572890026e-05, 'epoch': 0.77}
{'loss': 0.8908, 'grad_norm': 1.324893832206726, 'learning_rate': 9.168797953964195e-05, 'epoch': 0.83}
{'loss': 0.8408, 'grad_norm': 1.331591010093689, 'learning_rate': 9.104859335038364e-05, 'epoch': 0.9}
{'loss': 0.8296, 'grad_norm': 1.2680795192718506, 'learning_rate': 9.040920716112533e-05, 'epoch': 0.96}
{'loss': 0.7869, 'grad_norm': 1.3790757656097412, 'learning_rate': 8.976982097186701e-05, 'epoch': 1.02}
{'loss': 0.7428, 'grad_norm': 1.3410478830337524, 'learning_rate': 8.91304347826087e-05, 'epoch': 1.09}
{'loss': 0.6992, 'grad_norm': 1.31882905960083, 'learning_rate': 8.849104859335039e-05, 'epoch': 1.15}
{'loss': 0.7017, 'grad_norm': 1.0469553470611572, 'learning_rate': 8.785166240409208e-05, 'epoch': 1.21}
{'loss': 0.6845, 'grad_norm': 1.1071034669876099, 'learning_rate': 8.721227621483377e-05, 'epoch': 1.28}
{'loss': 0.6575, 'grad_norm': 1.2448406219482422, 'learning_rate': 8.657289002557545e-05, 'epoch': 1.34}
{'loss': 0.6898, 'grad_norm': 1.5049774646759033, 'learning_rate': 8.593350383631714e-05, 'epoch': 1.41}
{'loss': 0.6651, 'grad_norm': 1.3261045217514038, 'learning_rate': 8.529411764705883e-05, 'epoch': 1.47}
{'loss': 0.6261, 'grad_norm': 1.1551554203033447, 'learning_rate': 8.465473145780052e-05, 'epoch': 1.53}
{'loss': 0.6252, 'grad_norm': 0.9156345725059509, 'learning_rate': 8.40153452685422e-05, 'epoch': 1.6}
{'loss': 0.6186, 'grad_norm': 1.0600916147232056, 'learning_rate': 8.33759590792839e-05, 'epoch': 1.66}
{'loss': 0.5967, 'grad_norm': 1.2359752655029297, 'learning_rate': 8.273657289002558e-05, 'epoch': 1.73}
{'loss': 0.6309, 'grad_norm': 1.1147127151489258, 'learning_rate': 8.209718670076727e-05, 'epoch': 1.79}
{'loss': 0.5767, 'grad_norm': 1.2418144941329956, 'learning_rate': 8.145780051150896e-05, 'epoch': 1.85}
{'loss': 0.5946, 'grad_norm': 1.0088506937026978, 'learning_rate': 8.081841432225065e-05, 'epoch': 1.92}
{'loss': 0.5542, 'grad_norm': 0.9371520280838013, 'learning_rate': 8.017902813299233e-05, 'epoch': 1.98}
{'loss': 0.5017, 'grad_norm': 0.942845344543457, 'learning_rate': 7.953964194373402e-05, 'epoch': 2.05}
{'loss': 0.4754, 'grad_norm': 1.2281566858291626, 'learning_rate': 7.890025575447571e-05, 'epoch': 2.11}
{'loss': 0.4651, 'grad_norm': 1.0565241575241089, 'learning_rate': 7.82608695652174e-05, 'epoch': 2.17}
{'loss': 0.4557, 'grad_norm': 1.6835532188415527, 'learning_rate': 7.762148337595909e-05, 'epoch': 2.24}
{'loss': 0.4637, 'grad_norm': 0.8693986535072327, 'learning_rate': 7.698209718670077e-05, 'epoch': 2.3}
{'loss': 0.4329, 'grad_norm': 1.2182962894439697, 'learning_rate': 7.634271099744246e-05, 'epoch': 2.37}
{'loss': 0.4258, 'grad_norm': 0.8708282113075256, 'learning_rate': 7.570332480818415e-05, 'epoch': 2.43}
{'loss': 0.4067, 'grad_norm': 1.0619364976882935, 'learning_rate': 7.506393861892584e-05, 'epoch': 2.49}
{'loss': 0.4273, 'grad_norm': 1.043782353401184, 'learning_rate': 7.442455242966753e-05, 'epoch': 2.56}
{'loss': 0.4449, 'grad_norm': 1.167708158493042, 'learning_rate': 7.378516624040921e-05, 'epoch': 2.62}
{'loss': 0.3818, 'grad_norm': 0.9600796699523926, 'learning_rate': 7.31457800511509e-05, 'epoch': 2.69}
{'loss': 0.3811, 'grad_norm': 1.008018970489502, 'learning_rate': 7.250639386189259e-05, 'epoch': 2.75}
{'loss': 0.3778, 'grad_norm': 0.9440929889678955, 'learning_rate': 7.186700767263428e-05, 'epoch': 2.81}
{'loss': 0.3694, 'grad_norm': 0.7666249871253967, 'learning_rate': 7.122762148337597e-05, 'epoch': 2.88}
{'loss': 0.3701, 'grad_norm': 0.9569199681282043, 'learning_rate': 7.058823529411765e-05, 'epoch': 2.94}
{'loss': 0.352, 'grad_norm': 1.0718661546707153, 'learning_rate': 6.994884910485934e-05, 'epoch': 3.01}
{'loss': 0.3145, 'grad_norm': 0.9893192052841187, 'learning_rate': 6.930946291560103e-05, 'epoch': 3.07}
{'loss': 0.3076, 'grad_norm': 0.8603441119194031, 'learning_rate': 6.867007672634272e-05, 'epoch': 3.13}
{'loss': 0.2839, 'grad_norm': 1.1745446920394897, 'learning_rate': 6.80306905370844e-05, 'epoch': 3.2}
{'loss': 0.3132, 'grad_norm': 1.2770360708236694, 'learning_rate': 6.73913043478261e-05, 'epoch': 3.26}
{'loss': 0.2903, 'grad_norm': 0.6637508273124695, 'learning_rate': 6.675191815856778e-05, 'epoch': 3.32}
{'loss': 0.287, 'grad_norm': 0.673276960849762, 'learning_rate': 6.611253196930947e-05, 'epoch': 3.39}
{'loss': 0.2682, 'grad_norm': 0.8873740434646606, 'learning_rate': 6.547314578005116e-05, 'epoch': 3.45}
{'loss': 0.2895, 'grad_norm': 0.6364359855651855, 'learning_rate': 6.483375959079285e-05, 'epoch': 3.52}
{'loss': 0.2703, 'grad_norm': 0.8495316505432129, 'learning_rate': 6.419437340153452e-05, 'epoch': 3.58}
{'loss': 0.2567, 'grad_norm': 0.8973718881607056, 'learning_rate': 6.355498721227622e-05, 'epoch': 3.64}
{'loss': 0.2532, 'grad_norm': 0.809442400932312, 'learning_rate': 6.29156010230179e-05, 'epoch': 3.71}
{'loss': 0.2706, 'grad_norm': 0.9007883667945862, 'learning_rate': 6.22762148337596e-05, 'epoch': 3.77}
{'loss': 0.2415, 'grad_norm': 0.731759250164032, 'learning_rate': 6.163682864450127e-05, 'epoch': 3.84}
{'loss': 0.2611, 'grad_norm': 0.9150570631027222, 'learning_rate': 6.099744245524297e-05, 'epoch': 3.9}
{'loss': 0.254, 'grad_norm': 0.8322410583496094, 'learning_rate': 6.035805626598465e-05, 'epoch': 3.96}
{'loss': 0.2347, 'grad_norm': 0.7145822048187256, 'learning_rate': 5.9718670076726344e-05, 'epoch': 4.03}
{'loss': 0.2034, 'grad_norm': 0.7279718518257141, 'learning_rate': 5.9079283887468026e-05, 'epoch': 4.09}
{'loss': 0.206, 'grad_norm': 0.9875255227088928, 'learning_rate': 5.843989769820972e-05, 'epoch': 4.16}
{'loss': 0.1869, 'grad_norm': 0.6587445139884949, 'learning_rate': 5.78005115089514e-05, 'epoch': 4.22}
{'loss': 0.1947, 'grad_norm': 0.6929172277450562, 'learning_rate': 5.7161125319693096e-05, 'epoch': 4.28}
{'loss': 0.2017, 'grad_norm': 0.708968460559845, 'learning_rate': 5.652173913043478e-05, 'epoch': 4.35}
{'loss': 0.1941, 'grad_norm': 0.8261646628379822, 'learning_rate': 5.588235294117647e-05, 'epoch': 4.41}
{'loss': 0.1831, 'grad_norm': 0.7672910690307617, 'learning_rate': 5.5242966751918154e-05, 'epoch': 4.48}
{'loss': 0.1979, 'grad_norm': 0.8053486943244934, 'learning_rate': 5.460358056265985e-05, 'epoch': 4.54}
{'loss': 0.1693, 'grad_norm': 0.6988288164138794, 'learning_rate': 5.396419437340153e-05, 'epoch': 4.6}
{'loss': 0.1867, 'grad_norm': 0.6933002471923828, 'learning_rate': 5.3324808184143225e-05, 'epoch': 4.67}
{'loss': 0.1899, 'grad_norm': 0.8191375136375427, 'learning_rate': 5.268542199488491e-05, 'epoch': 4.73}
{'loss': 0.1976, 'grad_norm': 0.6938356161117554, 'learning_rate': 5.20460358056266e-05, 'epoch': 4.8}
{'loss': 0.1649, 'grad_norm': 0.8345311284065247, 'learning_rate': 5.140664961636829e-05, 'epoch': 4.86}
{'loss': 0.1567, 'grad_norm': 0.7657214403152466, 'learning_rate': 5.076726342710998e-05, 'epoch': 4.92}
{'loss': 0.172, 'grad_norm': 0.5919106006622314, 'learning_rate': 5.0127877237851665e-05, 'epoch': 4.99}
{'loss': 0.1507, 'grad_norm': 0.7011980414390564, 'learning_rate': 4.948849104859335e-05, 'epoch': 5.05}
{'loss': 0.1654, 'grad_norm': 0.7291375994682312, 'learning_rate': 4.884910485933504e-05, 'epoch': 5.12}
{'loss': 0.1489, 'grad_norm': 0.5733879208564758, 'learning_rate': 4.820971867007673e-05, 'epoch': 5.18}
{'loss': 0.1574, 'grad_norm': 0.6650118231773376, 'learning_rate': 4.757033248081842e-05, 'epoch': 5.24}
{'loss': 0.132, 'grad_norm': 0.5725044012069702, 'learning_rate': 4.6930946291560105e-05, 'epoch': 5.31}
{'loss': 0.1381, 'grad_norm': 0.555702269077301, 'learning_rate': 4.629156010230179e-05, 'epoch': 5.37}
{'loss': 0.132, 'grad_norm': 0.4288429915904999, 'learning_rate': 4.565217391304348e-05, 'epoch': 5.43}
{'loss': 0.1169, 'grad_norm': 0.5596100687980652, 'learning_rate': 4.501278772378517e-05, 'epoch': 5.5}
{'loss': 0.1144, 'grad_norm': 0.7542898654937744, 'learning_rate': 4.437340153452686e-05, 'epoch': 5.56}
{'loss': 0.135, 'grad_norm': 0.531470775604248, 'learning_rate': 4.3734015345268545e-05, 'epoch': 5.63}
{'loss': 0.1321, 'grad_norm': 0.9036681652069092, 'learning_rate': 4.309462915601023e-05, 'epoch': 5.69}
{'loss': 0.1279, 'grad_norm': 0.6665363907814026, 'learning_rate': 4.245524296675192e-05, 'epoch': 5.75}
{'loss': 0.1216, 'grad_norm': 0.4795217216014862, 'learning_rate': 4.181585677749361e-05, 'epoch': 5.82}
{'loss': 0.1235, 'grad_norm': 0.8350366353988647, 'learning_rate': 4.11764705882353e-05, 'epoch': 5.88}
{'loss': 0.1252, 'grad_norm': 0.9655511379241943, 'learning_rate': 4.0537084398976985e-05, 'epoch': 5.95}
{'loss': 0.1101, 'grad_norm': 0.46040743589401245, 'learning_rate': 3.989769820971867e-05, 'epoch': 6.01}
{'loss': 0.1135, 'grad_norm': 0.4487968683242798, 'learning_rate': 3.925831202046036e-05, 'epoch': 6.07}
{'loss': 0.1026, 'grad_norm': 0.5507612228393555, 'learning_rate': 3.861892583120205e-05, 'epoch': 6.14}
{'loss': 0.1078, 'grad_norm': 0.518158495426178, 'learning_rate': 3.797953964194374e-05, 'epoch': 6.2}
{'loss': 0.1085, 'grad_norm': 0.38451769948005676, 'learning_rate': 3.7340153452685426e-05, 'epoch': 6.27}
{'loss': 0.1048, 'grad_norm': 0.34043920040130615, 'learning_rate': 3.6700767263427114e-05, 'epoch': 6.33}
{'loss': 0.1002, 'grad_norm': 0.3991411626338959, 'learning_rate': 3.60613810741688e-05, 'epoch': 6.39}
{'loss': 0.0972, 'grad_norm': 0.5181810259819031, 'learning_rate': 3.542199488491049e-05, 'epoch': 6.46}
{'loss': 0.0969, 'grad_norm': 0.40468353033065796, 'learning_rate': 3.478260869565218e-05, 'epoch': 6.52}
{'loss': 0.1071, 'grad_norm': 0.7002722024917603, 'learning_rate': 3.4143222506393866e-05, 'epoch': 6.59}
{'loss': 0.0948, 'grad_norm': 0.3907013535499573, 'learning_rate': 3.3503836317135554e-05, 'epoch': 6.65}
{'loss': 0.0899, 'grad_norm': 0.4552641212940216, 'learning_rate': 3.286445012787724e-05, 'epoch': 6.71}
{'loss': 0.098, 'grad_norm': 0.6204559206962585, 'learning_rate': 3.222506393861893e-05, 'epoch': 6.78}
{'loss': 0.099, 'grad_norm': 0.42420703172683716, 'learning_rate': 3.158567774936062e-05, 'epoch': 6.84}
{'loss': 0.093, 'grad_norm': 2.338109016418457, 'learning_rate': 3.0946291560102306e-05, 'epoch': 6.91}
{'loss': 0.0981, 'grad_norm': 0.4423053562641144, 'learning_rate': 3.030690537084399e-05, 'epoch': 6.97}
{'loss': 0.0838, 'grad_norm': 0.32630863785743713, 'learning_rate': 2.966751918158568e-05, 'epoch': 7.03}
{'loss': 0.0788, 'grad_norm': 0.5107914209365845, 'learning_rate': 2.9028132992327367e-05, 'epoch': 7.1}
{'loss': 0.0872, 'grad_norm': 0.377790629863739, 'learning_rate': 2.8388746803069055e-05, 'epoch': 7.16}
{'loss': 0.083, 'grad_norm': 0.37279221415519714, 'learning_rate': 2.7749360613810743e-05, 'epoch': 7.23}
{'loss': 0.0801, 'grad_norm': 0.3999655544757843, 'learning_rate': 2.710997442455243e-05, 'epoch': 7.29}
{'loss': 0.0792, 'grad_norm': 0.32713958621025085, 'learning_rate': 2.647058823529412e-05, 'epoch': 7.35}
{'loss': 0.085, 'grad_norm': 0.3363380432128906, 'learning_rate': 2.5831202046035807e-05, 'epoch': 7.42}
{'loss': 0.0773, 'grad_norm': 0.23274599015712738, 'learning_rate': 2.5191815856777495e-05, 'epoch': 7.48}
{'loss': 0.0799, 'grad_norm': 0.24768275022506714, 'learning_rate': 2.4552429667519183e-05, 'epoch': 7.54}
{'loss': 0.0795, 'grad_norm': 0.28720569610595703, 'learning_rate': 2.391304347826087e-05, 'epoch': 7.61}
{'loss': 0.0814, 'grad_norm': 0.35844582319259644, 'learning_rate': 2.327365728900256e-05, 'epoch': 7.67}
{'loss': 0.0833, 'grad_norm': 0.3816165030002594, 'learning_rate': 2.2634271099744247e-05, 'epoch': 7.74}
{'loss': 0.0786, 'grad_norm': 0.3424679636955261, 'learning_rate': 2.1994884910485935e-05, 'epoch': 7.8}
{'loss': 0.0738, 'grad_norm': 0.2802965044975281, 'learning_rate': 2.1355498721227623e-05, 'epoch': 7.86}
{'loss': 0.0668, 'grad_norm': 0.21092131733894348, 'learning_rate': 2.071611253196931e-05, 'epoch': 7.93}
{'loss': 0.0725, 'grad_norm': 0.28730523586273193, 'learning_rate': 2.0076726342711e-05, 'epoch': 7.99}
{'loss': 0.0688, 'grad_norm': 0.2961525321006775, 'learning_rate': 1.9437340153452684e-05, 'epoch': 8.06}
{'loss': 0.0704, 'grad_norm': 0.3509763777256012, 'learning_rate': 1.8797953964194372e-05, 'epoch': 8.12}
{'loss': 0.0666, 'grad_norm': 0.5373799204826355, 'learning_rate': 1.815856777493606e-05, 'epoch': 8.18}
{'loss': 0.0722, 'grad_norm': 0.380170077085495, 'learning_rate': 1.7519181585677748e-05, 'epoch': 8.25}
{'loss': 0.0728, 'grad_norm': 0.7291166186332703, 'learning_rate': 1.6879795396419436e-05, 'epoch': 8.31}
{'loss': 0.0672, 'grad_norm': 0.28146788477897644, 'learning_rate': 1.6240409207161124e-05, 'epoch': 8.38}
{'loss': 0.0634, 'grad_norm': 0.3535691499710083, 'learning_rate': 1.5601023017902812e-05, 'epoch': 8.44}
{'loss': 0.0624, 'grad_norm': 0.265070378780365, 'learning_rate': 1.4961636828644502e-05, 'epoch': 8.5}
{'loss': 0.0652, 'grad_norm': 0.2867913544178009, 'learning_rate': 1.432225063938619e-05, 'epoch': 8.57}
{'loss': 0.06, 'grad_norm': 0.17821276187896729, 'learning_rate': 1.3682864450127878e-05, 'epoch': 8.63}
{'loss': 0.0665, 'grad_norm': 0.24416685104370117, 'learning_rate': 1.3043478260869566e-05, 'epoch': 8.7}
{'loss': 0.0638, 'grad_norm': 0.22443735599517822, 'learning_rate': 1.2404092071611254e-05, 'epoch': 8.76}
{'loss': 0.0707, 'grad_norm': 0.4545404016971588, 'learning_rate': 1.1764705882352942e-05, 'epoch': 8.82}
{'loss': 0.0634, 'grad_norm': 0.21449854969978333, 'learning_rate': 1.112531969309463e-05, 'epoch': 8.89}
{'loss': 0.0668, 'grad_norm': 0.25688636302948, 'learning_rate': 1.0485933503836318e-05, 'epoch': 8.95}
{'loss': 0.0661, 'grad_norm': 0.24063052237033844, 'learning_rate': 9.846547314578006e-06, 'epoch': 9.02}
{'loss': 0.061, 'grad_norm': 0.22177742421627045, 'learning_rate': 9.207161125319694e-06, 'epoch': 9.08}
{'loss': 0.0584, 'grad_norm': 0.10697188973426819, 'learning_rate': 8.567774936061382e-06, 'epoch': 9.14}
{'loss': 0.0562, 'grad_norm': 0.33973875641822815, 'learning_rate': 7.92838874680307e-06, 'epoch': 9.21}
{'loss': 0.0567, 'grad_norm': 0.3767911195755005, 'learning_rate': 7.289002557544758e-06, 'epoch': 9.27}
{'loss': 0.0546, 'grad_norm': 0.15473955869674683, 'learning_rate': 6.649616368286446e-06, 'epoch': 9.34}
{'loss': 0.0616, 'grad_norm': 0.1897890865802765, 'learning_rate': 6.010230179028133e-06, 'epoch': 9.4}
{'loss': 0.0616, 'grad_norm': 0.3863186836242676, 'learning_rate': 5.370843989769821e-06, 'epoch': 9.46}
{'loss': 0.0586, 'grad_norm': 0.1735372096300125, 'learning_rate': 4.731457800511509e-06, 'epoch': 9.53}
{'loss': 0.0568, 'grad_norm': 0.13463696837425232, 'learning_rate': 4.092071611253197e-06, 'epoch': 9.59}
{'loss': 0.0559, 'grad_norm': 0.23357930779457092, 'learning_rate': 3.4526854219948846e-06, 'epoch': 9.65}
{'loss': 0.062, 'grad_norm': 0.14694269001483917, 'learning_rate': 2.813299232736573e-06, 'epoch': 9.72}
{'loss': 0.0583, 'grad_norm': 0.24968887865543365, 'learning_rate': 2.173913043478261e-06, 'epoch': 9.78}
{'loss': 0.06, 'grad_norm': 0.1910003274679184, 'learning_rate': 1.534526854219949e-06, 'epoch': 9.85}
{'loss': 0.0595, 'grad_norm': 0.21720150113105774, 'learning_rate': 8.951406649616369e-07, 'epoch': 9.91}
{'loss': 0.0537, 'grad_norm': 0.19547539949417114, 'learning_rate': 2.5575447570332484e-07, 'epoch': 9.97}
[2025-06-26 18:27:23,536] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step3910 is about to be saved!
[2025-06-26 18:27:27,088] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt
[2025-06-26 18:27:27,089] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt...
[2025-06-26 18:28:33,435] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910/global_step3910/mp_rank_00_model_states.pt.
[2025-06-26 18:28:33,451] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt...
[2025-06-26 18:28:44,312] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt.
[2025-06-26 18:28:44,317] [INFO] [engine.py:3536:_save_zero_checkpoint] zero checkpoint saved data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910/global_step3910/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt
[2025-06-26 18:28:44,317] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step3910 is ready now!
{'train_runtime': 5382.5911, 'train_samples_per_second': 11.619, 'train_steps_per_second': 0.726, 'train_loss': 0.30002634148768453, 'epoch': 10.0}
Training completed!
Running final evaluation...
Evaluation metrics at step 3910: {'eval_loss': 0.06721542775630951, 'eval_runtime': 5.9483, 'eval_samples_per_second': 34.968, 'eval_steps_per_second': 8.742}Evaluation metrics at step 3910: {'eval_loss': 0.06721542775630951, 'eval_runtime': 5.9559, 'eval_samples_per_second': 34.923, 'eval_steps_per_second': 8.731}Evaluation metrics at step 3910: {'eval_loss': 0.06721542775630951, 'eval_runtime': 5.9498, 'eval_samples_per_second': 34.959, 'eval_steps_per_second': 8.74}


Final evaluation results: {'eval_loss': 0.06721542775630951, 'eval_runtime': 5.9498, 'eval_samples_per_second': 34.959, 'eval_steps_per_second': 8.74, 'epoch': 10.0}
Evaluation metrics at step 3910: {'eval_loss': 0.06721542775630951, 'eval_runtime': 5.9651, 'eval_samples_per_second': 34.87, 'eval_steps_per_second': 8.717}
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501093:3501806 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501095:3521303 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501093:3501806 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501095:3501809 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501095:3501809 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501093:3521302 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501093:3501806 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501094:3501808 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g019:3501095:3501809 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g019:3501094:3521305 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g019:3501092:3501897 [0] NCCL INFO [Service thread] Connection closed by localRank 3
cn-g019:3501092:3501897 [0] NCCL INFO [Service thread] Connection closed by localRank 1
cn-g019:3501092:3501897 [0] NCCL INFO [Service thread] Connection closed by localRank 2
cn-g019:3501092:3521307 [0] NCCL INFO comm 0x562e5b753b40 rank 0 nranks 4 cudaDev 0 busId 1000 - Destroy COMPLETE
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Thu Jun 26 18:49:12 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3501095
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 6724888 ms
            Is Running                    : 0
        Process ID                        : 3501093
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 6725465 ms
            Is Running                    : 0
        Process ID                        : 3501094
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 6725487 ms
            Is Running                    : 0
        Process ID                        : 3501092
            GPU Utilization               : 60 %
            Memory Utilization            : 19 %
            Max memory usage              : 41570 MiB
            Time                          : 6725818 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3501095
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6724883 ms
            Is Running                    : 0
        Process ID                        : 3501093
            GPU Utilization               : 61 %
            Memory Utilization            : 20 %
            Max memory usage              : 41230 MiB
            Time                          : 6725460 ms
            Is Running                    : 0
        Process ID                        : 3501094
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6725485 ms
            Is Running                    : 0
        Process ID                        : 3501092
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6725816 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3501095
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6724881 ms
            Is Running                    : 0
        Process ID                        : 3501093
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6725458 ms
            Is Running                    : 0
        Process ID                        : 3501094
            GPU Utilization               : 59 %
            Memory Utilization            : 20 %
            Max memory usage              : 39004 MiB
            Time                          : 6725482 ms
            Is Running                    : 0
        Process ID                        : 3501092
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6725814 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3501095
            GPU Utilization               : 60 %
            Memory Utilization            : 20 %
            Max memory usage              : 37962 MiB
            Time                          : 6724879 ms
            Is Running                    : 0
        Process ID                        : 3501093
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 6725456 ms
            Is Running                    : 0
        Process ID                        : 3501094
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 6725480 ms
            Is Running                    : 0
        Process ID                        : 3501092
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 6725812 ms
            Is Running                    : 0

Thu Jun 26 18:49:13 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   32C    P0             115W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   26C    P0              88W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   32C    P0             117W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   29C    P0             115W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
