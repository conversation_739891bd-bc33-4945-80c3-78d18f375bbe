[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: marya<PERSON>ha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250626_142654-g4psorf6
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-drug_abuse_pubmedqau-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/g4psorf6

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:02<00:02,  2.51s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:03<00:00,  1.77s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:03<00:00,  1.88s/it]

  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 205887.22it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:02<00:02,  2.28s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:08<00:00,  4.64s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:08<00:00,  4.29s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/6254 [00:00<?, ? examples/s]
Map:  16%|█▌        | 1000/6254 [00:00<00:00, 8125.63 examples/s]
Map:  32%|███▏      | 2000/6254 [00:00<00:00, 8550.31 examples/s]
Map:  48%|████▊     | 3000/6254 [00:00<00:00, 8506.70 examples/s]
Map:  64%|██████▍   | 4000/6254 [00:00<00:00, 8540.10 examples/s]
Map:  80%|███████▉  | 5000/6254 [00:00<00:00, 8486.05 examples/s]
Map:  96%|█████████▌| 6000/6254 [00:00<00:00, 8558.96 examples/s]
Map: 100%|██████████| 6254/6254 [00:00<00:00, 8349.75 examples/s]

Map:   0%|          | 0/208 [00:00<?, ? examples/s]
Map: 100%|██████████| 208/208 [00:00<00:00, 6360.24 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
Using auto half precision backend
***** Running training *****
  Num examples = 6,254
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Training with DataParallel so batch size has been adjusted to: 4
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 3,910
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/3910 [00:00<?, ?it/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

  0%|          | 1/3910 [00:07<7:56:42,  7.32s/it]
                                                  

  0%|          | 1/3910 [00:07<7:56:42,  7.32s/it]
  0%|          | 2/3910 [00:10<5:26:58,  5.02s/it]
  0%|          | 3/3910 [00:14<4:38:38,  4.28s/it]
  0%|          | 4/3910 [00:17<4:10:32,  3.85s/it]
  0%|          | 5/3910 [00:20<3:59:35,  3.68s/it]
  0%|          | 6/3910 [00:24<3:53:26,  3.59s/it]
  0%|          | 7/3910 [00:27<3:45:09,  3.46s/it]
  0%|          | 8/3910 [00:30<3:43:28,  3.44s/it]
  0%|          | 9/3910 [00:33<3:37:55,  3.35s/it]
  0%|          | 10/3910 [00:37<3:37:53,  3.35s/it]
  0%|          | 11/3910 [00:40<3:38:36,  3.36s/it]
  0%|          | 12/3910 [00:43<3:37:34,  3.35s/it]
  0%|          | 13/3910 [00:47<3:37:36,  3.35s/it]
  0%|          | 14/3910 [00:50<3:38:33,  3.37s/it]
  0%|          | 15/3910 [00:54<3:39:05,  3.37s/it]
  0%|          | 16/3910 [00:57<3:35:17,  3.32s/it]
  0%|          | 17/3910 [01:00<3:36:13,  3.33s/it]
  0%|          | 18/3910 [01:03<3:33:14,  3.29s/it]
  0%|          | 19/3910 [01:07<3:34:37,  3.31s/it]
  1%|          | 20/3910 [01:10<3:36:08,  3.33s/it]
  1%|          | 21/3910 [01:13<3:32:46,  3.28s/it]
  1%|          | 22/3910 [01:17<3:34:28,  3.31s/it]
  1%|          | 23/3910 [01:20<3:35:47,  3.33s/it]
  1%|          | 24/3910 [01:23<3:33:13,  3.29s/it]
  1%|          | 25/3910 [01:27<3:34:56,  3.32s/it]
                                                   

  1%|          | 25/3910 [01:34<3:34:56,  3.32s/it]
  1%|          | 26/3910 [01:37<6:00:56,  5.58s/it]
  1%|          | 27/3910 [01:41<5:14:40,  4.86s/it]
  1%|          | 28/3910 [01:44<4:45:57,  4.42s/it]
  1%|          | 29/3910 [01:47<4:25:43,  4.11s/it]
  1%|          | 30/3910 [01:51<4:07:26,  3.83s/it]
  1%|          | 31/3910 [01:54<3:59:03,  3.70s/it]
  1%|          | 32/3910 [01:57<3:53:07,  3.61s/it]
  1%|          | 33/3910 [02:01<3:44:56,  3.48s/it]
  1%|          | 34/3910 [02:04<3:43:04,  3.45s/it]
  1%|          | 35/3910 [02:07<3:41:10,  3.42s/it]
  1%|          | 36/3910 [02:10<3:36:40,  3.36s/it]
  1%|          | 37/3910 [02:14<3:37:24,  3.37s/it]
  1%|          | 38/3910 [02:17<3:38:02,  3.38s/it]
  1%|          | 39/3910 [02:20<3:34:11,  3.32s/it]
  1%|          | 40/3910 [02:24<3:35:25,  3.34s/it]
  1%|          | 41/3910 [02:27<3:35:45,  3.35s/it]
  1%|          | 42/3910 [02:30<3:32:35,  3.30s/it]
  1%|          | 43/3910 [02:34<3:34:26,  3.33s/it]
  1%|          | 44/3910 [02:37<3:35:23,  3.34s/it]
  1%|          | 45/3910 [02:40<3:32:39,  3.30s/it]
  1%|          | 46/3910 [02:44<3:33:54,  3.32s/it]
  1%|          | 47/3910 [02:47<3:35:04,  3.34s/it]
  1%|          | 48/3910 [02:50<3:32:06,  3.30s/it]
  1%|▏         | 49/3910 [02:54<3:33:46,  3.32s/it]
  1%|▏         | 50/3910 [02:57<3:35:03,  3.34s/it]
                                                   

  1%|▏         | 50/3910 [02:57<3:35:03,  3.34s/it]
  1%|▏         | 51/3910 [03:00<3:32:16,  3.30s/it]
  1%|▏         | 52/3910 [03:04<3:34:08,  3.33s/it]
  1%|▏         | 53/3910 [03:07<3:35:02,  3.35s/it]
  1%|▏         | 54/3910 [03:10<3:32:22,  3.30s/it]
  1%|▏         | 55/3910 [03:14<3:33:33,  3.32s/it]
  1%|▏         | 56/3910 [03:17<3:34:55,  3.35s/it]
  1%|▏         | 57/3910 [03:20<3:32:09,  3.30s/it]
  1%|▏         | 58/3910 [03:24<3:33:49,  3.33s/it]
  2%|▏         | 59/3910 [03:27<3:34:40,  3.34s/it]
  2%|▏         | 60/3910 [03:30<3:31:22,  3.29s/it]
  2%|▏         | 61/3910 [03:34<3:32:57,  3.32s/it]
  2%|▏         | 62/3910 [03:37<3:34:26,  3.34s/it]
  2%|▏         | 63/3910 [03:40<3:31:09,  3.29s/it]
  2%|▏         | 64/3910 [03:43<3:32:24,  3.31s/it]
  2%|▏         | 65/3910 [03:47<3:29:59,  3.28s/it]
  2%|▏         | 66/3910 [03:50<3:31:36,  3.30s/it]
  2%|▏         | 67/3910 [03:53<3:33:03,  3.33s/it]
  2%|▏         | 68/3910 [03:57<3:30:38,  3.29s/it]
  2%|▏         | 69/3910 [04:00<3:32:43,  3.32s/it]
  2%|▏         | 70/3910 [04:03<3:33:40,  3.34s/it]
  2%|▏         | 71/3910 [04:07<3:31:23,  3.30s/it]
  2%|▏         | 72/3910 [04:10<3:32:39,  3.32s/it]
  2%|▏         | 73/3910 [04:13<3:33:38,  3.34s/it]
  2%|▏         | 74/3910 [04:17<3:34:47,  3.36s/it]
  2%|▏         | 75/3910 [04:20<3:31:38,  3.31s/it]
                                                   

  2%|▏         | 75/3910 [04:20<3:31:38,  3.31s/it]
  2%|▏         | 76/3910 [04:23<3:32:39,  3.33s/it]
  2%|▏         | 77/3910 [04:27<3:29:42,  3.28s/it]
  2%|▏         | 78/3910 [04:30<3:31:50,  3.32s/it]
  2%|▏         | 79/3910 [04:33<3:33:07,  3.34s/it]
  2%|▏         | 80/3910 [04:37<3:30:30,  3.30s/it]
  2%|▏         | 81/3910 [04:40<3:32:33,  3.33s/it]
  2%|▏         | 82/3910 [04:43<3:33:16,  3.34s/it]
  2%|▏         | 83/3910 [04:46<3:29:51,  3.29s/it]
  2%|▏         | 84/3910 [04:50<3:32:16,  3.33s/it]
  2%|▏         | 85/3910 [04:53<3:33:28,  3.35s/it]
  2%|▏         | 86/3910 [04:56<3:30:35,  3.30s/it]
  2%|▏         | 87/3910 [05:00<3:32:31,  3.34s/it]
  2%|▏         | 88/3910 [05:03<3:33:59,  3.36s/it]
  2%|▏         | 89/3910 [05:07<3:31:01,  3.31s/it]
  2%|▏         | 90/3910 [05:10<3:32:33,  3.34s/it]
  2%|▏         | 91/3910 [05:13<3:33:34,  3.36s/it]
  2%|▏         | 92/3910 [05:16<3:30:20,  3.31s/it]
  2%|▏         | 93/3910 [05:20<3:32:12,  3.34s/it]
  2%|▏         | 94/3910 [05:23<3:33:35,  3.36s/it]
  2%|▏         | 95/3910 [05:26<3:29:53,  3.30s/it]
  2%|▏         | 96/3910 [05:30<3:31:46,  3.33s/it]
  2%|▏         | 97/3910 [05:33<3:32:38,  3.35s/it]
  3%|▎         | 98/3910 [05:36<3:29:32,  3.30s/it]
  3%|▎         | 99/3910 [05:40<3:31:33,  3.33s/it]
  3%|▎         | 100/3910 [05:43<3:32:27,  3.35s/it]
                                                    

  3%|▎         | 100/3910 [05:43<3:32:27,  3.35s/it]
  3%|▎         | 101/3910 [05:46<3:29:34,  3.30s/it]
  3%|▎         | 102/3910 [05:50<3:31:17,  3.33s/it]
  3%|▎         | 103/3910 [05:53<3:33:03,  3.36s/it]
  3%|▎         | 104/3910 [05:56<3:29:54,  3.31s/it]
  3%|▎         | 105/3910 [06:00<3:31:32,  3.34s/it]
  3%|▎         | 106/3910 [06:03<3:32:46,  3.36s/it]
  3%|▎         | 107/3910 [06:06<3:29:47,  3.31s/it]
  3%|▎         | 108/3910 [06:10<3:31:19,  3.33s/it]
  3%|▎         | 109/3910 [06:13<3:32:11,  3.35s/it]
  3%|▎         | 110/3910 [06:16<3:28:54,  3.30s/it]
  3%|▎         | 111/3910 [06:20<3:30:57,  3.33s/it]
  3%|▎         | 112/3910 [06:23<3:31:24,  3.34s/it]
  3%|▎         | 113/3910 [06:26<3:28:14,  3.29s/it]
  3%|▎         | 114/3910 [06:30<3:29:35,  3.31s/it]
  3%|▎         | 115/3910 [06:33<3:30:33,  3.33s/it]
  3%|▎         | 116/3910 [06:36<3:28:07,  3.29s/it]
  3%|▎         | 117/3910 [06:40<3:29:31,  3.31s/it]
  3%|▎         | 118/3910 [06:43<3:30:09,  3.33s/it]
  3%|▎         | 119/3910 [06:46<3:26:35,  3.27s/it]
  3%|▎         | 120/3910 [06:50<3:28:25,  3.30s/it]
  3%|▎         | 121/3910 [06:53<3:30:10,  3.33s/it]
  3%|▎         | 122/3910 [06:56<3:27:09,  3.28s/it]
  3%|▎         | 123/3910 [06:59<3:28:45,  3.31s/it]
  3%|▎         | 124/3910 [07:03<3:29:41,  3.32s/it]
  3%|▎         | 125/3910 [07:06<3:26:53,  3.28s/it]
                                                    

  3%|▎         | 125/3910 [07:06<3:26:53,  3.28s/it]
  3%|▎         | 126/3910 [07:09<3:28:39,  3.31s/it]
  3%|▎         | 127/3910 [07:13<3:29:35,  3.32s/it]
  3%|▎         | 128/3910 [07:16<3:26:54,  3.28s/it]
  3%|▎         | 129/3910 [07:19<3:29:06,  3.32s/it]
  3%|▎         | 130/3910 [07:23<3:30:21,  3.34s/it]
  3%|▎         | 131/3910 [07:26<3:27:31,  3.29s/it]
  3%|▎         | 132/3910 [07:29<3:29:21,  3.32s/it]
  3%|▎         | 133/3910 [07:33<3:30:03,  3.34s/it]
  3%|▎         | 134/3910 [07:36<3:27:34,  3.30s/it]
  3%|▎         | 135/3910 [07:39<3:29:09,  3.32s/it]
  3%|▎         | 136/3910 [07:43<3:30:03,  3.34s/it]
  4%|▎         | 137/3910 [07:46<3:27:29,  3.30s/it]
  4%|▎         | 138/3910 [07:49<3:28:57,  3.32s/it]
  4%|▎         | 139/3910 [07:53<3:29:50,  3.34s/it]
  4%|▎         | 140/3910 [07:56<3:27:12,  3.30s/it]
  4%|▎         | 141/3910 [07:59<3:28:21,  3.32s/it]
  4%|▎         | 142/3910 [08:03<3:29:36,  3.34s/it]
  4%|▎         | 143/3910 [08:06<3:26:43,  3.29s/it]
  4%|▎         | 144/3910 [08:09<3:28:54,  3.33s/it]
  4%|▎         | 145/3910 [08:12<3:26:38,  3.29s/it]
  4%|▎         | 146/3910 [08:16<3:28:42,  3.33s/it]
  4%|▍         | 147/3910 [08:19<3:29:58,  3.35s/it]slurmstepd: error: *** JOB 7067749 ON cn-g012 CANCELLED AT 2025-06-26T14:36:06 ***

  4%|▍         | 148/3910 [08:22<3:26:55,  3.30s/it]slurmstepd: error: container_p_join: open failed for /var/opt/slurm/localstorage/7067749/.ns: No such file or directory
slurmstepd: error: container_g_join(7067749): No such file or directory
