[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0624 13:26:43.079000 787027 torch/distributed/run.py:766] 
W0624 13:26:43.079000 787027 torch/distributed/run.py:766] *****************************************
W0624 13:26:43.079000 787027 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0624 13:26:43.079000 787027 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250624_132655-dpet7xvx
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-customer-support-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/dpet7xvx

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:14<00:14, 14.75s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:15<00:15, 15.16s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:15<00:15, 15.19s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:14<00:14, 14.36s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:22<00:00, 10.40s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:22<00:00, 11.11s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:22<00:00, 10.53s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:22<00:00, 11.17s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:22<00:00, 10.51s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:22<00:00, 11.21s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.66s/it]

  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 266281.83it/s]

  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 255573.61it/s]

  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 276220.12it/s]

  0%|          | 0/547 [00:00<?, ?it/s]
100%|██████████| 547/547 [00:00<00:00, 266498.35it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.85s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.17s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.78s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.77s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.83s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.27s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.96s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.38s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.88s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.22s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.49s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:17<00:00,  8.84s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank3]:[W624 13:28:57.830642470 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 3]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank1]:[W624 13:28:57.843184800 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/100 [00:00<?, ? examples/s]
Map: 100%|██████████| 100/100 [00:00<00:00, 1707.58 examples/s]

Map:   0%|          | 0/100 [00:00<?, ? examples/s]
Map: 100%|██████████| 100/100 [00:00<00:00, 4109.61 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank0]:[W624 13:28:57.104531793 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank2]:[W624 13:28:57.111820510 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 2]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
libibverbs: Warning: couldn't load driver 'libhns-rdmav34.so': libhns-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhns-rdmav34.so': libhns-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhns-rdmav34.so': libhns-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhns-rdmav34.so': libhns-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libsiw-rdmav34.so': libsiw-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libsiw-rdmav34.so': libsiw-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libsiw-rdmav34.so': libsiw-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libsiw-rdmav34.so': libsiw-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'librxe-rdmav34.so': librxe-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'librxe-rdmav34.so': librxe-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'librxe-rdmav34.so': librxe-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'librxe-rdmav34.so': librxe-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libvmw_pvrdma-rdmav34.so': libvmw_pvrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libvmw_pvrdma-rdmav34.so': libvmw_pvrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libvmw_pvrdma-rdmav34.so': libvmw_pvrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libvmw_pvrdma-rdmav34.so': libvmw_pvrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libefa-rdmav34.so': libefa-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libefa-rdmav34.so': libefa-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libefa-rdmav34.so': libefa-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libefa-rdmav34.so': libefa-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libcxgb4-rdmav34.so': libcxgb4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libcxgb4-rdmav34.so': libcxgb4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libcxgb4-rdmav34.so': libcxgb4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libcxgb4-rdmav34.so': libcxgb4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmlx4-rdmav34.so': libmlx4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmlx4-rdmav34.so': libmlx4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmlx4-rdmav34.so': libmlx4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmlx4-rdmav34.so': libmlx4-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libirdma-rdmav34.so': libirdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libirdma-rdmav34.so': libirdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libirdma-rdmav34.so': libirdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libirdma-rdmav34.so': libirdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhfi1verbs-rdmav34.so': libhfi1verbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhfi1verbs-rdmav34.so': libhfi1verbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhfi1verbs-rdmav34.so': libhfi1verbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libhfi1verbs-rdmav34.so': libhfi1verbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libqedr-rdmav34.so': libqedr-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libqedr-rdmav34.so': libqedr-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libqedr-rdmav34.so': libqedr-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libqedr-rdmav34.so': libqedr-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libbnxt_re-rdmav34.so': libbnxt_re-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libbnxt_re-rdmav34.so': libbnxt_re-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libbnxt_re-rdmav34.so': libbnxt_re-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libbnxt_re-rdmav34.so': libbnxt_re-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmthca-rdmav34.so': libmthca-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmthca-rdmav34.so': libmthca-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmthca-rdmav34.so': libmthca-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libmthca-rdmav34.so': libmthca-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libipathverbs-rdmav34.so': libipathverbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libipathverbs-rdmav34.so': libipathverbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libipathverbs-rdmav34.so': libipathverbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libipathverbs-rdmav34.so': libipathverbs-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libocrdma-rdmav34.so': libocrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libocrdma-rdmav34.so': libocrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libocrdma-rdmav34.so': libocrdma-rdmav34.so: cannot open shared object file: No such file or directory
libibverbs: Warning: couldn't load driver 'libocrdma-rdmav34.so': libocrdma-rdmav34.so: cannot open shared object file: No such file or directory
Error executing job with overrides: ['deepspeed.enabled=true']
Error executing job with overrides: ['deepspeed.enabled=true']
Error executing job with overrides: ['deepspeed.enabled=true']
Error executing job with overrides: ['deepspeed.enabled=true']
Traceback (most recent call last):
Traceback (most recent call last):
Traceback (most recent call last):
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 146, in main
    trainer = SFTTrainer(
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 146, in main
    trainer = SFTTrainer(
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 146, in main
    trainer = SFTTrainer(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py", line 101, in inner_f
    return f(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py", line 101, in inner_f
    return f(*args, **kwargs)
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 146, in main
    trainer = SFTTrainer(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/deprecation.py", line 165, in wrapped_func
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/deprecation.py", line 165, in wrapped_func
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py", line 101, in inner_f
    return f(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py", line 101, in inner_f
    return f(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 366, in __init__
    with PartialState().local_main_process_first():
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 366, in __init__
    with PartialState().local_main_process_first():
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/deprecation.py", line 165, in wrapped_func
    return func(*args, **kwargs)
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 366, in __init__
    with PartialState().local_main_process_first():
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/deprecation.py", line 165, in wrapped_func
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 545, in local_main_process_first
    yield from self._goes_first(self.is_local_main_process)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 545, in local_main_process_first
    yield from self._goes_first(self.is_local_main_process)
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 407, in _goes_first
    self.wait_for_everyone()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 407, in _goes_first
    self.wait_for_everyone()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 545, in local_main_process_first
    yield from self._goes_first(self.is_local_main_process)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 366, in __init__
    with PartialState().local_main_process_first():
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 401, in wait_for_everyone
    torch.distributed.barrier()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 401, in wait_for_everyone
    torch.distributed.barrier()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 407, in _goes_first
    self.wait_for_everyone()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 81, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 81, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 401, in wait_for_everyone
    torch.distributed.barrier()
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/contextlib.py", line 142, in __exit__
    next(self.gen)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py", line 4635, in barrier
    work = group.barrier(opts=opts)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py", line 4635, in barrier
    work = group.barrier(opts=opts)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 81, in wrapper
    return func(*args, **kwargs)
torch.distributed.DistBackendError: NCCL error in: /pytorch/torch/csrc/distributed/c10d/NCCLUtils.cpp:77, invalid usage (run with NCCL_DEBUG=WARN for details), NCCL version 2.26.2
ncclInvalidUsage: This usually reflects invalid usage of NCCL library.
Last error:
Duplicate GPU detected : rank 3 and rank 1 both on CUDA device 81000
torch.distributed.DistBackendError: NCCL error in: /pytorch/torch/csrc/distributed/c10d/NCCLUtils.cpp:77, invalid usage (run with NCCL_DEBUG=WARN for details), NCCL version 2.26.2
ncclInvalidUsage: This usually reflects invalid usage of NCCL library.
Last error:
Duplicate GPU detected : rank 2 and rank 0 both on CUDA device 1000
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py", line 4635, in barrier
    work = group.barrier(opts=opts)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 545, in local_main_process_first
    yield from self._goes_first(self.is_local_main_process)

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
torch.distributed.DistBackendError: NCCL error in: /pytorch/torch/csrc/distributed/c10d/NCCLUtils.cpp:77, invalid usage (run with NCCL_DEBUG=WARN for details), NCCL version 2.26.2
ncclInvalidUsage: This usually reflects invalid usage of NCCL library.
Last error:
Duplicate GPU detected : rank 1 and rank 3 both on CUDA device 81000

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 412, in _goes_first
    self.wait_for_everyone()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/state.py", line 401, in wait_for_everyone
    torch.distributed.barrier()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 81, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py", line 4635, in barrier
    work = group.barrier(opts=opts)
torch.distributed.DistBackendError: NCCL error in: /pytorch/torch/csrc/distributed/c10d/NCCLUtils.cpp:77, invalid usage (run with NCCL_DEBUG=WARN for details), NCCL version 2.26.2
ncclInvalidUsage: This usually reflects invalid usage of NCCL library.
Last error:
Duplicate GPU detected : rank 0 and rank 2 both on CUDA device 1000

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
[rank2]:[W624 13:28:59.251618621 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0624 13:28:59.836000 787027 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 787147 closing signal SIGTERM
W0624 13:28:59.837000 787027 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 787148 closing signal SIGTERM
W0624 13:28:59.837000 787027 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 787149 closing signal SIGTERM
E0624 13:29:00.453000 787027 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 3 (pid: 787150) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
exmp_1.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-06-24_13:28:59
  host      : cn-k003.server.mila.quebec
  rank      : 3 (local_rank: 3)
  exitcode  : 1 (pid: 787150)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
