[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0626 16:36:41.599000 2533021 torch/distributed/run.py:766] 
W0626 16:36:41.599000 2533021 torch/distributed/run.py:766] *****************************************
W0626 16:36:41.599000 2533021 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0626 16:36:41.599000 2533021 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250626_163652-z8ve741y
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-drug_abuse_pubmedqau-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/z8ve741y

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.61s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.68s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.71s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.56s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.23s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.59s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.28s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.65s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.30s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.66s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.18s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.54s/it]
Error executing job with overrides: []
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 63, in main
    expertmerger.compose()
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/mergoo/composers/composer_lora_moe.py", line 113, in compose
    assert self.config["router_layers"] == list(
AssertionError

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 202616.13it/s]

  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 204356.52it/s]

  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 203329.16it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
[rank0]:[W626 16:37:06.443258072 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0626 16:37:07.753000 2533021 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2533119 closing signal SIGTERM
W0626 16:37:07.754000 2533021 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2533120 closing signal SIGTERM
W0626 16:37:07.754000 2533021 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2533121 closing signal SIGTERM
E0626 16:37:08.534000 2533021 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 0 (pid: 2533118) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
exmp_1.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-06-26_16:37:07
  host      : cn-g012.server.mila.quebec
  rank      : 0 (local_rank: 0)
  exitcode  : 1 (pid: 2533118)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
