Configuration:
experiment_name: mistral-drug_abuse_pubmedqau-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - drug_abuse_pubmedqau
  notes: Training Mistral MoE model for drug_abuse_pubmedqau with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 5
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_drug_abuse_pubmedqau_2
  output_dir: data/output/mistral_lora_moe_drug_abuse_pubmedqau_2

Creating model checkpoint...
MoE Layer Index : [*]
[2025-06-26 01:24:23,788][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_drug_abuse_pubmedqau_2/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_drug_abuse_pubmedqau_2
Loading model...
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 6254, Val: 208, Test: 487
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
{'loss': 1.9327, 'grad_norm': 68.5, 'learning_rate': 9.994884910485935e-06, 'epoch': 0.0}
{'loss': 1.0673, 'grad_norm': 19.0, 'learning_rate': 9.872122762148338e-06, 'epoch': 0.06}
{'loss': 0.9233, 'grad_norm': 24.25, 'learning_rate': 9.744245524296676e-06, 'epoch': 0.13}
{'loss': 0.8691, 'grad_norm': 15.125, 'learning_rate': 9.616368286445014e-06, 'epoch': 0.19}
{'loss': 0.8568, 'grad_norm': 17.125, 'learning_rate': 9.488491048593351e-06, 'epoch': 0.26}
{'loss': 0.8357, 'grad_norm': 16.0, 'learning_rate': 9.360613810741689e-06, 'epoch': 0.32}
{'loss': 0.8372, 'grad_norm': 19.25, 'learning_rate': 9.232736572890026e-06, 'epoch': 0.38}
{'loss': 0.8286, 'grad_norm': 22.125, 'learning_rate': 9.104859335038364e-06, 'epoch': 0.45}
{'loss': 0.8111, 'grad_norm': 14.8125, 'learning_rate': 8.976982097186702e-06, 'epoch': 0.51}
{'loss': 0.8014, 'grad_norm': 15.8125, 'learning_rate': 8.84910485933504e-06, 'epoch': 0.58}
{'loss': 0.796, 'grad_norm': 16.875, 'learning_rate': 8.721227621483377e-06, 'epoch': 0.64}
{'loss': 0.7942, 'grad_norm': 19.875, 'learning_rate': 8.593350383631714e-06, 'epoch': 0.7}
{'loss': 0.7986, 'grad_norm': 16.0, 'learning_rate': 8.465473145780052e-06, 'epoch': 0.77}
{'loss': 0.7812, 'grad_norm': 14.8125, 'learning_rate': 8.33759590792839e-06, 'epoch': 0.83}
{'loss': 0.7632, 'grad_norm': 16.5, 'learning_rate': 8.209718670076727e-06, 'epoch': 0.9}
{'loss': 0.7688, 'grad_norm': 14.25, 'learning_rate': 8.081841432225065e-06, 'epoch': 0.96}
{'loss': 0.7306, 'grad_norm': 14.4375, 'learning_rate': 7.953964194373403e-06, 'epoch': 1.02}
{'loss': 0.5945, 'grad_norm': 13.9375, 'learning_rate': 7.82608695652174e-06, 'epoch': 1.09}
{'loss': 0.612, 'grad_norm': 15.875, 'learning_rate': 7.698209718670078e-06, 'epoch': 1.15}
{'loss': 0.6053, 'grad_norm': 16.875, 'learning_rate': 7.570332480818415e-06, 'epoch': 1.21}
{'loss': 0.6113, 'grad_norm': 15.1875, 'learning_rate': 7.442455242966753e-06, 'epoch': 1.28}
{'loss': 0.6026, 'grad_norm': 14.25, 'learning_rate': 7.3145780051150906e-06, 'epoch': 1.34}
{'loss': 0.5887, 'grad_norm': 15.5625, 'learning_rate': 7.186700767263428e-06, 'epoch': 1.41}
{'loss': 0.6091, 'grad_norm': 16.625, 'learning_rate': 7.058823529411766e-06, 'epoch': 1.47}
{'loss': 0.6079, 'grad_norm': 15.4375, 'learning_rate': 6.930946291560103e-06, 'epoch': 1.53}
{'loss': 0.5985, 'grad_norm': 15.5625, 'learning_rate': 6.803069053708441e-06, 'epoch': 1.6}
{'loss': 0.5817, 'grad_norm': 14.4375, 'learning_rate': 6.675191815856779e-06, 'epoch': 1.66}
{'loss': 0.6104, 'grad_norm': 30.75, 'learning_rate': 6.547314578005116e-06, 'epoch': 1.73}
{'loss': 0.6011, 'grad_norm': 16.625, 'learning_rate': 6.419437340153453e-06, 'epoch': 1.79}
{'loss': 0.6135, 'grad_norm': 16.75, 'learning_rate': 6.291560102301791e-06, 'epoch': 1.85}
{'loss': 0.6111, 'grad_norm': 14.6875, 'learning_rate': 6.163682864450128e-06, 'epoch': 1.92}
{'loss': 0.6027, 'grad_norm': 15.375, 'learning_rate': 6.035805626598466e-06, 'epoch': 1.98}
{'loss': 0.525, 'grad_norm': 17.75, 'learning_rate': 5.907928388746803e-06, 'epoch': 2.05}
{'loss': 0.4777, 'grad_norm': 15.4375, 'learning_rate': 5.780051150895141e-06, 'epoch': 2.11}
{'loss': 0.4794, 'grad_norm': 15.625, 'learning_rate': 5.652173913043479e-06, 'epoch': 2.17}
{'loss': 0.4753, 'grad_norm': 18.625, 'learning_rate': 5.524296675191816e-06, 'epoch': 2.24}
{'loss': 0.4794, 'grad_norm': 14.625, 'learning_rate': 5.396419437340154e-06, 'epoch': 2.3}
{'loss': 0.4774, 'grad_norm': 18.25, 'learning_rate': 5.2685421994884914e-06, 'epoch': 2.37}
{'loss': 0.4797, 'grad_norm': 17.25, 'learning_rate': 5.140664961636829e-06, 'epoch': 2.43}
{'loss': 0.4894, 'grad_norm': 16.5, 'learning_rate': 5.012787723785167e-06, 'epoch': 2.49}
{'loss': 0.4756, 'grad_norm': 19.5, 'learning_rate': 4.884910485933504e-06, 'epoch': 2.56}
{'loss': 0.4944, 'grad_norm': 16.625, 'learning_rate': 4.757033248081842e-06, 'epoch': 2.62}
{'loss': 0.4856, 'grad_norm': 15.9375, 'learning_rate': 4.6291560102301795e-06, 'epoch': 2.69}
{'loss': 0.4922, 'grad_norm': 18.25, 'learning_rate': 4.501278772378517e-06, 'epoch': 2.75}
{'loss': 0.4767, 'grad_norm': 16.625, 'learning_rate': 4.373401534526855e-06, 'epoch': 2.81}
{'loss': 0.467, 'grad_norm': 17.375, 'learning_rate': 4.245524296675192e-06, 'epoch': 2.88}
{'loss': 0.4649, 'grad_norm': 17.875, 'learning_rate': 4.11764705882353e-06, 'epoch': 2.94}
{'loss': 0.4776, 'grad_norm': 14.4375, 'learning_rate': 3.9897698209718675e-06, 'epoch': 3.01}
{'loss': 0.4127, 'grad_norm': 19.0, 'learning_rate': 3.861892583120205e-06, 'epoch': 3.07}
{'loss': 0.3913, 'grad_norm': 17.125, 'learning_rate': 3.7340153452685423e-06, 'epoch': 3.13}
{'loss': 0.4083, 'grad_norm': 17.25, 'learning_rate': 3.60613810741688e-06, 'epoch': 3.2}
{'loss': 0.4097, 'grad_norm': 17.25, 'learning_rate': 3.4782608695652175e-06, 'epoch': 3.26}
{'loss': 0.4128, 'grad_norm': 19.875, 'learning_rate': 3.350383631713555e-06, 'epoch': 3.32}
{'loss': 0.3925, 'grad_norm': 17.25, 'learning_rate': 3.2225063938618927e-06, 'epoch': 3.39}
{'loss': 0.4049, 'grad_norm': 17.375, 'learning_rate': 3.0946291560102303e-06, 'epoch': 3.45}
{'loss': 0.4059, 'grad_norm': 18.375, 'learning_rate': 2.966751918158568e-06, 'epoch': 3.52}
{'loss': 0.4153, 'grad_norm': 19.25, 'learning_rate': 2.8388746803069055e-06, 'epoch': 3.58}
{'loss': 0.4104, 'grad_norm': 18.375, 'learning_rate': 2.710997442455243e-06, 'epoch': 3.64}
{'loss': 0.4162, 'grad_norm': 17.375, 'learning_rate': 2.5831202046035808e-06, 'epoch': 3.71}
{'loss': 0.4041, 'grad_norm': 18.125, 'learning_rate': 2.4552429667519184e-06, 'epoch': 3.77}
{'loss': 0.4023, 'grad_norm': 19.0, 'learning_rate': 2.327365728900256e-06, 'epoch': 3.84}
{'loss': 0.4056, 'grad_norm': 17.375, 'learning_rate': 2.1994884910485936e-06, 'epoch': 3.9}
{'loss': 0.3989, 'grad_norm': 20.0, 'learning_rate': 2.071611253196931e-06, 'epoch': 3.96}
{'loss': 0.3896, 'grad_norm': 18.0, 'learning_rate': 1.9437340153452684e-06, 'epoch': 4.03}
{'loss': 0.3756, 'grad_norm': 18.0, 'learning_rate': 1.8158567774936062e-06, 'epoch': 4.09}
{'loss': 0.3857, 'grad_norm': 18.375, 'learning_rate': 1.6879795396419438e-06, 'epoch': 4.16}
{'loss': 0.3604, 'grad_norm': 18.0, 'learning_rate': 1.5601023017902814e-06, 'epoch': 4.22}
{'loss': 0.3721, 'grad_norm': 19.75, 'learning_rate': 1.432225063938619e-06, 'epoch': 4.28}
{'loss': 0.3718, 'grad_norm': 19.0, 'learning_rate': 1.3043478260869566e-06, 'epoch': 4.35}
{'loss': 0.3805, 'grad_norm': 18.125, 'learning_rate': 1.1764705882352942e-06, 'epoch': 4.41}
{'loss': 0.375, 'grad_norm': 19.125, 'learning_rate': 1.0485933503836318e-06, 'epoch': 4.48}
{'loss': 0.38, 'grad_norm': 18.875, 'learning_rate': 9.207161125319694e-07, 'epoch': 4.54}
{'loss': 0.3743, 'grad_norm': 19.25, 'learning_rate': 7.92838874680307e-07, 'epoch': 4.6}
{'loss': 0.3675, 'grad_norm': 16.0, 'learning_rate': 6.649616368286447e-07, 'epoch': 4.67}
{'loss': 0.3821, 'grad_norm': 17.375, 'learning_rate': 5.370843989769822e-07, 'epoch': 4.73}
{'loss': 0.3779, 'grad_norm': 20.375, 'learning_rate': 4.0920716112531976e-07, 'epoch': 4.8}
{'loss': 0.3749, 'grad_norm': 17.875, 'learning_rate': 2.813299232736573e-07, 'epoch': 4.86}
{'loss': 0.3771, 'grad_norm': 20.625, 'learning_rate': 1.534526854219949e-07, 'epoch': 4.92}
{'loss': 0.3641, 'grad_norm': 18.5, 'learning_rate': 2.5575447570332485e-08, 'epoch': 4.99}
{'train_runtime': 6587.0803, 'train_samples_per_second': 4.747, 'train_steps_per_second': 0.297, 'train_loss': 0.5401837384914193, 'epoch': 5.0}
Training completed!
Running final evaluation...
Evaluation metrics at step 1955: {'eval_loss': 0.****************, 'eval_runtime': 25.1007, 'eval_samples_per_second': 8.287, 'eval_steps_per_second': 2.072}
Final evaluation results: {'eval_loss': 0.****************, 'eval_runtime': 25.1007, 'eval_samples_per_second': 8.287, 'eval_steps_per_second': 2.072, 'epoch': 5.0}
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Thu Jun 26 03:15:24 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 1885669
            GPU Utilization               : 54 %
            Memory Utilization            : 20 %
            Max memory usage              : 62822 MiB
            Time                          : 6667192 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 1885669
            GPU Utilization               : 52 %
            Memory Utilization            : 15 %
            Max memory usage              : 30018 MiB
            Time                          : 6667191 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 1885669
            GPU Utilization               : 52 %
            Memory Utilization            : 16 %
            Max memory usage              : 29870 MiB
            Time                          : 6667190 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 1885669
            GPU Utilization               : 52 %
            Memory Utilization            : 15 %
            Max memory usage              : 29256 MiB
            Time                          : 6667189 ms
            Is Running                    : 0

Thu Jun 26 03:15:24 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   36C    P0             114W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   31C    P0             122W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   34C    P0              97W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   33C    P0             125W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
