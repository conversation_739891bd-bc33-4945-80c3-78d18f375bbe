[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0626 14:36:15.843000 2506579 torch/distributed/run.py:766] 
W0626 14:36:15.843000 2506579 torch/distributed/run.py:766] *****************************************
W0626 14:36:15.843000 2506579 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0626 14:36:15.843000 2506579 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250626_143624-0wzzl6bc
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-drug_abuse_pubmedqau-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/0wzzl6bc

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.51s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.71s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.73s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.52s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.17s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.52s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.26s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.63s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.28s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.64s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.14s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.50s/it]

  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 203183.24it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library

  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 208910.51it/s]

100%|██████████| 675/675 [00:00<00:00, 204681.55it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library

  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 200279.80it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:09<00:09,  9.85s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.60s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.63s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.54s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.68s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.12s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.80s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.21s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.87s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.28s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.28s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_drug_abuse_pubmedqau_3 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/6254 [00:00<?, ? examples/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(

Map:  16%|█▌        | 1000/6254 [00:00<00:00, 6048.98 examples/s]Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank1]:[W626 14:38:01.107184713 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank2]:[W626 14:38:01.126594762 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank3]:[W626 14:38:01.161490651 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.

Map:  32%|███▏      | 2000/6254 [00:00<00:00, 6081.23 examples/s]
Map:  48%|████▊     | 3000/6254 [00:00<00:00, 6862.14 examples/s]
Map:  64%|██████▍   | 4000/6254 [00:00<00:00, 7378.70 examples/s]
Map:  80%|███████▉  | 5000/6254 [00:00<00:00, 7706.45 examples/s]
Map:  96%|█████████▌| 6000/6254 [00:00<00:00, 7973.15 examples/s]
Map: 100%|██████████| 6254/6254 [00:00<00:00, 7337.90 examples/s]

Map:   0%|          | 0/208 [00:00<?, ? examples/s]
Map: 100%|██████████| 208/208 [00:00<00:00, 7018.01 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank0]:[W626 14:38:02.752899219 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/6254 [00:00<?, ? examples/s]
Map:   0%|          | 0/6254 [00:00<?, ? examples/s]
Map:   0%|          | 0/6254 [00:00<?, ? examples/s]Using auto half precision backend

Map:  16%|█▌        | 1000/6254 [00:00<00:01, 3426.14 examples/s]
Map:  16%|█▌        | 1000/6254 [00:00<00:01, 2878.97 examples/s]
Map:  16%|█▌        | 1000/6254 [00:00<00:01, 2730.71 examples/s]
Map:  32%|███▏      | 2000/6254 [00:00<00:01, 3524.64 examples/s]
Map:  32%|███▏      | 2000/6254 [00:00<00:01, 3237.56 examples/s]
Map:  32%|███▏      | 2000/6254 [00:00<00:01, 3168.33 examples/s]
Map:  48%|████▊     | 3000/6254 [00:00<00:00, 3612.41 examples/s]
Map:  48%|████▊     | 3000/6254 [00:00<00:00, 3424.78 examples/s]
Map:  48%|████▊     | 3000/6254 [00:00<00:00, 3368.21 examples/s]
Map:  64%|██████▍   | 4000/6254 [00:01<00:00, 3815.81 examples/s]
Map:  64%|██████▍   | 4000/6254 [00:01<00:00, 3298.81 examples/s]
Map:  64%|██████▍   | 4000/6254 [00:01<00:00, 3278.78 examples/s]
Map:  80%|███████▉  | 5000/6254 [00:01<00:00, 4118.71 examples/s]
Map:  80%|███████▉  | 5000/6254 [00:01<00:00, 3237.65 examples/s]
Map:  80%|███████▉  | 5000/6254 [00:01<00:00, 3230.71 examples/s]
Map:  96%|█████████▌| 6000/6254 [00:01<00:00, 3877.93 examples/s]
Map: 100%|██████████| 6254/6254 [00:01<00:00, 3764.86 examples/s]

Map:   0%|          | 0/208 [00:00<?, ? examples/s]
Map: 100%|██████████| 208/208 [00:00<00:00, 3222.91 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:  96%|█████████▌| 6000/6254 [00:01<00:00, 3304.08 examples/s]
Map:  96%|█████████▌| 6000/6254 [00:01<00:00, 3292.15 examples/s]
Map: 100%|██████████| 6254/6254 [00:01<00:00, 3253.28 examples/s]

Map: 100%|██████████| 6254/6254 [00:01<00:00, 3245.48 examples/s]

Map:   0%|          | 0/208 [00:00<?, ? examples/s]
Map:   0%|          | 0/208 [00:00<?, ? examples/s]
Map: 100%|██████████| 208/208 [00:00<00:00, 3408.70 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map: 100%|██████████| 208/208 [00:00<00:00, 3240.02 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
***** Running training *****
  Num examples = 6,254
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 3,910
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/3910 [00:00<?, ?it/s]
  0%|          | 1/3910 [00:02<2:17:16,  2.11s/it]
                                                  

  0%|          | 1/3910 [00:02<2:17:16,  2.11s/it]
  0%|          | 2/3910 [00:03<1:52:32,  1.73s/it]
  0%|          | 3/3910 [00:05<1:45:17,  1.62s/it]
  0%|          | 4/3910 [00:06<1:40:03,  1.54s/it]
  0%|          | 5/3910 [00:07<1:37:43,  1.50s/it]
  0%|          | 6/3910 [00:09<1:36:17,  1.48s/it]
  0%|          | 7/3910 [00:10<1:36:38,  1.49s/it]
  0%|          | 8/3910 [00:12<1:36:53,  1.49s/it]
  0%|          | 9/3910 [00:13<1:36:14,  1.48s/it]
  0%|          | 10/3910 [00:15<1:35:08,  1.46s/it]
  0%|          | 11/3910 [00:16<1:35:54,  1.48s/it]
  0%|          | 12/3910 [00:18<1:34:12,  1.45s/it]
  0%|          | 13/3910 [00:19<1:34:53,  1.46s/it]
  0%|          | 14/3910 [00:21<1:35:35,  1.47s/it]
  0%|          | 15/3910 [00:22<1:34:07,  1.45s/it]
  0%|          | 16/3910 [00:23<1:33:58,  1.45s/it]
  0%|          | 17/3910 [00:25<1:33:06,  1.44s/it]
  0%|          | 18/3910 [00:26<1:32:57,  1.43s/it]
  0%|          | 19/3910 [00:28<1:33:08,  1.44s/it]
  1%|          | 20/3910 [00:29<1:33:27,  1.44s/it]
  1%|          | 21/3910 [00:31<1:34:09,  1.45s/it]
  1%|          | 22/3910 [00:32<1:33:46,  1.45s/it]
  1%|          | 23/3910 [00:33<1:32:21,  1.43s/it]
  1%|          | 24/3910 [00:35<1:33:25,  1.44s/it]
  1%|          | 25/3910 [00:36<1:34:20,  1.46s/it]
                                                   

  1%|          | 25/3910 [00:36<1:34:20,  1.46s/it]
  1%|          | 26/3910 [00:38<1:32:52,  1.43s/it]
  1%|          | 27/3910 [00:39<1:34:21,  1.46s/it]
  1%|          | 28/3910 [00:41<1:34:06,  1.45s/it]
  1%|          | 29/3910 [00:42<1:33:21,  1.44s/it]
  1%|          | 30/3910 [00:44<1:34:19,  1.46s/it]
  1%|          | 31/3910 [00:45<1:34:17,  1.46s/it]
  1%|          | 32/3910 [00:47<1:35:18,  1.47s/it]
  1%|          | 33/3910 [00:48<1:35:27,  1.48s/it]
  1%|          | 34/3910 [00:50<1:35:21,  1.48s/it]
  1%|          | 35/3910 [00:51<1:35:03,  1.47s/it]
  1%|          | 36/3910 [00:53<1:34:47,  1.47s/it]
  1%|          | 37/3910 [00:54<1:34:39,  1.47s/it]
  1%|          | 38/3910 [00:55<1:34:39,  1.47s/it]
  1%|          | 39/3910 [00:57<1:33:26,  1.45s/it]
  1%|          | 40/3910 [00:58<1:34:14,  1.46s/it]
  1%|          | 41/3910 [01:00<1:34:30,  1.47s/it]
  1%|          | 42/3910 [01:01<1:33:59,  1.46s/it]
  1%|          | 43/3910 [01:03<1:34:21,  1.46s/it]
  1%|          | 44/3910 [01:04<1:33:59,  1.46s/it]
  1%|          | 45/3910 [01:06<1:34:41,  1.47s/it]
  1%|          | 46/3910 [01:07<1:33:26,  1.45s/it]
  1%|          | 47/3910 [01:09<1:33:54,  1.46s/it]
  1%|          | 48/3910 [01:10<1:34:50,  1.47s/it]
  1%|▏         | 49/3910 [01:12<1:35:29,  1.48s/it]
  1%|▏         | 50/3910 [01:13<1:35:07,  1.48s/it]
                                                   

  1%|▏         | 50/3910 [01:13<1:35:07,  1.48s/it]
  1%|▏         | 51/3910 [01:15<1:35:27,  1.48s/it]
  1%|▏         | 52/3910 [01:16<1:35:46,  1.49s/it]
  1%|▏         | 53/3910 [01:18<1:34:57,  1.48s/it]
  1%|▏         | 54/3910 [01:19<1:33:49,  1.46s/it]
  1%|▏         | 55/3910 [01:20<1:33:54,  1.46s/it]
  1%|▏         | 56/3910 [01:22<1:34:37,  1.47s/it]
  1%|▏         | 57/3910 [01:23<1:34:35,  1.47s/it]
  1%|▏         | 58/3910 [01:25<1:33:47,  1.46s/it]
  2%|▏         | 59/3910 [01:26<1:33:35,  1.46s/it]
  2%|▏         | 60/3910 [01:28<1:34:27,  1.47s/it]
  2%|▏         | 61/3910 [01:29<1:35:03,  1.48s/it]
  2%|▏         | 62/3910 [01:31<1:34:59,  1.48s/it]
  2%|▏         | 63/3910 [01:32<1:32:31,  1.44s/it]
  2%|▏         | 64/3910 [01:34<1:32:18,  1.44s/it]
  2%|▏         | 65/3910 [01:35<1:32:19,  1.44s/it]
  2%|▏         | 66/3910 [01:36<1:32:11,  1.44s/it]
  2%|▏         | 67/3910 [01:38<1:33:19,  1.46s/it]
  2%|▏         | 68/3910 [01:39<1:33:25,  1.46s/it]
  2%|▏         | 69/3910 [01:41<1:33:48,  1.47s/it]
  2%|▏         | 70/3910 [01:42<1:33:09,  1.46s/it]
  2%|▏         | 71/3910 [01:44<1:32:42,  1.45s/it]
  2%|▏         | 72/3910 [01:45<1:33:09,  1.46s/it]
  2%|▏         | 73/3910 [01:47<1:33:13,  1.46s/it]
  2%|▏         | 74/3910 [01:48<1:37:38,  1.53s/it]
  2%|▏         | 75/3910 [01:50<1:36:35,  1.51s/it]
                                                   

  2%|▏         | 75/3910 [01:50<1:36:35,  1.51s/it]
  2%|▏         | 76/3910 [01:51<1:36:09,  1.50s/it]
  2%|▏         | 77/3910 [01:53<1:34:36,  1.48s/it]
  2%|▏         | 78/3910 [01:54<1:33:59,  1.47s/it]
  2%|▏         | 79/3910 [01:56<1:32:54,  1.46s/it]
  2%|▏         | 80/3910 [01:57<1:33:12,  1.46s/it]
  2%|▏         | 81/3910 [01:59<1:33:36,  1.47s/it]
  2%|▏         | 82/3910 [02:00<1:32:40,  1.45s/it]
  2%|▏         | 83/3910 [02:01<1:32:23,  1.45s/it]
  2%|▏         | 84/3910 [02:03<1:31:43,  1.44s/it]
  2%|▏         | 85/3910 [02:04<1:31:44,  1.44s/it]
  2%|▏         | 86/3910 [02:06<1:32:11,  1.45s/it]
  2%|▏         | 87/3910 [02:07<1:32:00,  1.44s/it]
  2%|▏         | 88/3910 [02:09<1:32:30,  1.45s/it]
  2%|▏         | 89/3910 [02:10<1:31:39,  1.44s/it]
  2%|▏         | 90/3910 [02:12<1:32:36,  1.45s/it]
  2%|▏         | 91/3910 [02:13<1:32:22,  1.45s/it]
  2%|▏         | 92/3910 [02:14<1:31:28,  1.44s/it]
  2%|▏         | 93/3910 [02:16<1:32:31,  1.45s/it]
  2%|▏         | 94/3910 [02:17<1:32:50,  1.46s/it]
  2%|▏         | 95/3910 [02:19<1:32:42,  1.46s/it]
  2%|▏         | 96/3910 [02:20<1:31:47,  1.44s/it]
  2%|▏         | 97/3910 [02:22<1:32:22,  1.45s/it]
  3%|▎         | 98/3910 [02:23<1:30:49,  1.43s/it]
  3%|▎         | 99/3910 [02:25<1:31:32,  1.44s/it]
  3%|▎         | 100/3910 [02:26<1:32:10,  1.45s/it]
                                                    

  3%|▎         | 100/3910 [02:26<1:32:10,  1.45s/it]
  3%|▎         | 101/3910 [02:28<1:32:56,  1.46s/it]
  3%|▎         | 102/3910 [02:29<1:32:07,  1.45s/it]
  3%|▎         | 103/3910 [02:30<1:32:11,  1.45s/it]
  3%|▎         | 104/3910 [02:32<1:32:20,  1.46s/it]
  3%|▎         | 105/3910 [02:33<1:30:42,  1.43s/it]
  3%|▎         | 106/3910 [02:35<1:30:58,  1.43s/it]
  3%|▎         | 107/3910 [02:36<1:30:14,  1.42s/it]
  3%|▎         | 108/3910 [02:37<1:30:08,  1.42s/it]
  3%|▎         | 109/3910 [02:39<1:30:35,  1.43s/it]
  3%|▎         | 110/3910 [02:40<1:30:45,  1.43s/it]
  3%|▎         | 111/3910 [02:42<1:30:59,  1.44s/it]
  3%|▎         | 112/3910 [02:43<1:30:19,  1.43s/it]
  3%|▎         | 113/3910 [02:45<1:29:14,  1.41s/it]
  3%|▎         | 114/3910 [02:46<1:30:44,  1.43s/it]
  3%|▎         | 115/3910 [02:48<1:30:46,  1.44s/it]
  3%|▎         | 116/3910 [02:49<1:31:23,  1.45s/it]
  3%|▎         | 117/3910 [02:50<1:30:59,  1.44s/it]
  3%|▎         | 118/3910 [02:52<1:30:48,  1.44s/it]
  3%|▎         | 119/3910 [02:53<1:31:20,  1.45s/it]
  3%|▎         | 120/3910 [02:55<1:31:03,  1.44s/it]
  3%|▎         | 121/3910 [02:56<1:30:13,  1.43s/it]
  3%|▎         | 122/3910 [02:58<1:30:23,  1.43s/it]
  3%|▎         | 123/3910 [02:59<1:30:45,  1.44s/it]
  3%|▎         | 124/3910 [03:00<1:30:22,  1.43s/it]
  3%|▎         | 125/3910 [03:02<1:30:54,  1.44s/it]
                                                    

  3%|▎         | 125/3910 [03:02<1:30:54,  1.44s/it]
  3%|▎         | 126/3910 [03:03<1:31:44,  1.45s/it]
  3%|▎         | 127/3910 [03:05<1:32:12,  1.46s/it]
  3%|▎         | 128/3910 [03:06<1:32:25,  1.47s/it]
  3%|▎         | 129/3910 [03:08<1:32:02,  1.46s/it]
  3%|▎         | 130/3910 [03:09<1:32:08,  1.46s/it]
  3%|▎         | 131/3910 [03:11<1:31:30,  1.45s/it]
  3%|▎         | 132/3910 [03:12<1:29:18,  1.42s/it]
  3%|▎         | 133/3910 [03:13<1:29:03,  1.41s/it]
  3%|▎         | 134/3910 [03:15<1:30:13,  1.43s/it]
  3%|▎         | 135/3910 [03:16<1:30:42,  1.44s/it]
  3%|▎         | 136/3910 [03:18<1:31:40,  1.46s/it]
  4%|▎         | 137/3910 [03:19<1:32:07,  1.47s/it]
  4%|▎         | 138/3910 [03:21<1:32:30,  1.47s/it]
  4%|▎         | 139/3910 [03:22<1:32:37,  1.47s/it]
  4%|▎         | 140/3910 [03:24<1:32:21,  1.47s/it]
  4%|▎         | 141/3910 [03:25<1:31:25,  1.46s/it]
  4%|▎         | 142/3910 [03:27<1:31:19,  1.45s/it]
  4%|▎         | 143/3910 [03:28<1:30:57,  1.45s/it]
  4%|▎         | 144/3910 [03:30<1:30:42,  1.45s/it]
  4%|▎         | 145/3910 [03:31<1:31:29,  1.46s/it]
  4%|▎         | 146/3910 [03:33<1:31:57,  1.47s/it]
  4%|▍         | 147/3910 [03:34<1:30:48,  1.45s/it]
  4%|▍         | 148/3910 [03:35<1:29:34,  1.43s/it]
  4%|▍         | 149/3910 [03:37<1:30:16,  1.44s/it]
  4%|▍         | 150/3910 [03:38<1:29:35,  1.43s/it]
                                                    

  4%|▍         | 150/3910 [03:38<1:29:35,  1.43s/it]
  4%|▍         | 151/3910 [03:40<1:29:37,  1.43s/it]
  4%|▍         | 152/3910 [03:41<1:28:51,  1.42s/it]
  4%|▍         | 153/3910 [03:42<1:28:28,  1.41s/it]
  4%|▍         | 154/3910 [03:44<1:27:24,  1.40s/it]
  4%|▍         | 155/3910 [03:45<1:28:23,  1.41s/it]
  4%|▍         | 156/3910 [03:47<1:28:50,  1.42s/it]
  4%|▍         | 157/3910 [03:48<1:28:44,  1.42s/it]
  4%|▍         | 158/3910 [03:49<1:27:45,  1.40s/it]
  4%|▍         | 159/3910 [03:51<1:28:01,  1.41s/it]
  4%|▍         | 160/3910 [03:52<1:28:26,  1.42s/it]
  4%|▍         | 161/3910 [03:54<1:28:29,  1.42s/it]
  4%|▍         | 162/3910 [03:55<1:29:50,  1.44s/it]
  4%|▍         | 163/3910 [03:57<1:29:55,  1.44s/it]
  4%|▍         | 164/3910 [03:58<1:28:46,  1.42s/it]
  4%|▍         | 165/3910 [03:59<1:29:33,  1.43s/it]
  4%|▍         | 166/3910 [04:01<1:29:13,  1.43s/it]
  4%|▍         | 167/3910 [04:02<1:30:15,  1.45s/it]
  4%|▍         | 168/3910 [04:04<1:29:15,  1.43s/it]
  4%|▍         | 169/3910 [04:05<1:28:11,  1.41s/it]
  4%|▍         | 170/3910 [04:07<1:28:44,  1.42s/it]
  4%|▍         | 171/3910 [04:08<1:29:30,  1.44s/it]
  4%|▍         | 172/3910 [04:09<1:29:11,  1.43s/it]
  4%|▍         | 173/3910 [04:11<1:29:51,  1.44s/it]
  4%|▍         | 174/3910 [04:12<1:29:56,  1.44s/it]
  4%|▍         | 175/3910 [04:14<1:30:24,  1.45s/it]
                                                    

  4%|▍         | 175/3910 [04:14<1:30:24,  1.45s/it]
  5%|▍         | 176/3910 [04:15<1:31:07,  1.46s/it]
  5%|▍         | 177/3910 [04:17<1:30:39,  1.46s/it]
  5%|▍         | 178/3910 [04:18<1:29:33,  1.44s/it]
  5%|▍         | 179/3910 [04:20<1:30:16,  1.45s/it]
  5%|▍         | 180/3910 [04:21<1:30:57,  1.46s/it]
  5%|▍         | 181/3910 [04:23<1:30:34,  1.46s/it]
  5%|▍         | 182/3910 [04:24<1:30:43,  1.46s/it]
  5%|▍         | 183/3910 [04:25<1:27:51,  1.41s/it]
  5%|▍         | 184/3910 [04:27<1:28:35,  1.43s/it]
  5%|▍         | 185/3910 [04:28<1:28:00,  1.42s/it]
  5%|▍         | 186/3910 [04:30<1:27:48,  1.41s/it]
  5%|▍         | 187/3910 [04:31<1:33:03,  1.50s/it]
  5%|▍         | 188/3910 [04:33<1:32:51,  1.50s/it]
  5%|▍         | 189/3910 [04:34<1:32:34,  1.49s/it]
  5%|▍         | 190/3910 [04:36<1:32:10,  1.49s/it]
  5%|▍         | 191/3910 [04:37<1:31:15,  1.47s/it]
  5%|▍         | 192/3910 [04:39<1:30:45,  1.46s/it]
  5%|▍         | 193/3910 [04:40<1:30:15,  1.46s/it]
  5%|▍         | 194/3910 [04:42<1:28:57,  1.44s/it]
  5%|▍         | 195/3910 [04:43<1:28:06,  1.42s/it]
  5%|▌         | 196/3910 [04:44<1:29:10,  1.44s/it]
  5%|▌         | 197/3910 [04:46<1:28:56,  1.44s/it]
  5%|▌         | 198/3910 [04:47<1:29:33,  1.45s/it]
  5%|▌         | 199/3910 [04:49<1:29:38,  1.45s/it]
  5%|▌         | 200/3910 [04:50<1:30:19,  1.46s/it]
                                                    

  5%|▌         | 200/3910 [04:50<1:30:19,  1.46s/it]
  5%|▌         | 201/3910 [04:52<1:30:23,  1.46s/it]
  5%|▌         | 202/3910 [04:53<1:29:42,  1.45s/it]
  5%|▌         | 203/3910 [04:55<1:29:29,  1.45s/it]
  5%|▌         | 204/3910 [04:56<1:29:30,  1.45s/it]
  5%|▌         | 205/3910 [04:57<1:29:11,  1.44s/it]
  5%|▌         | 206/3910 [04:59<1:29:08,  1.44s/it]
  5%|▌         | 207/3910 [05:00<1:30:02,  1.46s/it]
  5%|▌         | 208/3910 [05:02<1:29:37,  1.45s/it]
  5%|▌         | 209/3910 [05:03<1:28:35,  1.44s/it]
  5%|▌         | 210/3910 [05:05<1:29:34,  1.45s/it]
  5%|▌         | 211/3910 [05:06<1:29:13,  1.45s/it]
  5%|▌         | 212/3910 [05:08<1:29:12,  1.45s/it]
  5%|▌         | 213/3910 [05:09<1:28:35,  1.44s/it]
  5%|▌         | 214/3910 [05:10<1:28:30,  1.44s/it]
  5%|▌         | 215/3910 [05:12<1:29:14,  1.45s/it]
  6%|▌         | 216/3910 [05:13<1:29:38,  1.46s/it]
  6%|▌         | 217/3910 [05:15<1:29:50,  1.46s/it]
  6%|▌         | 218/3910 [05:16<1:29:32,  1.46s/it]
  6%|▌         | 219/3910 [05:18<1:29:10,  1.45s/it]
  6%|▌         | 220/3910 [05:19<1:29:35,  1.46s/it]
  6%|▌         | 221/3910 [05:21<1:28:17,  1.44s/it]
  6%|▌         | 222/3910 [05:22<1:28:38,  1.44s/it]
  6%|▌         | 223/3910 [05:24<1:29:14,  1.45s/it]
  6%|▌         | 224/3910 [05:25<1:30:04,  1.47s/it]
  6%|▌         | 225/3910 [05:26<1:30:01,  1.47s/it]
                                                    

  6%|▌         | 225/3910 [05:26<1:30:01,  1.47s/it]
  6%|▌         | 226/3910 [05:28<1:28:25,  1.44s/it]
  6%|▌         | 227/3910 [05:29<1:29:24,  1.46s/it]
  6%|▌         | 228/3910 [05:31<1:28:23,  1.44s/it]
  6%|▌         | 229/3910 [05:32<1:28:16,  1.44s/it]
  6%|▌         | 230/3910 [05:34<1:28:40,  1.45s/it]
  6%|▌         | 231/3910 [05:35<1:28:19,  1.44s/it]
  6%|▌         | 232/3910 [05:37<1:28:07,  1.44s/it]
  6%|▌         | 233/3910 [05:38<1:28:39,  1.45s/it]
  6%|▌         | 234/3910 [05:39<1:29:29,  1.46s/it]
  6%|▌         | 235/3910 [05:41<1:29:37,  1.46s/it]
  6%|▌         | 236/3910 [05:42<1:30:04,  1.47s/it]
  6%|▌         | 237/3910 [05:44<1:30:18,  1.48s/it]
  6%|▌         | 238/3910 [05:45<1:30:42,  1.48s/it]
  6%|▌         | 239/3910 [05:47<1:29:11,  1.46s/it]
  6%|▌         | 240/3910 [05:48<1:29:53,  1.47s/it]
  6%|▌         | 241/3910 [05:50<1:30:18,  1.48s/it]
  6%|▌         | 242/3910 [05:51<1:29:48,  1.47s/it]
  6%|▌         | 243/3910 [05:53<1:30:00,  1.47s/it]
  6%|▌         | 244/3910 [05:54<1:29:55,  1.47s/it]
  6%|▋         | 245/3910 [05:56<1:28:40,  1.45s/it]
  6%|▋         | 246/3910 [05:57<1:28:04,  1.44s/it]
  6%|▋         | 247/3910 [05:59<1:28:33,  1.45s/it]
  6%|▋         | 248/3910 [06:00<1:28:53,  1.46s/it]
  6%|▋         | 249/3910 [06:01<1:27:21,  1.43s/it]
  6%|▋         | 250/3910 [06:03<1:27:15,  1.43s/it]
                                                    

  6%|▋         | 250/3910 [06:03<1:27:15,  1.43s/it]
  6%|▋         | 251/3910 [06:04<1:28:10,  1.45s/it]
  6%|▋         | 252/3910 [06:06<1:28:04,  1.44s/it]
  6%|▋         | 253/3910 [06:07<1:27:25,  1.43s/it]
  6%|▋         | 254/3910 [06:09<1:28:17,  1.45s/it]
  7%|▋         | 255/3910 [06:10<1:28:48,  1.46s/it]
  7%|▋         | 256/3910 [06:11<1:27:58,  1.44s/it]
  7%|▋         | 257/3910 [06:13<1:27:24,  1.44s/it]
  7%|▋         | 258/3910 [06:14<1:26:43,  1.42s/it]
  7%|▋         | 259/3910 [06:16<1:27:33,  1.44s/it]
  7%|▋         | 260/3910 [06:17<1:28:20,  1.45s/it]
  7%|▋         | 261/3910 [06:19<1:28:44,  1.46s/it]
  7%|▋         | 262/3910 [06:20<1:28:13,  1.45s/it]
  7%|▋         | 263/3910 [06:22<1:27:58,  1.45s/it]
  7%|▋         | 264/3910 [06:23<1:27:38,  1.44s/it]
  7%|▋         | 265/3910 [06:25<1:28:19,  1.45s/it]
  7%|▋         | 266/3910 [06:26<1:32:19,  1.52s/it]
  7%|▋         | 267/3910 [06:28<1:30:20,  1.49s/it]
  7%|▋         | 268/3910 [06:29<1:28:30,  1.46s/it]
  7%|▋         | 269/3910 [06:30<1:27:29,  1.44s/it]
  7%|▋         | 270/3910 [06:32<1:26:40,  1.43s/it]
  7%|▋         | 271/3910 [06:33<1:27:26,  1.44s/it]
  7%|▋         | 272/3910 [06:35<1:27:02,  1.44s/it]
  7%|▋         | 273/3910 [06:36<1:26:22,  1.42s/it]
  7%|▋         | 274/3910 [06:37<1:25:35,  1.41s/it]
  7%|▋         | 275/3910 [06:39<1:25:45,  1.42s/it]
                                                    

  7%|▋         | 275/3910 [06:39<1:25:45,  1.42s/it]
  7%|▋         | 276/3910 [06:40<1:27:11,  1.44s/it]
  7%|▋         | 277/3910 [06:42<1:26:34,  1.43s/it]
  7%|▋         | 278/3910 [06:43<1:26:50,  1.43s/it]
  7%|▋         | 279/3910 [06:45<1:25:47,  1.42s/it]
  7%|▋         | 280/3910 [06:46<1:25:49,  1.42s/it]
  7%|▋         | 281/3910 [06:48<1:26:46,  1.43s/it]
  7%|▋         | 282/3910 [06:49<1:27:44,  1.45s/it]
  7%|▋         | 283/3910 [06:50<1:27:06,  1.44s/it]
  7%|▋         | 284/3910 [06:52<1:27:35,  1.45s/it]
  7%|▋         | 285/3910 [06:53<1:27:46,  1.45s/it]
  7%|▋         | 286/3910 [06:55<1:27:39,  1.45s/it]
  7%|▋         | 287/3910 [06:56<1:27:47,  1.45s/it]
  7%|▋         | 288/3910 [06:58<1:28:02,  1.46s/it]
  7%|▋         | 289/3910 [06:59<1:27:45,  1.45s/it]
  7%|▋         | 290/3910 [07:01<1:26:23,  1.43s/it]
  7%|▋         | 291/3910 [07:02<1:27:21,  1.45s/it]
  7%|▋         | 292/3910 [07:04<1:27:51,  1.46s/it]
  7%|▋         | 293/3910 [07:05<1:31:04,  1.51s/it]
  8%|▊         | 294/3910 [07:07<1:30:37,  1.50s/it]
  8%|▊         | 295/3910 [07:08<1:30:20,  1.50s/it]
  8%|▊         | 296/3910 [07:10<1:28:23,  1.47s/it]
  8%|▊         | 297/3910 [07:11<1:27:53,  1.46s/it]
  8%|▊         | 298/3910 [07:12<1:26:56,  1.44s/it]
  8%|▊         | 299/3910 [07:14<1:27:27,  1.45s/it]
  8%|▊         | 300/3910 [07:15<1:27:28,  1.45s/it]
                                                    

  8%|▊         | 300/3910 [07:15<1:27:28,  1.45s/it]
  8%|▊         | 301/3910 [07:17<1:26:15,  1.43s/it]
  8%|▊         | 302/3910 [07:18<1:26:06,  1.43s/it]
  8%|▊         | 303/3910 [07:20<1:27:06,  1.45s/it]
  8%|▊         | 304/3910 [07:21<1:26:22,  1.44s/it]
  8%|▊         | 305/3910 [07:22<1:26:06,  1.43s/it]
  8%|▊         | 306/3910 [07:24<1:26:41,  1.44s/it]
  8%|▊         | 307/3910 [07:25<1:26:11,  1.44s/it]
  8%|▊         | 308/3910 [07:27<1:26:18,  1.44s/it]
  8%|▊         | 309/3910 [07:28<1:26:10,  1.44s/it]
  8%|▊         | 310/3910 [07:30<1:25:48,  1.43s/it]
  8%|▊         | 311/3910 [07:31<1:25:59,  1.43s/it]
  8%|▊         | 312/3910 [07:33<1:26:55,  1.45s/it]
  8%|▊         | 313/3910 [07:34<1:26:39,  1.45s/it]
  8%|▊         | 314/3910 [07:35<1:25:42,  1.43s/it]
  8%|▊         | 315/3910 [07:37<1:26:29,  1.44s/it]
  8%|▊         | 316/3910 [07:38<1:27:10,  1.46s/it]
  8%|▊         | 317/3910 [07:40<1:25:59,  1.44s/it]
  8%|▊         | 318/3910 [07:41<1:27:01,  1.45s/it]
  8%|▊         | 319/3910 [07:43<1:26:10,  1.44s/it]
  8%|▊         | 320/3910 [07:44<1:26:25,  1.44s/it]
  8%|▊         | 321/3910 [07:46<1:25:59,  1.44s/it]
  8%|▊         | 322/3910 [07:47<1:26:07,  1.44s/it]
  8%|▊         | 323/3910 [07:48<1:26:24,  1.45s/it]
  8%|▊         | 324/3910 [07:50<1:27:12,  1.46s/it]
  8%|▊         | 325/3910 [07:51<1:26:18,  1.44s/it]
                                                    

  8%|▊         | 325/3910 [07:51<1:26:18,  1.44s/it]
  8%|▊         | 326/3910 [07:53<1:27:07,  1.46s/it]
  8%|▊         | 327/3910 [07:54<1:27:05,  1.46s/it]
  8%|▊         | 328/3910 [07:56<1:26:20,  1.45s/it]
  8%|▊         | 329/3910 [07:57<1:24:23,  1.41s/it]
  8%|▊         | 330/3910 [07:58<1:25:11,  1.43s/it]
  8%|▊         | 331/3910 [08:00<1:25:37,  1.44s/it]
  8%|▊         | 332/3910 [08:01<1:26:31,  1.45s/it]
  9%|▊         | 333/3910 [08:03<1:26:36,  1.45s/it]
  9%|▊         | 334/3910 [08:04<1:26:27,  1.45s/it]
  9%|▊         | 335/3910 [08:06<1:26:15,  1.45s/it]
  9%|▊         | 336/3910 [08:07<1:26:58,  1.46s/it]
  9%|▊         | 337/3910 [08:09<1:26:37,  1.45s/it]
  9%|▊         | 338/3910 [08:10<1:26:48,  1.46s/it]
  9%|▊         | 339/3910 [08:12<1:25:44,  1.44s/it]
  9%|▊         | 340/3910 [08:13<1:25:29,  1.44s/it]
  9%|▊         | 341/3910 [08:14<1:25:41,  1.44s/it]
  9%|▊         | 342/3910 [08:16<1:25:14,  1.43s/it]
  9%|▉         | 343/3910 [08:17<1:25:02,  1.43s/it]
  9%|▉         | 344/3910 [08:19<1:24:54,  1.43s/it]
  9%|▉         | 345/3910 [08:20<1:25:12,  1.43s/it]
  9%|▉         | 346/3910 [08:22<1:26:02,  1.45s/it]
  9%|▉         | 347/3910 [08:23<1:26:52,  1.46s/it]
  9%|▉         | 348/3910 [08:25<1:27:20,  1.47s/it]
  9%|▉         | 349/3910 [08:26<1:26:54,  1.46s/it]
  9%|▉         | 350/3910 [08:28<1:27:28,  1.47s/it]
                                                    

  9%|▉         | 350/3910 [08:28<1:27:28,  1.47s/it]
  9%|▉         | 351/3910 [08:29<1:27:51,  1.48s/it]
  9%|▉         | 352/3910 [08:31<1:27:16,  1.47s/it]
  9%|▉         | 353/3910 [08:32<1:27:37,  1.48s/it]
  9%|▉         | 354/3910 [08:33<1:27:16,  1.47s/it]
  9%|▉         | 355/3910 [08:35<1:25:26,  1.44s/it]
  9%|▉         | 356/3910 [08:36<1:24:02,  1.42s/it]
  9%|▉         | 357/3910 [08:38<1:23:07,  1.40s/it]
  9%|▉         | 358/3910 [08:39<1:23:48,  1.42s/it]
  9%|▉         | 359/3910 [08:40<1:24:41,  1.43s/it]
  9%|▉         | 360/3910 [08:42<1:24:31,  1.43s/it]
  9%|▉         | 361/3910 [08:43<1:24:43,  1.43s/it]
  9%|▉         | 362/3910 [08:45<1:25:26,  1.44s/it]
  9%|▉         | 363/3910 [08:46<1:25:11,  1.44s/it]
  9%|▉         | 364/3910 [08:48<1:25:09,  1.44s/it]
  9%|▉         | 365/3910 [08:49<1:24:21,  1.43s/it]
  9%|▉         | 366/3910 [08:51<1:29:12,  1.51s/it]
  9%|▉         | 367/3910 [08:52<1:27:44,  1.49s/it]
  9%|▉         | 368/3910 [08:54<1:27:38,  1.48s/it]
  9%|▉         | 369/3910 [08:55<1:26:20,  1.46s/it]
  9%|▉         | 370/3910 [08:57<1:26:50,  1.47s/it]
  9%|▉         | 371/3910 [08:58<1:25:21,  1.45s/it]
 10%|▉         | 372/3910 [08:59<1:24:57,  1.44s/it]
 10%|▉         | 373/3910 [09:01<1:27:43,  1.49s/it]
 10%|▉         | 374/3910 [09:02<1:26:51,  1.47s/it]
 10%|▉         | 375/3910 [09:04<1:25:40,  1.45s/it]
                                                    

 10%|▉         | 375/3910 [09:04<1:25:40,  1.45s/it]
 10%|▉         | 376/3910 [09:05<1:26:05,  1.46s/it]
 10%|▉         | 377/3910 [09:07<1:25:38,  1.45s/it]
 10%|▉         | 378/3910 [09:08<1:25:28,  1.45s/it]
 10%|▉         | 379/3910 [09:10<1:26:09,  1.46s/it]
 10%|▉         | 380/3910 [09:11<1:26:43,  1.47s/it]
 10%|▉         | 381/3910 [09:13<1:26:46,  1.48s/it]
 10%|▉         | 382/3910 [09:14<1:25:57,  1.46s/it]
 10%|▉         | 383/3910 [09:16<1:25:16,  1.45s/it]
 10%|▉         | 384/3910 [09:17<1:24:08,  1.43s/it]
 10%|▉         | 385/3910 [09:18<1:25:18,  1.45s/it]
 10%|▉         | 386/3910 [09:20<1:25:28,  1.46s/it]
 10%|▉         | 387/3910 [09:21<1:25:48,  1.46s/it]
 10%|▉         | 388/3910 [09:23<1:25:41,  1.46s/it]
 10%|▉         | 389/3910 [09:24<1:25:59,  1.47s/it]
 10%|▉         | 390/3910 [09:26<1:25:54,  1.46s/it]
 10%|█         | 391/3910 [09:27<1:25:52,  1.46s/it]
 10%|█         | 392/3910 [09:29<1:25:14,  1.45s/it]
 10%|█         | 393/3910 [09:30<1:25:13,  1.45s/it]
 10%|█         | 394/3910 [09:32<1:24:37,  1.44s/it]
 10%|█         | 395/3910 [09:33<1:24:18,  1.44s/it]
 10%|█         | 396/3910 [09:34<1:24:06,  1.44s/it]
 10%|█         | 397/3910 [09:36<1:23:54,  1.43s/it]
 10%|█         | 398/3910 [09:37<1:22:11,  1.40s/it]
 10%|█         | 399/3910 [09:39<1:23:13,  1.42s/it]
 10%|█         | 400/3910 [09:40<1:22:46,  1.41s/it]
                                                    

 10%|█         | 400/3910 [09:40<1:22:46,  1.41s/it]
 10%|█         | 401/3910 [09:41<1:21:43,  1.40s/it]
 10%|█         | 402/3910 [09:43<1:23:07,  1.42s/it]
 10%|█         | 403/3910 [09:44<1:24:00,  1.44s/it]
 10%|█         | 404/3910 [09:46<1:23:08,  1.42s/it]
 10%|█         | 405/3910 [09:47<1:23:56,  1.44s/it]
 10%|█         | 406/3910 [09:49<1:22:32,  1.41s/it]
 10%|█         | 407/3910 [09:50<1:23:07,  1.42s/it]
 10%|█         | 408/3910 [09:51<1:23:47,  1.44s/it]
 10%|█         | 409/3910 [09:53<1:23:26,  1.43s/it]
 10%|█         | 410/3910 [09:54<1:24:00,  1.44s/it]
 11%|█         | 411/3910 [09:56<1:23:45,  1.44s/it]
 11%|█         | 412/3910 [09:57<1:24:08,  1.44s/it]
 11%|█         | 413/3910 [09:59<1:24:28,  1.45s/it]
 11%|█         | 414/3910 [10:00<1:24:20,  1.45s/it]
 11%|█         | 415/3910 [10:02<1:24:15,  1.45s/it]
 11%|█         | 416/3910 [10:03<1:24:16,  1.45s/it]
 11%|█         | 417/3910 [10:04<1:23:59,  1.44s/it]
 11%|█         | 418/3910 [10:06<1:23:46,  1.44s/it]
 11%|█         | 419/3910 [10:07<1:23:45,  1.44s/it]
 11%|█         | 420/3910 [10:09<1:23:58,  1.44s/it]
 11%|█         | 421/3910 [10:10<1:23:12,  1.43s/it]
 11%|█         | 422/3910 [10:12<1:22:45,  1.42s/it]
 11%|█         | 423/3910 [10:13<1:23:53,  1.44s/it]
 11%|█         | 424/3910 [10:15<1:23:28,  1.44s/it]
 11%|█         | 425/3910 [10:16<1:24:27,  1.45s/it]
                                                    

 11%|█         | 425/3910 [10:16<1:24:27,  1.45s/it]
 11%|█         | 426/3910 [10:17<1:23:42,  1.44s/it]
 11%|█         | 427/3910 [10:19<1:23:27,  1.44s/it]
 11%|█         | 428/3910 [10:20<1:24:07,  1.45s/it]
 11%|█         | 429/3910 [10:22<1:22:53,  1.43s/it]
 11%|█         | 430/3910 [10:23<1:23:06,  1.43s/it]
 11%|█         | 431/3910 [10:25<1:23:02,  1.43s/it]
 11%|█         | 432/3910 [10:26<1:24:04,  1.45s/it]
 11%|█         | 433/3910 [10:27<1:22:43,  1.43s/it]
 11%|█         | 434/3910 [10:29<1:22:59,  1.43s/it]
 11%|█         | 435/3910 [10:30<1:22:41,  1.43s/it]
 11%|█         | 436/3910 [10:32<1:23:17,  1.44s/it]
 11%|█         | 437/3910 [10:33<1:24:12,  1.45s/it]
 11%|█         | 438/3910 [10:35<1:24:27,  1.46s/it]
 11%|█         | 439/3910 [10:36<1:23:25,  1.44s/it]
 11%|█▏        | 440/3910 [10:38<1:23:12,  1.44s/it]
 11%|█▏        | 441/3910 [10:39<1:23:32,  1.44s/it]
 11%|█▏        | 442/3910 [10:41<1:24:14,  1.46s/it]
 11%|█▏        | 443/3910 [10:42<1:23:41,  1.45s/it]
 11%|█▏        | 444/3910 [10:43<1:24:26,  1.46s/it]
 11%|█▏        | 445/3910 [10:45<1:24:36,  1.46s/it]
 11%|█▏        | 446/3910 [10:46<1:22:44,  1.43s/it]
 11%|█▏        | 447/3910 [10:48<1:23:42,  1.45s/it]
 11%|█▏        | 448/3910 [10:49<1:26:39,  1.50s/it]
 11%|█▏        | 449/3910 [10:51<1:26:34,  1.50s/it]
 12%|█▏        | 450/3910 [10:52<1:25:30,  1.48s/it]
                                                    

 12%|█▏        | 450/3910 [10:52<1:25:30,  1.48s/it]
 12%|█▏        | 451/3910 [10:54<1:25:01,  1.47s/it]
 12%|█▏        | 452/3910 [10:55<1:23:56,  1.46s/it]
 12%|█▏        | 453/3910 [10:57<1:23:45,  1.45s/it]
 12%|█▏        | 454/3910 [10:58<1:22:31,  1.43s/it]
 12%|█▏        | 455/3910 [10:59<1:21:44,  1.42s/it]
 12%|█▏        | 456/3910 [11:01<1:22:30,  1.43s/it]
 12%|█▏        | 457/3910 [11:02<1:21:24,  1.41s/it]
 12%|█▏        | 458/3910 [11:04<1:22:17,  1.43s/it]
 12%|█▏        | 459/3910 [11:05<1:22:25,  1.43s/it]
 12%|█▏        | 460/3910 [11:07<1:22:49,  1.44s/it]
 12%|█▏        | 461/3910 [11:08<1:21:04,  1.41s/it]
 12%|█▏        | 462/3910 [11:09<1:22:10,  1.43s/it]
 12%|█▏        | 463/3910 [11:11<1:22:44,  1.44s/it]
 12%|█▏        | 464/3910 [11:12<1:23:10,  1.45s/it]
 12%|█▏        | 465/3910 [11:14<1:22:48,  1.44s/it]
 12%|█▏        | 466/3910 [11:15<1:22:26,  1.44s/it]
 12%|█▏        | 467/3910 [11:17<1:23:18,  1.45s/it]
 12%|█▏        | 468/3910 [11:18<1:23:21,  1.45s/it]
 12%|█▏        | 469/3910 [11:20<1:22:11,  1.43s/it]
 12%|█▏        | 470/3910 [11:21<1:22:28,  1.44s/it]
 12%|█▏        | 471/3910 [11:22<1:22:04,  1.43s/it]
 12%|█▏        | 472/3910 [11:24<1:23:04,  1.45s/it]
 12%|█▏        | 473/3910 [11:25<1:23:48,  1.46s/it]
 12%|█▏        | 474/3910 [11:27<1:23:49,  1.46s/it]
 12%|█▏        | 475/3910 [11:28<1:24:00,  1.47s/it]
                                                    

 12%|█▏        | 475/3910 [11:28<1:24:00,  1.47s/it]
 12%|█▏        | 476/3910 [11:30<1:23:42,  1.46s/it]
 12%|█▏        | 477/3910 [11:31<1:24:22,  1.47s/it]
 12%|█▏        | 478/3910 [11:33<1:23:31,  1.46s/it]
 12%|█▏        | 479/3910 [11:34<1:23:42,  1.46s/it]
 12%|█▏        | 480/3910 [11:36<1:22:59,  1.45s/it]
 12%|█▏        | 481/3910 [11:37<1:21:57,  1.43s/it]
 12%|█▏        | 482/3910 [11:38<1:22:25,  1.44s/it]
 12%|█▏        | 483/3910 [11:40<1:21:58,  1.44s/it]
 12%|█▏        | 484/3910 [11:41<1:21:56,  1.44s/it]
 12%|█▏        | 485/3910 [11:43<1:22:01,  1.44s/it]
 12%|█▏        | 486/3910 [11:44<1:21:45,  1.43s/it]
 12%|█▏        | 487/3910 [11:46<1:21:13,  1.42s/it]
 12%|█▏        | 488/3910 [11:47<1:21:55,  1.44s/it]
 13%|█▎        | 489/3910 [11:48<1:22:03,  1.44s/it]
 13%|█▎        | 490/3910 [11:50<1:21:22,  1.43s/it]
 13%|█▎        | 491/3910 [11:51<1:21:54,  1.44s/it]
 13%|█▎        | 492/3910 [11:53<1:21:02,  1.42s/it]
 13%|█▎        | 493/3910 [11:54<1:21:28,  1.43s/it]
 13%|█▎        | 494/3910 [11:56<1:20:38,  1.42s/it]
 13%|█▎        | 495/3910 [11:57<1:20:58,  1.42s/it]
 13%|█▎        | 496/3910 [11:58<1:20:52,  1.42s/it]
 13%|█▎        | 497/3910 [12:00<1:21:32,  1.43s/it]
 13%|█▎        | 498/3910 [12:01<1:21:54,  1.44s/it]
 13%|█▎        | 499/3910 [12:03<1:22:33,  1.45s/it]
 13%|█▎        | 500/3910 [12:04<1:22:25,  1.45s/it]
                                                    

 13%|█▎        | 500/3910 [12:04<1:22:25,  1.45s/it]
 13%|█▎        | 501/3910 [12:06<1:21:19,  1.43s/it]
 13%|█▎        | 502/3910 [12:07<1:21:50,  1.44s/it]
 13%|█▎        | 503/3910 [12:09<1:21:37,  1.44s/it]
 13%|█▎        | 504/3910 [12:10<1:21:40,  1.44s/it]
 13%|█▎        | 505/3910 [12:11<1:21:13,  1.43s/it]
 13%|█▎        | 506/3910 [12:13<1:20:57,  1.43s/it]
 13%|█▎        | 507/3910 [12:14<1:21:00,  1.43s/it]
 13%|█▎        | 508/3910 [12:16<1:21:06,  1.43s/it]
 13%|█▎        | 509/3910 [12:17<1:21:40,  1.44s/it]
 13%|█▎        | 510/3910 [12:19<1:22:25,  1.45s/it]
 13%|█▎        | 511/3910 [12:20<1:23:01,  1.47s/it]
 13%|█▎        | 512/3910 [12:22<1:22:39,  1.46s/it]
 13%|█▎        | 513/3910 [12:23<1:22:47,  1.46s/it]
 13%|█▎        | 514/3910 [12:25<1:23:10,  1.47s/it]
 13%|█▎        | 515/3910 [12:26<1:23:28,  1.48s/it]
 13%|█▎        | 516/3910 [12:27<1:22:54,  1.47s/it]
 13%|█▎        | 517/3910 [12:29<1:23:19,  1.47s/it]
 13%|█▎        | 518/3910 [12:30<1:21:48,  1.45s/it]
 13%|█▎        | 519/3910 [12:32<1:21:38,  1.44s/it]
 13%|█▎        | 520/3910 [12:33<1:21:14,  1.44s/it]
 13%|█▎        | 521/3910 [12:35<1:21:00,  1.43s/it]
 13%|█▎        | 522/3910 [12:36<1:20:59,  1.43s/it]
 13%|█▎        | 523/3910 [12:37<1:19:40,  1.41s/it]
 13%|█▎        | 524/3910 [12:39<1:18:52,  1.40s/it]
 13%|█▎        | 525/3910 [12:40<1:19:26,  1.41s/it]
                                                    

 13%|█▎        | 525/3910 [12:40<1:19:26,  1.41s/it]
 13%|█▎        | 526/3910 [12:42<1:19:45,  1.41s/it]
 13%|█▎        | 527/3910 [12:43<1:20:19,  1.42s/it]
 14%|█▎        | 528/3910 [12:45<1:21:24,  1.44s/it]
 14%|█▎        | 529/3910 [12:46<1:22:07,  1.46s/it]
 14%|█▎        | 530/3910 [12:47<1:21:32,  1.45s/it]
 14%|█▎        | 531/3910 [12:49<1:20:46,  1.43s/it]
 14%|█▎        | 532/3910 [12:50<1:21:42,  1.45s/it]
 14%|█▎        | 533/3910 [12:52<1:21:06,  1.44s/it]
 14%|█▎        | 534/3910 [12:53<1:20:32,  1.43s/it]
 14%|█▎        | 535/3910 [12:55<1:20:58,  1.44s/it]
 14%|█▎        | 536/3910 [12:56<1:21:49,  1.46s/it]
 14%|█▎        | 537/3910 [12:58<1:22:25,  1.47s/it]
 14%|█▍        | 538/3910 [12:59<1:22:34,  1.47s/it]
 14%|█▍        | 539/3910 [13:01<1:21:53,  1.46s/it]
 14%|█▍        | 540/3910 [13:02<1:21:21,  1.45s/it]
 14%|█▍        | 541/3910 [13:03<1:19:21,  1.41s/it]
 14%|█▍        | 542/3910 [13:05<1:20:06,  1.43s/it]
 14%|█▍        | 543/3910 [13:06<1:20:57,  1.44s/it]
 14%|█▍        | 544/3910 [13:08<1:21:25,  1.45s/it]
 14%|█▍        | 545/3910 [13:09<1:22:01,  1.46s/it]
 14%|█▍        | 546/3910 [13:11<1:22:03,  1.46s/it]
 14%|█▍        | 547/3910 [13:12<1:20:17,  1.43s/it]
 14%|█▍        | 548/3910 [13:14<1:21:15,  1.45s/it]
 14%|█▍        | 549/3910 [13:15<1:21:50,  1.46s/it]
 14%|█▍        | 550/3910 [13:16<1:21:09,  1.45s/it]
                                                    

 14%|█▍        | 550/3910 [13:16<1:21:09,  1.45s/it]
 14%|█▍        | 551/3910 [13:18<1:20:54,  1.45s/it]
 14%|█▍        | 552/3910 [13:19<1:21:27,  1.46s/it]
 14%|█▍        | 553/3910 [13:21<1:20:53,  1.45s/it]
 14%|█▍        | 554/3910 [13:22<1:20:45,  1.44s/it]
 14%|█▍        | 555/3910 [13:24<1:18:53,  1.41s/it]
 14%|█▍        | 556/3910 [13:25<1:19:58,  1.43s/it]
 14%|█▍        | 557/3910 [13:26<1:20:33,  1.44s/it]
 14%|█▍        | 558/3910 [13:28<1:22:54,  1.48s/it]
 14%|█▍        | 559/3910 [13:30<1:21:52,  1.47s/it]
 14%|█▍        | 560/3910 [13:31<1:22:11,  1.47s/it]
 14%|█▍        | 561/3910 [13:32<1:22:04,  1.47s/it]
 14%|█▍        | 562/3910 [13:34<1:21:34,  1.46s/it]
 14%|█▍        | 563/3910 [13:35<1:22:00,  1.47s/it]
 14%|█▍        | 564/3910 [13:37<1:22:14,  1.47s/it]
 14%|█▍        | 565/3910 [13:38<1:21:56,  1.47s/it]
 14%|█▍        | 566/3910 [13:40<1:21:35,  1.46s/it]
 15%|█▍        | 567/3910 [13:41<1:21:47,  1.47s/it]
 15%|█▍        | 568/3910 [13:43<1:22:14,  1.48s/it]
 15%|█▍        | 569/3910 [13:44<1:21:42,  1.47s/it]
 15%|█▍        | 570/3910 [13:46<1:21:43,  1.47s/it]
 15%|█▍        | 571/3910 [13:47<1:21:10,  1.46s/it]
 15%|█▍        | 572/3910 [13:49<1:21:39,  1.47s/it]
 15%|█▍        | 573/3910 [13:50<1:21:28,  1.47s/it]
 15%|█▍        | 574/3910 [13:52<1:21:58,  1.47s/it]
 15%|█▍        | 575/3910 [13:53<1:21:46,  1.47s/it]
                                                    

 15%|█▍        | 575/3910 [13:53<1:21:46,  1.47s/it]
 15%|█▍        | 576/3910 [13:54<1:21:44,  1.47s/it]
 15%|█▍        | 577/3910 [13:56<1:19:30,  1.43s/it]
 15%|█▍        | 578/3910 [13:57<1:18:17,  1.41s/it]
 15%|█▍        | 579/3910 [13:59<1:19:38,  1.43s/it]
 15%|█▍        | 580/3910 [14:00<1:20:27,  1.45s/it]
 15%|█▍        | 581/3910 [14:02<1:20:50,  1.46s/it]
 15%|█▍        | 582/3910 [14:03<1:20:18,  1.45s/it]
 15%|█▍        | 583/3910 [14:04<1:19:52,  1.44s/it]
 15%|█▍        | 584/3910 [14:06<1:18:13,  1.41s/it]
 15%|█▍        | 585/3910 [14:07<1:18:49,  1.42s/it]
 15%|█▍        | 586/3910 [14:09<1:17:53,  1.41s/it]
 15%|█▌        | 587/3910 [14:10<1:19:13,  1.43s/it]
 15%|█▌        | 588/3910 [14:12<1:19:54,  1.44s/it]
 15%|█▌        | 589/3910 [14:13<1:20:49,  1.46s/it]
 15%|█▌        | 590/3910 [14:15<1:20:58,  1.46s/it]
 15%|█▌        | 591/3910 [14:16<1:21:21,  1.47s/it]
 15%|█▌        | 592/3910 [14:18<1:21:30,  1.47s/it]
 15%|█▌        | 593/3910 [14:19<1:21:22,  1.47s/it]
 15%|█▌        | 594/3910 [14:20<1:19:50,  1.44s/it]
 15%|█▌        | 595/3910 [14:22<1:20:20,  1.45s/it]
 15%|█▌        | 596/3910 [14:23<1:20:03,  1.45s/it]
 15%|█▌        | 597/3910 [14:25<1:20:20,  1.46s/it]
 15%|█▌        | 598/3910 [14:26<1:19:46,  1.45s/it]
 15%|█▌        | 599/3910 [14:28<1:20:01,  1.45s/it]
 15%|█▌        | 600/3910 [14:29<1:20:36,  1.46s/it]
                                                    

 15%|█▌        | 600/3910 [14:29<1:20:36,  1.46s/it]
 15%|█▌        | 601/3910 [14:31<1:19:52,  1.45s/it]
 15%|█▌        | 602/3910 [14:32<1:18:28,  1.42s/it]
 15%|█▌        | 603/3910 [14:33<1:18:47,  1.43s/it]
 15%|█▌        | 604/3910 [14:35<1:19:43,  1.45s/it]
 15%|█▌        | 605/3910 [14:36<1:19:31,  1.44s/it]
 15%|█▌        | 606/3910 [14:38<1:19:27,  1.44s/it]
 16%|█▌        | 607/3910 [14:39<1:20:14,  1.46s/it]
 16%|█▌        | 608/3910 [14:41<1:20:00,  1.45s/it]
 16%|█▌        | 609/3910 [14:42<1:19:33,  1.45s/it]
 16%|█▌        | 610/3910 [14:44<1:19:21,  1.44s/it]
 16%|█▌        | 611/3910 [14:45<1:18:56,  1.44s/it]
 16%|█▌        | 612/3910 [14:46<1:19:41,  1.45s/it]
 16%|█▌        | 613/3910 [14:48<1:20:05,  1.46s/it]
 16%|█▌        | 614/3910 [14:49<1:19:55,  1.46s/it]
 16%|█▌        | 615/3910 [14:51<1:18:26,  1.43s/it]
 16%|█▌        | 616/3910 [14:52<1:18:02,  1.42s/it]
 16%|█▌        | 617/3910 [14:54<1:18:40,  1.43s/it]
 16%|█▌        | 618/3910 [14:55<1:19:23,  1.45s/it]
 16%|█▌        | 619/3910 [14:57<1:19:39,  1.45s/it]
 16%|█▌        | 620/3910 [14:58<1:20:23,  1.47s/it]
 16%|█▌        | 621/3910 [15:00<1:20:31,  1.47s/it]
 16%|█▌        | 622/3910 [15:01<1:20:18,  1.47s/it]
 16%|█▌        | 623/3910 [15:02<1:19:50,  1.46s/it]
 16%|█▌        | 624/3910 [15:04<1:19:12,  1.45s/it]
 16%|█▌        | 625/3910 [15:05<1:18:52,  1.44s/it]
                                                    

 16%|█▌        | 625/3910 [15:05<1:18:52,  1.44s/it]
 16%|█▌        | 626/3910 [15:07<1:19:28,  1.45s/it]
 16%|█▌        | 627/3910 [15:08<1:18:02,  1.43s/it]
 16%|█▌        | 628/3910 [15:10<1:18:14,  1.43s/it]
 16%|█▌        | 629/3910 [15:11<1:18:04,  1.43s/it]
 16%|█▌        | 630/3910 [15:12<1:17:53,  1.42s/it]
 16%|█▌        | 631/3910 [15:14<1:16:32,  1.40s/it]
 16%|█▌        | 632/3910 [15:15<1:17:25,  1.42s/it]
 16%|█▌        | 633/3910 [15:17<1:17:50,  1.43s/it]
 16%|█▌        | 634/3910 [15:18<1:18:56,  1.45s/it]
 16%|█▌        | 635/3910 [15:20<1:19:36,  1.46s/it]
 16%|█▋        | 636/3910 [15:21<1:18:19,  1.44s/it]
 16%|█▋        | 637/3910 [15:22<1:17:16,  1.42s/it]
 16%|█▋        | 638/3910 [15:24<1:17:20,  1.42s/it]
 16%|█▋        | 639/3910 [15:25<1:18:27,  1.44s/it]
 16%|█▋        | 640/3910 [15:27<1:21:52,  1.50s/it]
 16%|█▋        | 641/3910 [15:28<1:21:43,  1.50s/it]
 16%|█▋        | 642/3910 [15:30<1:20:37,  1.48s/it]
 16%|█▋        | 643/3910 [15:31<1:20:27,  1.48s/it]
 16%|█▋        | 644/3910 [15:33<1:20:39,  1.48s/it]
 16%|█▋        | 645/3910 [15:34<1:19:32,  1.46s/it]
 17%|█▋        | 646/3910 [15:36<1:19:52,  1.47s/it]
 17%|█▋        | 647/3910 [15:37<1:19:50,  1.47s/it]
 17%|█▋        | 648/3910 [15:39<1:19:54,  1.47s/it]
 17%|█▋        | 649/3910 [15:40<1:19:58,  1.47s/it]
 17%|█▋        | 650/3910 [15:42<1:19:58,  1.47s/it]
                                                    

 17%|█▋        | 650/3910 [15:42<1:19:58,  1.47s/it]
 17%|█▋        | 651/3910 [15:43<1:18:58,  1.45s/it]
 17%|█▋        | 652/3910 [15:44<1:18:38,  1.45s/it]
 17%|█▋        | 653/3910 [15:46<1:18:15,  1.44s/it]
 17%|█▋        | 654/3910 [15:47<1:17:57,  1.44s/it]
 17%|█▋        | 655/3910 [15:49<1:17:10,  1.42s/it]
 17%|█▋        | 656/3910 [15:50<1:17:29,  1.43s/it]
 17%|█▋        | 657/3910 [15:52<1:16:29,  1.41s/it]
 17%|█▋        | 658/3910 [15:53<1:15:30,  1.39s/it]
 17%|█▋        | 659/3910 [15:54<1:17:02,  1.42s/it]
 17%|█▋        | 660/3910 [15:56<1:18:07,  1.44s/it]
 17%|█▋        | 661/3910 [15:57<1:18:01,  1.44s/it]
 17%|█▋        | 662/3910 [15:59<1:18:13,  1.44s/it]
 17%|█▋        | 663/3910 [16:00<1:17:46,  1.44s/it]
 17%|█▋        | 664/3910 [16:02<1:20:33,  1.49s/it]
 17%|█▋        | 665/3910 [16:03<1:20:23,  1.49s/it]
 17%|█▋        | 666/3910 [16:05<1:18:40,  1.46s/it]
 17%|█▋        | 667/3910 [16:06<1:18:56,  1.46s/it]
 17%|█▋        | 668/3910 [16:08<1:19:25,  1.47s/it]
 17%|█▋        | 669/3910 [16:09<1:19:16,  1.47s/it]
 17%|█▋        | 670/3910 [16:10<1:18:13,  1.45s/it]
 17%|█▋        | 671/3910 [16:12<1:18:16,  1.45s/it]
 17%|█▋        | 672/3910 [16:13<1:17:08,  1.43s/it]
 17%|█▋        | 673/3910 [16:15<1:17:18,  1.43s/it]
 17%|█▋        | 674/3910 [16:16<1:16:50,  1.42s/it]
 17%|█▋        | 675/3910 [16:18<1:17:15,  1.43s/it]
                                                    

 17%|█▋        | 675/3910 [16:18<1:17:15,  1.43s/it]
 17%|█▋        | 676/3910 [16:19<1:17:55,  1.45s/it]
 17%|█▋        | 677/3910 [16:21<1:18:14,  1.45s/it]
 17%|█▋        | 678/3910 [16:22<1:18:12,  1.45s/it]
 17%|█▋        | 679/3910 [16:23<1:17:02,  1.43s/it]
 17%|█▋        | 680/3910 [16:25<1:17:38,  1.44s/it]
 17%|█▋        | 681/3910 [16:26<1:18:27,  1.46s/it]
 17%|█▋        | 682/3910 [16:28<1:18:46,  1.46s/it]
 17%|█▋        | 683/3910 [16:29<1:17:52,  1.45s/it]
 17%|█▋        | 684/3910 [16:31<1:17:27,  1.44s/it]
 18%|█▊        | 685/3910 [16:32<1:17:08,  1.44s/it]
 18%|█▊        | 686/3910 [16:34<1:18:58,  1.47s/it]
 18%|█▊        | 687/3910 [16:35<1:17:35,  1.44s/it]
 18%|█▊        | 688/3910 [16:36<1:17:59,  1.45s/it]
 18%|█▊        | 689/3910 [16:38<1:18:18,  1.46s/it]
 18%|█▊        | 690/3910 [16:39<1:18:46,  1.47s/it]
 18%|█▊        | 691/3910 [16:41<1:18:57,  1.47s/it]
 18%|█▊        | 692/3910 [16:42<1:18:24,  1.46s/it]
 18%|█▊        | 693/3910 [16:44<1:18:10,  1.46s/it]
 18%|█▊        | 694/3910 [16:45<1:17:34,  1.45s/it]
 18%|█▊        | 695/3910 [16:47<1:18:00,  1.46s/it]
 18%|█▊        | 696/3910 [16:48<1:17:35,  1.45s/it]
 18%|█▊        | 697/3910 [16:50<1:17:22,  1.44s/it]
 18%|█▊        | 698/3910 [16:51<1:17:12,  1.44s/it]
 18%|█▊        | 699/3910 [16:52<1:16:43,  1.43s/it]
 18%|█▊        | 700/3910 [16:54<1:15:53,  1.42s/it]
                                                    

 18%|█▊        | 700/3910 [16:54<1:15:53,  1.42s/it]
 18%|█▊        | 701/3910 [16:55<1:16:40,  1.43s/it]
 18%|█▊        | 702/3910 [16:57<1:16:16,  1.43s/it]
 18%|█▊        | 703/3910 [16:58<1:16:28,  1.43s/it]
 18%|█▊        | 704/3910 [17:00<1:17:01,  1.44s/it]
 18%|█▊        | 705/3910 [17:01<1:17:40,  1.45s/it]
 18%|█▊        | 706/3910 [17:03<1:18:15,  1.47s/it]
 18%|█▊        | 707/3910 [17:04<1:17:51,  1.46s/it]
 18%|█▊        | 708/3910 [17:05<1:17:54,  1.46s/it]
 18%|█▊        | 709/3910 [17:07<1:17:09,  1.45s/it]
 18%|█▊        | 710/3910 [17:08<1:17:30,  1.45s/it]
 18%|█▊        | 711/3910 [17:10<1:17:28,  1.45s/it]
 18%|█▊        | 712/3910 [17:11<1:16:21,  1.43s/it]
 18%|█▊        | 713/3910 [17:13<1:16:50,  1.44s/it]
 18%|█▊        | 714/3910 [17:14<1:17:09,  1.45s/it]
 18%|█▊        | 715/3910 [17:16<1:16:56,  1.44s/it]
 18%|█▊        | 716/3910 [17:17<1:16:45,  1.44s/it]
 18%|█▊        | 717/3910 [17:18<1:16:55,  1.45s/it]
 18%|█▊        | 718/3910 [17:20<1:17:22,  1.45s/it]
 18%|█▊        | 719/3910 [17:21<1:17:50,  1.46s/it]
 18%|█▊        | 720/3910 [17:23<1:16:25,  1.44s/it]
 18%|█▊        | 721/3910 [17:24<1:16:45,  1.44s/it]
 18%|█▊        | 722/3910 [17:26<1:16:39,  1.44s/it]
 18%|█▊        | 723/3910 [17:27<1:16:38,  1.44s/it]
 19%|█▊        | 724/3910 [17:29<1:17:21,  1.46s/it]
 19%|█▊        | 725/3910 [17:30<1:17:35,  1.46s/it]
                                                    

 19%|█▊        | 725/3910 [17:30<1:17:35,  1.46s/it]
 19%|█▊        | 726/3910 [17:32<1:18:01,  1.47s/it]
 19%|█▊        | 727/3910 [17:33<1:16:54,  1.45s/it]
 19%|█▊        | 728/3910 [17:34<1:16:42,  1.45s/it]
 19%|█▊        | 729/3910 [17:36<1:15:16,  1.42s/it]
 19%|█▊        | 730/3910 [17:37<1:14:49,  1.41s/it]
 19%|█▊        | 731/3910 [17:39<1:14:58,  1.42s/it]
 19%|█▊        | 732/3910 [17:40<1:15:18,  1.42s/it]
 19%|█▊        | 733/3910 [17:42<1:16:30,  1.44s/it]
 19%|█▉        | 734/3910 [17:43<1:17:02,  1.46s/it]
 19%|█▉        | 735/3910 [17:44<1:16:43,  1.45s/it]
 19%|█▉        | 736/3910 [17:46<1:17:15,  1.46s/it]
 19%|█▉        | 737/3910 [17:47<1:17:13,  1.46s/it]
 19%|█▉        | 738/3910 [17:49<1:16:06,  1.44s/it]
 19%|█▉        | 739/3910 [17:50<1:18:34,  1.49s/it]
 19%|█▉        | 740/3910 [17:52<1:17:39,  1.47s/it]
 19%|█▉        | 741/3910 [17:53<1:17:34,  1.47s/it]
 19%|█▉        | 742/3910 [17:55<1:17:44,  1.47s/it]
 19%|█▉        | 743/3910 [17:56<1:17:02,  1.46s/it]
 19%|█▉        | 744/3910 [17:58<1:17:18,  1.47s/it]
 19%|█▉        | 745/3910 [17:59<1:17:46,  1.47s/it]
 19%|█▉        | 746/3910 [18:01<1:20:33,  1.53s/it]
 19%|█▉        | 747/3910 [18:02<1:18:46,  1.49s/it]
 19%|█▉        | 748/3910 [18:04<1:17:58,  1.48s/it]
 19%|█▉        | 749/3910 [18:05<1:16:53,  1.46s/it]
 19%|█▉        | 750/3910 [18:07<1:16:30,  1.45s/it]
                                                    

 19%|█▉        | 750/3910 [18:07<1:16:30,  1.45s/it]
 19%|█▉        | 751/3910 [18:08<1:16:02,  1.44s/it]
 19%|█▉        | 752/3910 [18:09<1:16:32,  1.45s/it]
 19%|█▉        | 753/3910 [18:11<1:15:49,  1.44s/it]
 19%|█▉        | 754/3910 [18:12<1:16:11,  1.45s/it]
 19%|█▉        | 755/3910 [18:14<1:16:18,  1.45s/it]
 19%|█▉        | 756/3910 [18:15<1:16:23,  1.45s/it]
 19%|█▉        | 757/3910 [18:17<1:16:10,  1.45s/it]
 19%|█▉        | 758/3910 [18:18<1:16:11,  1.45s/it]
 19%|█▉        | 759/3910 [18:20<1:15:51,  1.44s/it]
 19%|█▉        | 760/3910 [18:21<1:16:21,  1.45s/it]
 19%|█▉        | 761/3910 [18:22<1:16:25,  1.46s/it]
 19%|█▉        | 762/3910 [18:24<1:16:50,  1.46s/it]
 20%|█▉        | 763/3910 [18:25<1:16:29,  1.46s/it]
 20%|█▉        | 764/3910 [18:27<1:16:14,  1.45s/it]
 20%|█▉        | 765/3910 [18:28<1:15:36,  1.44s/it]
 20%|█▉        | 766/3910 [18:30<1:14:44,  1.43s/it]
 20%|█▉        | 767/3910 [18:31<1:15:24,  1.44s/it]
 20%|█▉        | 768/3910 [18:33<1:15:44,  1.45s/it]
 20%|█▉        | 769/3910 [18:34<1:15:06,  1.43s/it]
 20%|█▉        | 770/3910 [18:35<1:14:39,  1.43s/it]
 20%|█▉        | 771/3910 [18:37<1:14:39,  1.43s/it]
 20%|█▉        | 772/3910 [18:38<1:14:26,  1.42s/it]
 20%|█▉        | 773/3910 [18:40<1:15:09,  1.44s/it]
 20%|█▉        | 774/3910 [18:41<1:15:11,  1.44s/it]
 20%|█▉        | 775/3910 [18:43<1:15:47,  1.45s/it]
                                                    

 20%|█▉        | 775/3910 [18:43<1:15:47,  1.45s/it]
 20%|█▉        | 776/3910 [18:44<1:15:48,  1.45s/it]
 20%|█▉        | 777/3910 [18:46<1:15:28,  1.45s/it]
 20%|█▉        | 778/3910 [18:47<1:15:52,  1.45s/it]
 20%|█▉        | 779/3910 [18:48<1:16:12,  1.46s/it]
 20%|█▉        | 780/3910 [18:50<1:15:52,  1.45s/it]
 20%|█▉        | 781/3910 [18:51<1:15:22,  1.45s/it]
 20%|██        | 782/3910 [18:53<1:15:06,  1.44s/it]
 20%|██        | 783/3910 [18:54<1:15:32,  1.45s/it]
 20%|██        | 784/3910 [18:56<1:15:45,  1.45s/it]
 20%|██        | 785/3910 [18:57<1:15:34,  1.45s/it]
 20%|██        | 786/3910 [18:59<1:15:25,  1.45s/it]
 20%|██        | 787/3910 [19:00<1:15:07,  1.44s/it]
 20%|██        | 788/3910 [19:01<1:14:42,  1.44s/it]
 20%|██        | 789/3910 [19:03<1:14:42,  1.44s/it]
 20%|██        | 790/3910 [19:04<1:14:08,  1.43s/it]
 20%|██        | 791/3910 [19:06<1:14:03,  1.42s/it]
 20%|██        | 792/3910 [19:07<1:13:53,  1.42s/it]
 20%|██        | 793/3910 [19:09<1:14:14,  1.43s/it]
 20%|██        | 794/3910 [19:10<1:14:26,  1.43s/it]
 20%|██        | 795/3910 [19:11<1:14:53,  1.44s/it]
 20%|██        | 796/3910 [19:13<1:14:40,  1.44s/it]
 20%|██        | 797/3910 [19:14<1:14:51,  1.44s/it]
 20%|██        | 798/3910 [19:16<1:15:01,  1.45s/it]
 20%|██        | 799/3910 [19:17<1:15:09,  1.45s/it]
 20%|██        | 800/3910 [19:19<1:15:14,  1.45s/it]
                                                    

 20%|██        | 800/3910 [19:19<1:15:14,  1.45s/it]
 20%|██        | 801/3910 [19:20<1:15:47,  1.46s/it]
 21%|██        | 802/3910 [19:22<1:16:01,  1.47s/it]
 21%|██        | 803/3910 [19:23<1:15:27,  1.46s/it]
 21%|██        | 804/3910 [19:25<1:15:39,  1.46s/it]
 21%|██        | 805/3910 [19:26<1:14:55,  1.45s/it]
 21%|██        | 806/3910 [19:27<1:15:23,  1.46s/it]
 21%|██        | 807/3910 [19:29<1:15:08,  1.45s/it]
 21%|██        | 808/3910 [19:30<1:13:40,  1.43s/it]
 21%|██        | 809/3910 [19:32<1:14:10,  1.44s/it]
 21%|██        | 810/3910 [19:33<1:13:45,  1.43s/it]
 21%|██        | 811/3910 [19:35<1:14:34,  1.44s/it]
 21%|██        | 812/3910 [19:36<1:15:24,  1.46s/it]
 21%|██        | 813/3910 [19:38<1:14:46,  1.45s/it]
 21%|██        | 814/3910 [19:39<1:14:49,  1.45s/it]
 21%|██        | 815/3910 [19:40<1:14:04,  1.44s/it]
 21%|██        | 816/3910 [19:42<1:13:47,  1.43s/it]
 21%|██        | 817/3910 [19:43<1:13:41,  1.43s/it]
 21%|██        | 818/3910 [19:45<1:14:02,  1.44s/it]
 21%|██        | 819/3910 [19:46<1:16:01,  1.48s/it]
 21%|██        | 820/3910 [19:48<1:15:47,  1.47s/it]
 21%|██        | 821/3910 [19:49<1:15:44,  1.47s/it]
 21%|██        | 822/3910 [19:51<1:14:15,  1.44s/it]
 21%|██        | 823/3910 [19:52<1:15:06,  1.46s/it]
 21%|██        | 824/3910 [19:54<1:15:23,  1.47s/it]
 21%|██        | 825/3910 [19:55<1:14:43,  1.45s/it]
                                                    

 21%|██        | 825/3910 [19:55<1:14:43,  1.45s/it]
 21%|██        | 826/3910 [19:56<1:15:13,  1.46s/it]
 21%|██        | 827/3910 [19:58<1:14:28,  1.45s/it]
 21%|██        | 828/3910 [19:59<1:14:26,  1.45s/it]
 21%|██        | 829/3910 [20:01<1:14:03,  1.44s/it]
 21%|██        | 830/3910 [20:02<1:14:03,  1.44s/it]
 21%|██▏       | 831/3910 [20:04<1:13:16,  1.43s/it]
 21%|██▏       | 832/3910 [20:05<1:13:52,  1.44s/it]
 21%|██▏       | 833/3910 [20:07<1:13:51,  1.44s/it]
 21%|██▏       | 834/3910 [20:08<1:12:58,  1.42s/it]
 21%|██▏       | 835/3910 [20:09<1:12:13,  1.41s/it]
 21%|██▏       | 836/3910 [20:11<1:12:56,  1.42s/it]
 21%|██▏       | 837/3910 [20:12<1:12:54,  1.42s/it]
 21%|██▏       | 838/3910 [20:14<1:13:38,  1.44s/it]
 21%|██▏       | 839/3910 [20:15<1:13:59,  1.45s/it]
 21%|██▏       | 840/3910 [20:17<1:13:44,  1.44s/it]
 22%|██▏       | 841/3910 [20:18<1:12:55,  1.43s/it]
 22%|██▏       | 842/3910 [20:19<1:13:37,  1.44s/it]
 22%|██▏       | 843/3910 [20:21<1:13:41,  1.44s/it]
 22%|██▏       | 844/3910 [20:22<1:12:38,  1.42s/it]
 22%|██▏       | 845/3910 [20:24<1:12:59,  1.43s/it]
 22%|██▏       | 846/3910 [20:25<1:12:14,  1.41s/it]
 22%|██▏       | 847/3910 [20:26<1:12:31,  1.42s/it]
 22%|██▏       | 848/3910 [20:28<1:13:19,  1.44s/it]
 22%|██▏       | 849/3910 [20:29<1:12:29,  1.42s/it]
 22%|██▏       | 850/3910 [20:31<1:11:14,  1.40s/it]
                                                    

 22%|██▏       | 850/3910 [20:31<1:11:14,  1.40s/it]
 22%|██▏       | 851/3910 [20:32<1:11:43,  1.41s/it]
 22%|██▏       | 852/3910 [20:34<1:12:08,  1.42s/it]
 22%|██▏       | 853/3910 [20:35<1:10:59,  1.39s/it]
 22%|██▏       | 854/3910 [20:36<1:12:28,  1.42s/it]
 22%|██▏       | 855/3910 [20:38<1:12:30,  1.42s/it]
 22%|██▏       | 856/3910 [20:39<1:13:06,  1.44s/it]
 22%|██▏       | 857/3910 [20:41<1:13:51,  1.45s/it]
 22%|██▏       | 858/3910 [20:42<1:13:50,  1.45s/it]
 22%|██▏       | 859/3910 [20:44<1:12:52,  1.43s/it]
 22%|██▏       | 860/3910 [20:45<1:13:38,  1.45s/it]
 22%|██▏       | 861/3910 [20:47<1:13:49,  1.45s/it]
 22%|██▏       | 862/3910 [20:48<1:14:20,  1.46s/it]
 22%|██▏       | 863/3910 [20:49<1:14:09,  1.46s/it]
 22%|██▏       | 864/3910 [20:51<1:13:48,  1.45s/it]
 22%|██▏       | 865/3910 [20:52<1:14:16,  1.46s/it]
 22%|██▏       | 866/3910 [20:54<1:13:51,  1.46s/it]
 22%|██▏       | 867/3910 [20:55<1:14:25,  1.47s/it]
 22%|██▏       | 868/3910 [20:57<1:14:02,  1.46s/it]
 22%|██▏       | 869/3910 [20:58<1:14:22,  1.47s/it]
 22%|██▏       | 870/3910 [21:00<1:13:57,  1.46s/it]
 22%|██▏       | 871/3910 [21:01<1:14:10,  1.46s/it]
 22%|██▏       | 872/3910 [21:03<1:14:14,  1.47s/it]
 22%|██▏       | 873/3910 [21:04<1:13:32,  1.45s/it]
 22%|██▏       | 874/3910 [21:05<1:12:58,  1.44s/it]
 22%|██▏       | 875/3910 [21:07<1:12:55,  1.44s/it]
                                                    

 22%|██▏       | 875/3910 [21:07<1:12:55,  1.44s/it]
 22%|██▏       | 876/3910 [21:08<1:13:26,  1.45s/it]
 22%|██▏       | 877/3910 [21:10<1:13:15,  1.45s/it]
 22%|██▏       | 878/3910 [21:11<1:12:06,  1.43s/it]
 22%|██▏       | 879/3910 [21:13<1:12:59,  1.44s/it]
 23%|██▎       | 880/3910 [21:14<1:12:50,  1.44s/it]
 23%|██▎       | 881/3910 [21:16<1:13:00,  1.45s/it]
 23%|██▎       | 882/3910 [21:17<1:12:56,  1.45s/it]
 23%|██▎       | 883/3910 [21:18<1:12:21,  1.43s/it]
 23%|██▎       | 884/3910 [21:20<1:13:12,  1.45s/it]
 23%|██▎       | 885/3910 [21:21<1:12:57,  1.45s/it]
 23%|██▎       | 886/3910 [21:23<1:12:24,  1.44s/it]
 23%|██▎       | 887/3910 [21:24<1:13:01,  1.45s/it]
 23%|██▎       | 888/3910 [21:26<1:13:08,  1.45s/it]
 23%|██▎       | 889/3910 [21:27<1:13:46,  1.47s/it]
 23%|██▎       | 890/3910 [21:29<1:13:58,  1.47s/it]
 23%|██▎       | 891/3910 [21:30<1:13:58,  1.47s/it]
 23%|██▎       | 892/3910 [21:32<1:13:45,  1.47s/it]
 23%|██▎       | 893/3910 [21:33<1:13:08,  1.45s/it]
 23%|██▎       | 894/3910 [21:35<1:13:17,  1.46s/it]
 23%|██▎       | 895/3910 [21:36<1:13:25,  1.46s/it]
 23%|██▎       | 896/3910 [21:37<1:13:22,  1.46s/it]
 23%|██▎       | 897/3910 [21:39<1:11:55,  1.43s/it]
 23%|██▎       | 898/3910 [21:40<1:12:00,  1.43s/it]
 23%|██▎       | 899/3910 [21:42<1:12:47,  1.45s/it]
 23%|██▎       | 900/3910 [21:43<1:12:49,  1.45s/it]
                                                    

 23%|██▎       | 900/3910 [21:43<1:12:49,  1.45s/it]
 23%|██▎       | 901/3910 [21:45<1:13:06,  1.46s/it]
 23%|██▎       | 902/3910 [21:46<1:12:51,  1.45s/it]
 23%|██▎       | 903/3910 [21:48<1:12:37,  1.45s/it]
 23%|██▎       | 904/3910 [21:49<1:12:15,  1.44s/it]
 23%|██▎       | 905/3910 [21:50<1:11:25,  1.43s/it]
 23%|██▎       | 906/3910 [21:52<1:12:21,  1.45s/it]
 23%|██▎       | 907/3910 [21:53<1:12:42,  1.45s/it]
 23%|██▎       | 908/3910 [21:55<1:13:16,  1.46s/it]
 23%|██▎       | 909/3910 [21:56<1:12:42,  1.45s/it]
 23%|██▎       | 910/3910 [21:58<1:12:28,  1.45s/it]
 23%|██▎       | 911/3910 [21:59<1:12:57,  1.46s/it]
 23%|██▎       | 912/3910 [22:01<1:12:22,  1.45s/it]
 23%|██▎       | 913/3910 [22:02<1:13:00,  1.46s/it]
 23%|██▎       | 914/3910 [22:04<1:13:13,  1.47s/it]
 23%|██▎       | 915/3910 [22:05<1:12:27,  1.45s/it]
 23%|██▎       | 916/3910 [22:06<1:12:31,  1.45s/it]
 23%|██▎       | 917/3910 [22:08<1:12:33,  1.45s/it]
 23%|██▎       | 918/3910 [22:09<1:12:18,  1.45s/it]
 24%|██▎       | 919/3910 [22:11<1:12:47,  1.46s/it]
 24%|██▎       | 920/3910 [22:12<1:11:58,  1.44s/it]
 24%|██▎       | 921/3910 [22:14<1:12:06,  1.45s/it]
 24%|██▎       | 922/3910 [22:15<1:12:40,  1.46s/it]
 24%|██▎       | 923/3910 [22:17<1:12:50,  1.46s/it]
 24%|██▎       | 924/3910 [22:18<1:13:07,  1.47s/it]
 24%|██▎       | 925/3910 [22:20<1:12:52,  1.46s/it]
                                                    

 24%|██▎       | 925/3910 [22:20<1:12:52,  1.46s/it]
 24%|██▎       | 926/3910 [22:21<1:12:14,  1.45s/it]
 24%|██▎       | 927/3910 [22:22<1:12:37,  1.46s/it]
 24%|██▎       | 928/3910 [22:24<1:12:29,  1.46s/it]
 24%|██▍       | 929/3910 [22:25<1:12:17,  1.45s/it]
 24%|██▍       | 930/3910 [22:27<1:12:45,  1.46s/it]
 24%|██▍       | 931/3910 [22:28<1:11:39,  1.44s/it]
 24%|██▍       | 932/3910 [22:30<1:12:17,  1.46s/it]
 24%|██▍       | 933/3910 [22:31<1:11:22,  1.44s/it]
 24%|██▍       | 934/3910 [22:33<1:11:19,  1.44s/it]
 24%|██▍       | 935/3910 [22:34<1:11:45,  1.45s/it]
 24%|██▍       | 936/3910 [22:35<1:11:28,  1.44s/it]
 24%|██▍       | 937/3910 [22:37<1:10:38,  1.43s/it]
 24%|██▍       | 938/3910 [22:38<1:10:33,  1.42s/it]
 24%|██▍       | 939/3910 [22:40<1:11:33,  1.45s/it]
 24%|██▍       | 940/3910 [22:41<1:10:44,  1.43s/it]
 24%|██▍       | 941/3910 [22:43<1:11:40,  1.45s/it]
 24%|██▍       | 942/3910 [22:44<1:12:18,  1.46s/it]
 24%|██▍       | 943/3910 [22:46<1:11:51,  1.45s/it]
 24%|██▍       | 944/3910 [22:47<1:11:37,  1.45s/it]
 24%|██▍       | 945/3910 [22:48<1:11:52,  1.45s/it]
 24%|██▍       | 946/3910 [22:50<1:11:22,  1.44s/it]
 24%|██▍       | 947/3910 [22:52<1:14:12,  1.50s/it]
 24%|██▍       | 948/3910 [22:53<1:13:39,  1.49s/it]
 24%|██▍       | 949/3910 [22:54<1:12:03,  1.46s/it]
 24%|██▍       | 950/3910 [22:56<1:11:11,  1.44s/it]
                                                    

 24%|██▍       | 950/3910 [22:56<1:11:11,  1.44s/it]
 24%|██▍       | 951/3910 [22:57<1:10:36,  1.43s/it]
 24%|██▍       | 952/3910 [22:59<1:10:00,  1.42s/it]
 24%|██▍       | 953/3910 [23:00<1:09:57,  1.42s/it]
 24%|██▍       | 954/3910 [23:01<1:10:10,  1.42s/it]
 24%|██▍       | 955/3910 [23:03<1:10:30,  1.43s/it]
 24%|██▍       | 956/3910 [23:04<1:11:05,  1.44s/it]
 24%|██▍       | 957/3910 [23:06<1:11:05,  1.44s/it]
 25%|██▍       | 958/3910 [23:07<1:11:41,  1.46s/it]
 25%|██▍       | 959/3910 [23:09<1:11:37,  1.46s/it]
 25%|██▍       | 960/3910 [23:10<1:11:03,  1.45s/it]
 25%|██▍       | 961/3910 [23:12<1:10:57,  1.44s/it]
 25%|██▍       | 962/3910 [23:13<1:09:59,  1.42s/it]
 25%|██▍       | 963/3910 [23:15<1:10:53,  1.44s/it]
 25%|██▍       | 964/3910 [23:16<1:11:11,  1.45s/it]
 25%|██▍       | 965/3910 [23:17<1:11:29,  1.46s/it]
 25%|██▍       | 966/3910 [23:19<1:11:59,  1.47s/it]
 25%|██▍       | 967/3910 [23:20<1:12:09,  1.47s/it]
 25%|██▍       | 968/3910 [23:22<1:11:53,  1.47s/it]
 25%|██▍       | 969/3910 [23:23<1:11:52,  1.47s/it]
 25%|██▍       | 970/3910 [23:25<1:11:30,  1.46s/it]
 25%|██▍       | 971/3910 [23:26<1:10:54,  1.45s/it]
 25%|██▍       | 972/3910 [23:28<1:11:27,  1.46s/it]
 25%|██▍       | 973/3910 [23:29<1:11:46,  1.47s/it]
 25%|██▍       | 974/3910 [23:31<1:10:55,  1.45s/it]
 25%|██▍       | 975/3910 [23:32<1:10:21,  1.44s/it]
                                                    

 25%|██▍       | 975/3910 [23:32<1:10:21,  1.44s/it]
 25%|██▍       | 976/3910 [23:33<1:11:04,  1.45s/it]
 25%|██▍       | 977/3910 [23:35<1:11:00,  1.45s/it]
 25%|██▌       | 978/3910 [23:36<1:11:09,  1.46s/it]
 25%|██▌       | 979/3910 [23:38<1:10:30,  1.44s/it]
 25%|██▌       | 980/3910 [23:39<1:10:10,  1.44s/it]
 25%|██▌       | 981/3910 [23:41<1:10:57,  1.45s/it]
 25%|██▌       | 982/3910 [23:42<1:10:10,  1.44s/it]
 25%|██▌       | 983/3910 [23:44<1:10:46,  1.45s/it]
 25%|██▌       | 984/3910 [23:45<1:11:18,  1.46s/it]
 25%|██▌       | 985/3910 [23:46<1:10:00,  1.44s/it]
 25%|██▌       | 986/3910 [23:48<1:10:02,  1.44s/it]
 25%|██▌       | 987/3910 [23:49<1:10:55,  1.46s/it]
 25%|██▌       | 988/3910 [23:51<1:11:23,  1.47s/it]
 25%|██▌       | 989/3910 [23:52<1:11:15,  1.46s/it]
 25%|██▌       | 990/3910 [23:54<1:10:36,  1.45s/it]
 25%|██▌       | 991/3910 [23:55<1:10:21,  1.45s/it]
 25%|██▌       | 992/3910 [23:57<1:10:58,  1.46s/it]
 25%|██▌       | 993/3910 [23:58<1:10:23,  1.45s/it]
 25%|██▌       | 994/3910 [24:00<1:10:10,  1.44s/it]
 25%|██▌       | 995/3910 [24:01<1:10:04,  1.44s/it]
 25%|██▌       | 996/3910 [24:02<1:10:17,  1.45s/it]
 25%|██▌       | 997/3910 [24:04<1:10:58,  1.46s/it]
 26%|██▌       | 998/3910 [24:05<1:10:32,  1.45s/it]
 26%|██▌       | 999/3910 [24:07<1:11:04,  1.46s/it]
 26%|██▌       | 1000/3910 [24:08<1:11:01,  1.46s/it]
                                                     

 26%|██▌       | 1000/3910 [24:08<1:11:01,  1.46s/it]
 26%|██▌       | 1001/3910 [24:10<1:10:39,  1.46s/it]
 26%|██▌       | 1002/3910 [24:11<1:09:38,  1.44s/it]
 26%|██▌       | 1003/3910 [24:13<1:09:24,  1.43s/it]
 26%|██▌       | 1004/3910 [24:14<1:08:41,  1.42s/it]
 26%|██▌       | 1005/3910 [24:15<1:09:08,  1.43s/it]
 26%|██▌       | 1006/3910 [24:17<1:09:25,  1.43s/it]
 26%|██▌       | 1007/3910 [24:18<1:08:27,  1.41s/it]
 26%|██▌       | 1008/3910 [24:20<1:08:46,  1.42s/it]
 26%|██▌       | 1009/3910 [24:21<1:09:12,  1.43s/it]
 26%|██▌       | 1010/3910 [24:23<1:09:23,  1.44s/it]
 26%|██▌       | 1011/3910 [24:24<1:10:09,  1.45s/it]
 26%|██▌       | 1012/3910 [24:26<1:13:12,  1.52s/it]
 26%|██▌       | 1013/3910 [24:27<1:12:45,  1.51s/it]
 26%|██▌       | 1014/3910 [24:29<1:12:33,  1.50s/it]
 26%|██▌       | 1015/3910 [24:30<1:11:21,  1.48s/it]
 26%|██▌       | 1016/3910 [24:32<1:11:18,  1.48s/it]
 26%|██▌       | 1017/3910 [24:33<1:10:00,  1.45s/it]
 26%|██▌       | 1018/3910 [24:34<1:10:06,  1.45s/it]
 26%|██▌       | 1019/3910 [24:36<1:10:08,  1.46s/it]
 26%|██▌       | 1020/3910 [24:37<1:10:04,  1.45s/it]
 26%|██▌       | 1021/3910 [24:39<1:08:52,  1.43s/it]
 26%|██▌       | 1022/3910 [24:40<1:09:13,  1.44s/it]
 26%|██▌       | 1023/3910 [24:42<1:09:17,  1.44s/it]
 26%|██▌       | 1024/3910 [24:43<1:09:14,  1.44s/it]
 26%|██▌       | 1025/3910 [24:45<1:09:42,  1.45s/it]
                                                     

 26%|██▌       | 1025/3910 [24:45<1:09:42,  1.45s/it]
 26%|██▌       | 1026/3910 [24:46<1:09:57,  1.46s/it]
 26%|██▋       | 1027/3910 [24:47<1:08:25,  1.42s/it]
 26%|██▋       | 1028/3910 [24:49<1:09:23,  1.44s/it]
 26%|██▋       | 1029/3910 [24:50<1:09:20,  1.44s/it]
 26%|██▋       | 1030/3910 [24:52<1:08:33,  1.43s/it]
 26%|██▋       | 1031/3910 [24:53<1:09:16,  1.44s/it]
 26%|██▋       | 1032/3910 [24:55<1:09:38,  1.45s/it]
 26%|██▋       | 1033/3910 [24:56<1:08:35,  1.43s/it]
 26%|██▋       | 1034/3910 [24:57<1:08:46,  1.43s/it]
 26%|██▋       | 1035/3910 [24:59<1:09:11,  1.44s/it]
 26%|██▋       | 1036/3910 [25:00<1:08:53,  1.44s/it]
 27%|██▋       | 1037/3910 [25:02<1:08:45,  1.44s/it]
 27%|██▋       | 1038/3910 [25:03<1:09:32,  1.45s/it]
 27%|██▋       | 1039/3910 [25:05<1:09:22,  1.45s/it]
 27%|██▋       | 1040/3910 [25:06<1:09:26,  1.45s/it]
 27%|██▋       | 1041/3910 [25:08<1:09:15,  1.45s/it]
 27%|██▋       | 1042/3910 [25:09<1:09:38,  1.46s/it]
 27%|██▋       | 1043/3910 [25:11<1:10:12,  1.47s/it]
 27%|██▋       | 1044/3910 [25:12<1:09:43,  1.46s/it]
 27%|██▋       | 1045/3910 [25:14<1:10:05,  1.47s/it]
 27%|██▋       | 1046/3910 [25:15<1:10:26,  1.48s/it]
 27%|██▋       | 1047/3910 [25:17<1:10:40,  1.48s/it]
 27%|██▋       | 1048/3910 [25:18<1:10:01,  1.47s/it]
 27%|██▋       | 1049/3910 [25:19<1:10:15,  1.47s/it]
 27%|██▋       | 1050/3910 [25:21<1:09:57,  1.47s/it]
                                                     

 27%|██▋       | 1050/3910 [25:21<1:09:57,  1.47s/it]
 27%|██▋       | 1051/3910 [25:22<1:10:22,  1.48s/it]
 27%|██▋       | 1052/3910 [25:24<1:10:08,  1.47s/it]
 27%|██▋       | 1053/3910 [25:25<1:09:43,  1.46s/it]
 27%|██▋       | 1054/3910 [25:27<1:09:47,  1.47s/it]
 27%|██▋       | 1055/3910 [25:28<1:09:47,  1.47s/it]
 27%|██▋       | 1056/3910 [25:30<1:10:03,  1.47s/it]
 27%|██▋       | 1057/3910 [25:31<1:10:10,  1.48s/it]
 27%|██▋       | 1058/3910 [25:33<1:09:55,  1.47s/it]
 27%|██▋       | 1059/3910 [25:34<1:13:07,  1.54s/it]
 27%|██▋       | 1060/3910 [25:36<1:10:40,  1.49s/it]
 27%|██▋       | 1061/3910 [25:37<1:10:03,  1.48s/it]
 27%|██▋       | 1062/3910 [25:39<1:09:24,  1.46s/it]
 27%|██▋       | 1063/3910 [25:40<1:08:32,  1.44s/it]
 27%|██▋       | 1064/3910 [25:42<1:09:00,  1.45s/it]
 27%|██▋       | 1065/3910 [25:43<1:09:00,  1.46s/it]
 27%|██▋       | 1066/3910 [25:44<1:08:26,  1.44s/it]
 27%|██▋       | 1067/3910 [25:46<1:07:55,  1.43s/it]
 27%|██▋       | 1068/3910 [25:47<1:07:37,  1.43s/it]
 27%|██▋       | 1069/3910 [25:49<1:07:29,  1.43s/it]
 27%|██▋       | 1070/3910 [25:50<1:06:44,  1.41s/it]
 27%|██▋       | 1071/3910 [25:51<1:07:40,  1.43s/it]
 27%|██▋       | 1072/3910 [25:53<1:07:27,  1.43s/it]
 27%|██▋       | 1073/3910 [25:54<1:07:55,  1.44s/it]
 27%|██▋       | 1074/3910 [25:56<1:07:45,  1.43s/it]
 27%|██▋       | 1075/3910 [25:57<1:07:31,  1.43s/it]
                                                     

 27%|██▋       | 1075/3910 [25:57<1:07:31,  1.43s/it]
 28%|██▊       | 1076/3910 [25:59<1:07:21,  1.43s/it]
 28%|██▊       | 1077/3910 [26:00<1:08:13,  1.45s/it]
 28%|██▊       | 1078/3910 [26:02<1:07:53,  1.44s/it]
 28%|██▊       | 1079/3910 [26:03<1:08:38,  1.45s/it]
 28%|██▊       | 1080/3910 [26:04<1:07:58,  1.44s/it]
 28%|██▊       | 1081/3910 [26:06<1:07:50,  1.44s/it]
 28%|██▊       | 1082/3910 [26:07<1:07:43,  1.44s/it]
 28%|██▊       | 1083/3910 [26:09<1:08:20,  1.45s/it]
 28%|██▊       | 1084/3910 [26:10<1:07:52,  1.44s/it]
 28%|██▊       | 1085/3910 [26:12<1:08:24,  1.45s/it]
 28%|██▊       | 1086/3910 [26:13<1:07:49,  1.44s/it]
 28%|██▊       | 1087/3910 [26:15<1:07:54,  1.44s/it]
 28%|██▊       | 1088/3910 [26:16<1:07:36,  1.44s/it]
 28%|██▊       | 1089/3910 [26:17<1:08:22,  1.45s/it]
 28%|██▊       | 1090/3910 [26:19<1:08:33,  1.46s/it]
 28%|██▊       | 1091/3910 [26:20<1:08:05,  1.45s/it]
 28%|██▊       | 1092/3910 [26:22<1:07:24,  1.44s/it]
 28%|██▊       | 1093/3910 [26:23<1:05:34,  1.40s/it]
 28%|██▊       | 1094/3910 [26:25<1:06:11,  1.41s/it]
 28%|██▊       | 1095/3910 [26:26<1:07:01,  1.43s/it]
 28%|██▊       | 1096/3910 [26:27<1:07:06,  1.43s/it]
 28%|██▊       | 1097/3910 [26:29<1:07:05,  1.43s/it]
 28%|██▊       | 1098/3910 [26:30<1:07:50,  1.45s/it]
 28%|██▊       | 1099/3910 [26:32<1:08:30,  1.46s/it]
 28%|██▊       | 1100/3910 [26:33<1:07:31,  1.44s/it]
                                                     

 28%|██▊       | 1100/3910 [26:33<1:07:31,  1.44s/it]
 28%|██▊       | 1101/3910 [26:35<1:07:27,  1.44s/it]
 28%|██▊       | 1102/3910 [26:36<1:08:06,  1.46s/it]
 28%|██▊       | 1103/3910 [26:38<1:07:44,  1.45s/it]
 28%|██▊       | 1104/3910 [26:39<1:07:29,  1.44s/it]
 28%|██▊       | 1105/3910 [26:40<1:07:59,  1.45s/it]
 28%|██▊       | 1106/3910 [26:42<1:08:05,  1.46s/it]
 28%|██▊       | 1107/3910 [26:43<1:07:44,  1.45s/it]
 28%|██▊       | 1108/3910 [26:45<1:07:42,  1.45s/it]
 28%|██▊       | 1109/3910 [26:46<1:07:28,  1.45s/it]
 28%|██▊       | 1110/3910 [26:48<1:08:16,  1.46s/it]
 28%|██▊       | 1111/3910 [26:49<1:08:26,  1.47s/it]
 28%|██▊       | 1112/3910 [26:51<1:08:24,  1.47s/it]
 28%|██▊       | 1113/3910 [26:52<1:08:21,  1.47s/it]
 28%|██▊       | 1114/3910 [26:54<1:08:07,  1.46s/it]
 29%|██▊       | 1115/3910 [26:55<1:07:28,  1.45s/it]
 29%|██▊       | 1116/3910 [26:56<1:06:39,  1.43s/it]
 29%|██▊       | 1117/3910 [26:58<1:07:01,  1.44s/it]
 29%|██▊       | 1118/3910 [27:00<1:09:40,  1.50s/it]
 29%|██▊       | 1119/3910 [27:01<1:09:15,  1.49s/it]
 29%|██▊       | 1120/3910 [27:02<1:08:33,  1.47s/it]
 29%|██▊       | 1121/3910 [27:04<1:08:26,  1.47s/it]
 29%|██▊       | 1122/3910 [27:05<1:08:36,  1.48s/it]
 29%|██▊       | 1123/3910 [27:07<1:08:31,  1.48s/it]
 29%|██▊       | 1124/3910 [27:08<1:08:24,  1.47s/it]
 29%|██▉       | 1125/3910 [27:10<1:08:26,  1.47s/it]
                                                     

 29%|██▉       | 1125/3910 [27:10<1:08:26,  1.47s/it]
 29%|██▉       | 1126/3910 [27:11<1:06:50,  1.44s/it]
 29%|██▉       | 1127/3910 [27:13<1:07:03,  1.45s/it]
 29%|██▉       | 1128/3910 [27:14<1:06:44,  1.44s/it]
 29%|██▉       | 1129/3910 [27:16<1:06:50,  1.44s/it]
 29%|██▉       | 1130/3910 [27:17<1:06:45,  1.44s/it]
 29%|██▉       | 1131/3910 [27:18<1:06:42,  1.44s/it]
 29%|██▉       | 1132/3910 [27:20<1:06:53,  1.44s/it]
 29%|██▉       | 1133/3910 [27:21<1:06:47,  1.44s/it]
 29%|██▉       | 1134/3910 [27:23<1:06:47,  1.44s/it]
 29%|██▉       | 1135/3910 [27:24<1:06:06,  1.43s/it]
 29%|██▉       | 1136/3910 [27:26<1:07:16,  1.46s/it]
 29%|██▉       | 1137/3910 [27:27<1:07:28,  1.46s/it]
 29%|██▉       | 1138/3910 [27:29<1:06:56,  1.45s/it]
 29%|██▉       | 1139/3910 [27:30<1:07:02,  1.45s/it]
 29%|██▉       | 1140/3910 [27:31<1:05:48,  1.43s/it]
 29%|██▉       | 1141/3910 [27:33<1:06:01,  1.43s/it]
 29%|██▉       | 1142/3910 [27:34<1:06:30,  1.44s/it]
 29%|██▉       | 1143/3910 [27:36<1:05:11,  1.41s/it]
 29%|██▉       | 1144/3910 [27:37<1:05:06,  1.41s/it]
 29%|██▉       | 1145/3910 [27:38<1:05:56,  1.43s/it]
 29%|██▉       | 1146/3910 [27:40<1:05:37,  1.42s/it]
 29%|██▉       | 1147/3910 [27:41<1:04:49,  1.41s/it]
 29%|██▉       | 1148/3910 [27:43<1:04:14,  1.40s/it]
 29%|██▉       | 1149/3910 [27:44<1:05:26,  1.42s/it]
 29%|██▉       | 1150/3910 [27:46<1:05:11,  1.42s/it]
                                                     

 29%|██▉       | 1150/3910 [27:46<1:05:11,  1.42s/it]
 29%|██▉       | 1151/3910 [27:47<1:06:08,  1.44s/it]
 29%|██▉       | 1152/3910 [27:48<1:05:56,  1.43s/it]
 29%|██▉       | 1153/3910 [27:50<1:06:03,  1.44s/it]
 30%|██▉       | 1154/3910 [27:51<1:06:21,  1.44s/it]
 30%|██▉       | 1155/3910 [27:53<1:06:47,  1.45s/it]
 30%|██▉       | 1156/3910 [27:54<1:06:13,  1.44s/it]
 30%|██▉       | 1157/3910 [27:56<1:05:57,  1.44s/it]
 30%|██▉       | 1158/3910 [27:57<1:06:40,  1.45s/it]
 30%|██▉       | 1159/3910 [27:59<1:06:25,  1.45s/it]
 30%|██▉       | 1160/3910 [28:00<1:07:02,  1.46s/it]
 30%|██▉       | 1161/3910 [28:01<1:05:36,  1.43s/it]
 30%|██▉       | 1162/3910 [28:03<1:06:21,  1.45s/it]
 30%|██▉       | 1163/3910 [28:04<1:06:12,  1.45s/it]
 30%|██▉       | 1164/3910 [28:06<1:06:29,  1.45s/it]
 30%|██▉       | 1165/3910 [28:07<1:06:35,  1.46s/it]
 30%|██▉       | 1166/3910 [28:09<1:07:04,  1.47s/it]
 30%|██▉       | 1167/3910 [28:10<1:06:49,  1.46s/it]
 30%|██▉       | 1168/3910 [28:12<1:07:12,  1.47s/it]
 30%|██▉       | 1169/3910 [28:13<1:06:28,  1.46s/it]
 30%|██▉       | 1170/3910 [28:15<1:06:00,  1.45s/it]
 30%|██▉       | 1171/3910 [28:16<1:06:28,  1.46s/it]
 30%|██▉       | 1172/3910 [28:18<1:06:39,  1.46s/it]
 30%|███       | 1173/3910 [28:19<1:04:47,  1.42s/it]
 30%|███       | 1174/3910 [28:20<1:05:27,  1.44s/it]
 30%|███       | 1175/3910 [28:22<1:05:27,  1.44s/it]
                                                     

 30%|███       | 1175/3910 [28:22<1:05:27,  1.44s/it]
 30%|███       | 1176/3910 [28:23<1:04:30,  1.42s/it]
 30%|███       | 1177/3910 [28:25<1:04:36,  1.42s/it]
 30%|███       | 1178/3910 [28:26<1:05:35,  1.44s/it]
 30%|███       | 1179/3910 [28:27<1:04:34,  1.42s/it]
 30%|███       | 1180/3910 [28:29<1:05:25,  1.44s/it]
 30%|███       | 1181/3910 [28:30<1:04:30,  1.42s/it]
 30%|███       | 1182/3910 [28:32<1:05:06,  1.43s/it]
 30%|███       | 1183/3910 [28:33<1:05:49,  1.45s/it]
 30%|███       | 1184/3910 [28:35<1:05:41,  1.45s/it]
 30%|███       | 1185/3910 [28:36<1:05:52,  1.45s/it]
 30%|███       | 1186/3910 [28:38<1:06:07,  1.46s/it]
 30%|███       | 1187/3910 [28:39<1:04:59,  1.43s/it]
 30%|███       | 1188/3910 [28:40<1:05:35,  1.45s/it]
 30%|███       | 1189/3910 [28:42<1:04:37,  1.43s/it]
 30%|███       | 1190/3910 [28:43<1:03:51,  1.41s/it]
 30%|███       | 1191/3910 [28:45<1:03:11,  1.39s/it]
 30%|███       | 1192/3910 [28:46<1:04:11,  1.42s/it]
 31%|███       | 1193/3910 [28:48<1:07:24,  1.49s/it]
 31%|███       | 1194/3910 [28:49<1:06:47,  1.48s/it]
 31%|███       | 1195/3910 [28:51<1:06:25,  1.47s/it]
 31%|███       | 1196/3910 [28:52<1:06:09,  1.46s/it]
 31%|███       | 1197/3910 [28:54<1:06:31,  1.47s/it]
 31%|███       | 1198/3910 [28:55<1:06:30,  1.47s/it]
 31%|███       | 1199/3910 [28:56<1:05:27,  1.45s/it]
 31%|███       | 1200/3910 [28:58<1:04:48,  1.43s/it]
                                                     

 31%|███       | 1200/3910 [28:58<1:04:48,  1.43s/it]
 31%|███       | 1201/3910 [28:59<1:03:34,  1.41s/it]
 31%|███       | 1202/3910 [29:01<1:03:58,  1.42s/it]
 31%|███       | 1203/3910 [29:02<1:04:47,  1.44s/it]
 31%|███       | 1204/3910 [29:03<1:04:26,  1.43s/it]
 31%|███       | 1205/3910 [29:05<1:04:47,  1.44s/it]
 31%|███       | 1206/3910 [29:06<1:05:11,  1.45s/it]
 31%|███       | 1207/3910 [29:08<1:04:11,  1.42s/it]
 31%|███       | 1208/3910 [29:09<1:04:19,  1.43s/it]
 31%|███       | 1209/3910 [29:11<1:03:06,  1.40s/it]
 31%|███       | 1210/3910 [29:12<1:03:40,  1.41s/it]
 31%|███       | 1211/3910 [29:13<1:03:41,  1.42s/it]
 31%|███       | 1212/3910 [29:15<1:04:46,  1.44s/it]
 31%|███       | 1213/3910 [29:16<1:05:12,  1.45s/it]
 31%|███       | 1214/3910 [29:18<1:05:31,  1.46s/it]
 31%|███       | 1215/3910 [29:19<1:05:59,  1.47s/it]
 31%|███       | 1216/3910 [29:21<1:05:11,  1.45s/it]
 31%|███       | 1217/3910 [29:22<1:04:24,  1.44s/it]
 31%|███       | 1218/3910 [29:24<1:04:42,  1.44s/it]
 31%|███       | 1219/3910 [29:25<1:04:00,  1.43s/it]
 31%|███       | 1220/3910 [29:26<1:04:24,  1.44s/it]
 31%|███       | 1221/3910 [29:28<1:03:38,  1.42s/it]
 31%|███▏      | 1222/3910 [29:29<1:04:16,  1.43s/it]
 31%|███▏      | 1223/3910 [29:31<1:04:35,  1.44s/it]
 31%|███▏      | 1224/3910 [29:32<1:04:39,  1.44s/it]
 31%|███▏      | 1225/3910 [29:34<1:04:02,  1.43s/it]
                                                     

 31%|███▏      | 1225/3910 [29:34<1:04:02,  1.43s/it]
 31%|███▏      | 1226/3910 [29:35<1:04:06,  1.43s/it]
 31%|███▏      | 1227/3910 [29:37<1:04:56,  1.45s/it]
 31%|███▏      | 1228/3910 [29:38<1:05:19,  1.46s/it]
 31%|███▏      | 1229/3910 [29:39<1:04:50,  1.45s/it]
 31%|███▏      | 1230/3910 [29:41<1:04:50,  1.45s/it]
 31%|███▏      | 1231/3910 [29:42<1:05:05,  1.46s/it]
 32%|███▏      | 1232/3910 [29:44<1:05:31,  1.47s/it]
 32%|███▏      | 1233/3910 [29:45<1:05:07,  1.46s/it]
 32%|███▏      | 1234/3910 [29:47<1:04:27,  1.45s/it]
 32%|███▏      | 1235/3910 [29:48<1:04:48,  1.45s/it]
 32%|███▏      | 1236/3910 [29:50<1:04:39,  1.45s/it]
 32%|███▏      | 1237/3910 [29:51<1:05:06,  1.46s/it]
 32%|███▏      | 1238/3910 [29:53<1:04:28,  1.45s/it]
 32%|███▏      | 1239/3910 [29:54<1:04:11,  1.44s/it]
 32%|███▏      | 1240/3910 [29:55<1:04:08,  1.44s/it]
 32%|███▏      | 1241/3910 [29:57<1:04:28,  1.45s/it]
 32%|███▏      | 1242/3910 [29:58<1:04:50,  1.46s/it]
 32%|███▏      | 1243/3910 [30:00<1:03:51,  1.44s/it]
 32%|███▏      | 1244/3910 [30:01<1:03:18,  1.42s/it]
 32%|███▏      | 1245/3910 [30:03<1:03:44,  1.44s/it]
 32%|███▏      | 1246/3910 [30:04<1:03:17,  1.43s/it]
 32%|███▏      | 1247/3910 [30:05<1:03:13,  1.42s/it]
 32%|███▏      | 1248/3910 [30:07<1:03:17,  1.43s/it]
 32%|███▏      | 1249/3910 [30:08<1:04:04,  1.44s/it]
 32%|███▏      | 1250/3910 [30:10<1:04:32,  1.46s/it]
                                                     

 32%|███▏      | 1250/3910 [30:10<1:04:32,  1.46s/it]
 32%|███▏      | 1251/3910 [30:11<1:04:18,  1.45s/it]
 32%|███▏      | 1252/3910 [30:13<1:04:26,  1.45s/it]
 32%|███▏      | 1253/3910 [30:14<1:04:17,  1.45s/it]
 32%|███▏      | 1254/3910 [30:16<1:04:46,  1.46s/it]
 32%|███▏      | 1255/3910 [30:17<1:04:16,  1.45s/it]
 32%|███▏      | 1256/3910 [30:19<1:03:45,  1.44s/it]
 32%|███▏      | 1257/3910 [30:20<1:04:23,  1.46s/it]
 32%|███▏      | 1258/3910 [30:21<1:03:50,  1.44s/it]
 32%|███▏      | 1259/3910 [30:23<1:04:21,  1.46s/it]
 32%|███▏      | 1260/3910 [30:24<1:04:28,  1.46s/it]
 32%|███▏      | 1261/3910 [30:26<1:04:10,  1.45s/it]
 32%|███▏      | 1262/3910 [30:27<1:04:15,  1.46s/it]
 32%|███▏      | 1263/3910 [30:29<1:03:55,  1.45s/it]
 32%|███▏      | 1264/3910 [30:30<1:03:02,  1.43s/it]
 32%|███▏      | 1265/3910 [30:32<1:03:37,  1.44s/it]
 32%|███▏      | 1266/3910 [30:33<1:03:37,  1.44s/it]
 32%|███▏      | 1267/3910 [30:35<1:04:01,  1.45s/it]
 32%|███▏      | 1268/3910 [30:36<1:04:01,  1.45s/it]
 32%|███▏      | 1269/3910 [30:37<1:03:37,  1.45s/it]
 32%|███▏      | 1270/3910 [30:39<1:02:44,  1.43s/it]
 33%|███▎      | 1271/3910 [30:40<1:03:34,  1.45s/it]
 33%|███▎      | 1272/3910 [30:42<1:03:42,  1.45s/it]
 33%|███▎      | 1273/3910 [30:43<1:03:40,  1.45s/it]
 33%|███▎      | 1274/3910 [30:45<1:04:00,  1.46s/it]
 33%|███▎      | 1275/3910 [30:46<1:04:03,  1.46s/it]
                                                     

 33%|███▎      | 1275/3910 [30:46<1:04:03,  1.46s/it]
 33%|███▎      | 1276/3910 [30:48<1:04:14,  1.46s/it]
 33%|███▎      | 1277/3910 [30:49<1:04:36,  1.47s/it]
 33%|███▎      | 1278/3910 [30:50<1:03:33,  1.45s/it]
 33%|███▎      | 1279/3910 [30:52<1:03:52,  1.46s/it]
 33%|███▎      | 1280/3910 [30:53<1:04:07,  1.46s/it]
 33%|███▎      | 1281/3910 [30:55<1:04:23,  1.47s/it]
 33%|███▎      | 1282/3910 [30:56<1:04:35,  1.47s/it]
 33%|███▎      | 1283/3910 [30:58<1:04:38,  1.48s/it]
 33%|███▎      | 1284/3910 [30:59<1:03:49,  1.46s/it]
 33%|███▎      | 1285/3910 [31:01<1:03:57,  1.46s/it]
 33%|███▎      | 1286/3910 [31:02<1:03:33,  1.45s/it]
 33%|███▎      | 1287/3910 [31:04<1:04:00,  1.46s/it]
 33%|███▎      | 1288/3910 [31:05<1:04:09,  1.47s/it]
 33%|███▎      | 1289/3910 [31:07<1:03:42,  1.46s/it]
 33%|███▎      | 1290/3910 [31:08<1:03:17,  1.45s/it]
 33%|███▎      | 1291/3910 [31:09<1:02:32,  1.43s/it]
 33%|███▎      | 1292/3910 [31:11<1:02:50,  1.44s/it]
 33%|███▎      | 1293/3910 [31:12<1:02:50,  1.44s/it]
 33%|███▎      | 1294/3910 [31:14<1:02:55,  1.44s/it]
 33%|███▎      | 1295/3910 [31:15<1:02:48,  1.44s/it]
 33%|███▎      | 1296/3910 [31:17<1:01:58,  1.42s/it]
 33%|███▎      | 1297/3910 [31:18<1:02:34,  1.44s/it]
 33%|███▎      | 1298/3910 [31:20<1:03:02,  1.45s/it]
 33%|███▎      | 1299/3910 [31:21<1:03:23,  1.46s/it]
 33%|███▎      | 1300/3910 [31:22<1:03:11,  1.45s/it]
                                                     

 33%|███▎      | 1300/3910 [31:22<1:03:11,  1.45s/it]
 33%|███▎      | 1301/3910 [31:24<1:03:23,  1.46s/it]
 33%|███▎      | 1302/3910 [31:25<1:03:40,  1.46s/it]
 33%|███▎      | 1303/3910 [31:27<1:02:56,  1.45s/it]
 33%|███▎      | 1304/3910 [31:28<1:03:00,  1.45s/it]
 33%|███▎      | 1305/3910 [31:30<1:02:46,  1.45s/it]
 33%|███▎      | 1306/3910 [31:31<1:02:33,  1.44s/it]
 33%|███▎      | 1307/3910 [31:33<1:02:54,  1.45s/it]
 33%|███▎      | 1308/3910 [31:34<1:02:53,  1.45s/it]
 33%|███▎      | 1309/3910 [31:36<1:03:19,  1.46s/it]
 34%|███▎      | 1310/3910 [31:37<1:02:36,  1.44s/it]
 34%|███▎      | 1311/3910 [31:38<1:02:17,  1.44s/it]
 34%|███▎      | 1312/3910 [31:40<1:02:26,  1.44s/it]
 34%|███▎      | 1313/3910 [31:41<1:02:27,  1.44s/it]
 34%|███▎      | 1314/3910 [31:43<1:01:53,  1.43s/it]
 34%|███▎      | 1315/3910 [31:44<1:01:48,  1.43s/it]
 34%|███▎      | 1316/3910 [31:45<1:01:24,  1.42s/it]
 34%|███▎      | 1317/3910 [31:47<1:01:20,  1.42s/it]
 34%|███▎      | 1318/3910 [31:48<1:01:16,  1.42s/it]
 34%|███▎      | 1319/3910 [31:50<1:00:50,  1.41s/it]
 34%|███▍      | 1320/3910 [31:51<1:01:03,  1.41s/it]
 34%|███▍      | 1321/3910 [31:53<1:02:02,  1.44s/it]
 34%|███▍      | 1322/3910 [31:54<1:02:05,  1.44s/it]
 34%|███▍      | 1323/3910 [31:55<1:01:23,  1.42s/it]
 34%|███▍      | 1324/3910 [31:57<1:01:41,  1.43s/it]
 34%|███▍      | 1325/3910 [31:58<1:01:38,  1.43s/it]
                                                     

 34%|███▍      | 1325/3910 [31:58<1:01:38,  1.43s/it]
 34%|███▍      | 1326/3910 [32:00<1:01:26,  1.43s/it]
 34%|███▍      | 1327/3910 [32:01<1:04:40,  1.50s/it]
 34%|███▍      | 1328/3910 [32:03<1:04:14,  1.49s/it]
 34%|███▍      | 1329/3910 [32:04<1:03:48,  1.48s/it]
 34%|███▍      | 1330/3910 [32:06<1:02:54,  1.46s/it]
 34%|███▍      | 1331/3910 [32:07<1:02:41,  1.46s/it]
 34%|███▍      | 1332/3910 [32:09<1:02:36,  1.46s/it]
 34%|███▍      | 1333/3910 [32:10<1:02:02,  1.44s/it]
 34%|███▍      | 1334/3910 [32:12<1:01:42,  1.44s/it]
 34%|███▍      | 1335/3910 [32:13<1:01:16,  1.43s/it]
 34%|███▍      | 1336/3910 [32:14<1:02:03,  1.45s/it]
 34%|███▍      | 1337/3910 [32:16<1:02:31,  1.46s/it]
 34%|███▍      | 1338/3910 [32:17<1:02:51,  1.47s/it]
 34%|███▍      | 1339/3910 [32:19<1:02:23,  1.46s/it]
 34%|███▍      | 1340/3910 [32:20<1:01:57,  1.45s/it]
 34%|███▍      | 1341/3910 [32:22<1:01:56,  1.45s/it]
 34%|███▍      | 1342/3910 [32:23<1:02:09,  1.45s/it]
 34%|███▍      | 1343/3910 [32:25<1:02:05,  1.45s/it]
 34%|███▍      | 1344/3910 [32:26<1:02:40,  1.47s/it]
 34%|███▍      | 1345/3910 [32:28<1:01:53,  1.45s/it]
 34%|███▍      | 1346/3910 [32:29<1:01:24,  1.44s/it]
 34%|███▍      | 1347/3910 [32:30<1:01:37,  1.44s/it]
 34%|███▍      | 1348/3910 [32:32<1:02:12,  1.46s/it]
 35%|███▍      | 1349/3910 [32:33<1:02:22,  1.46s/it]
 35%|███▍      | 1350/3910 [32:35<1:01:40,  1.45s/it]
                                                     

 35%|███▍      | 1350/3910 [32:35<1:01:40,  1.45s/it]
 35%|███▍      | 1351/3910 [32:36<1:02:11,  1.46s/it]
 35%|███▍      | 1352/3910 [32:38<1:01:18,  1.44s/it]
 35%|███▍      | 1353/3910 [32:39<1:01:33,  1.44s/it]
 35%|███▍      | 1354/3910 [32:41<1:01:50,  1.45s/it]
 35%|███▍      | 1355/3910 [32:42<1:01:39,  1.45s/it]
 35%|███▍      | 1356/3910 [32:43<1:01:21,  1.44s/it]
 35%|███▍      | 1357/3910 [32:45<1:01:52,  1.45s/it]
 35%|███▍      | 1358/3910 [32:46<1:01:36,  1.45s/it]
 35%|███▍      | 1359/3910 [32:48<1:01:23,  1.44s/it]
 35%|███▍      | 1360/3910 [32:49<1:01:34,  1.45s/it]
 35%|███▍      | 1361/3910 [32:51<1:01:48,  1.45s/it]
 35%|███▍      | 1362/3910 [32:52<1:01:58,  1.46s/it]
 35%|███▍      | 1363/3910 [32:54<1:01:46,  1.46s/it]
 35%|███▍      | 1364/3910 [32:55<1:01:37,  1.45s/it]
 35%|███▍      | 1365/3910 [32:56<1:01:24,  1.45s/it]
 35%|███▍      | 1366/3910 [32:58<1:01:33,  1.45s/it]
 35%|███▍      | 1367/3910 [32:59<1:01:22,  1.45s/it]
 35%|███▍      | 1368/3910 [33:01<1:00:10,  1.42s/it]
 35%|███▌      | 1369/3910 [33:02<1:00:27,  1.43s/it]
 35%|███▌      | 1370/3910 [33:04<1:01:13,  1.45s/it]
 35%|███▌      | 1371/3910 [33:05<1:01:40,  1.46s/it]
 35%|███▌      | 1372/3910 [33:07<1:01:27,  1.45s/it]
 35%|███▌      | 1373/3910 [33:08<1:01:58,  1.47s/it]
 35%|███▌      | 1374/3910 [33:10<1:02:15,  1.47s/it]
 35%|███▌      | 1375/3910 [33:11<1:02:26,  1.48s/it]
                                                     

 35%|███▌      | 1375/3910 [33:11<1:02:26,  1.48s/it]
 35%|███▌      | 1376/3910 [33:13<1:01:52,  1.47s/it]
 35%|███▌      | 1377/3910 [33:14<1:01:31,  1.46s/it]
 35%|███▌      | 1378/3910 [33:15<1:01:58,  1.47s/it]
 35%|███▌      | 1379/3910 [33:17<1:01:58,  1.47s/it]
 35%|███▌      | 1380/3910 [33:18<1:01:39,  1.46s/it]
 35%|███▌      | 1381/3910 [33:20<1:02:04,  1.47s/it]
 35%|███▌      | 1382/3910 [33:21<1:00:55,  1.45s/it]
 35%|███▌      | 1383/3910 [33:23<1:00:30,  1.44s/it]
 35%|███▌      | 1384/3910 [33:24<1:00:49,  1.44s/it]
 35%|███▌      | 1385/3910 [33:26<1:00:45,  1.44s/it]
 35%|███▌      | 1386/3910 [33:27<1:01:05,  1.45s/it]
 35%|███▌      | 1387/3910 [33:29<1:01:19,  1.46s/it]
 35%|███▌      | 1388/3910 [33:30<1:00:14,  1.43s/it]
 36%|███▌      | 1389/3910 [33:31<1:00:10,  1.43s/it]
 36%|███▌      | 1390/3910 [33:33<59:59,  1.43s/it]  
 36%|███▌      | 1391/3910 [33:34<1:00:07,  1.43s/it]
 36%|███▌      | 1392/3910 [33:36<1:00:48,  1.45s/it]
 36%|███▌      | 1393/3910 [33:37<1:00:45,  1.45s/it]
 36%|███▌      | 1394/3910 [33:39<1:00:36,  1.45s/it]
 36%|███▌      | 1395/3910 [33:40<1:00:40,  1.45s/it]
 36%|███▌      | 1396/3910 [33:42<1:01:16,  1.46s/it]
 36%|███▌      | 1397/3910 [33:43<59:39,  1.42s/it]  
 36%|███▌      | 1398/3910 [33:44<59:54,  1.43s/it]
 36%|███▌      | 1399/3910 [33:46<59:57,  1.43s/it]
 36%|███▌      | 1400/3910 [33:47<1:00:04,  1.44s/it]
                                                     

 36%|███▌      | 1400/3910 [33:47<1:00:04,  1.44s/it]
 36%|███▌      | 1401/3910 [33:49<1:00:33,  1.45s/it]
 36%|███▌      | 1402/3910 [33:50<1:03:21,  1.52s/it]
 36%|███▌      | 1403/3910 [33:52<1:01:58,  1.48s/it]
 36%|███▌      | 1404/3910 [33:53<1:01:46,  1.48s/it]
 36%|███▌      | 1405/3910 [33:55<1:00:55,  1.46s/it]
 36%|███▌      | 1406/3910 [33:56<1:00:19,  1.45s/it]
 36%|███▌      | 1407/3910 [33:57<1:00:39,  1.45s/it]
 36%|███▌      | 1408/3910 [33:59<1:00:50,  1.46s/it]
 36%|███▌      | 1409/3910 [34:00<1:01:00,  1.46s/it]
 36%|███▌      | 1410/3910 [34:02<1:00:41,  1.46s/it]
 36%|███▌      | 1411/3910 [34:03<1:00:27,  1.45s/it]
 36%|███▌      | 1412/3910 [34:05<1:00:42,  1.46s/it]
 36%|███▌      | 1413/3910 [34:06<1:00:10,  1.45s/it]
 36%|███▌      | 1414/3910 [34:08<1:00:31,  1.45s/it]
 36%|███▌      | 1415/3910 [34:09<1:00:38,  1.46s/it]
 36%|███▌      | 1416/3910 [34:11<1:00:26,  1.45s/it]
 36%|███▌      | 1417/3910 [34:12<1:00:10,  1.45s/it]
 36%|███▋      | 1418/3910 [34:13<1:00:22,  1.45s/it]
 36%|███▋      | 1419/3910 [34:15<59:24,  1.43s/it]  
 36%|███▋      | 1420/3910 [34:16<59:18,  1.43s/it]
 36%|███▋      | 1421/3910 [34:18<59:06,  1.42s/it]
 36%|███▋      | 1422/3910 [34:19<59:59,  1.45s/it]
 36%|███▋      | 1423/3910 [34:21<59:31,  1.44s/it]
 36%|███▋      | 1424/3910 [34:22<1:00:04,  1.45s/it]
 36%|███▋      | 1425/3910 [34:24<59:54,  1.45s/it]  
                                                   

 36%|███▋      | 1425/3910 [34:24<59:54,  1.45s/it]
 36%|███▋      | 1426/3910 [34:25<59:42,  1.44s/it]
 36%|███▋      | 1427/3910 [34:26<59:36,  1.44s/it]
 37%|███▋      | 1428/3910 [34:28<59:30,  1.44s/it]
 37%|███▋      | 1429/3910 [34:29<59:02,  1.43s/it]
 37%|███▋      | 1430/3910 [34:31<59:38,  1.44s/it]
 37%|███▋      | 1431/3910 [34:32<59:45,  1.45s/it]
 37%|███▋      | 1432/3910 [34:34<59:50,  1.45s/it]
 37%|███▋      | 1433/3910 [34:35<59:11,  1.43s/it]
 37%|███▋      | 1434/3910 [34:37<1:02:14,  1.51s/it]
 37%|███▋      | 1435/3910 [34:38<1:01:22,  1.49s/it]
 37%|███▋      | 1436/3910 [34:40<1:01:08,  1.48s/it]
 37%|███▋      | 1437/3910 [34:41<1:00:36,  1.47s/it]
 37%|███▋      | 1438/3910 [34:43<1:00:53,  1.48s/it]
 37%|███▋      | 1439/3910 [34:44<59:45,  1.45s/it]  
 37%|███▋      | 1440/3910 [34:45<1:00:16,  1.46s/it]
 37%|███▋      | 1441/3910 [34:47<59:43,  1.45s/it]  
 37%|███▋      | 1442/3910 [34:48<59:06,  1.44s/it]
 37%|███▋      | 1443/3910 [34:50<58:51,  1.43s/it]
 37%|███▋      | 1444/3910 [34:51<58:01,  1.41s/it]
 37%|███▋      | 1445/3910 [34:52<58:21,  1.42s/it]
 37%|███▋      | 1446/3910 [34:54<58:33,  1.43s/it]
 37%|███▋      | 1447/3910 [34:55<58:21,  1.42s/it]
 37%|███▋      | 1448/3910 [34:57<58:39,  1.43s/it]
 37%|███▋      | 1449/3910 [34:58<58:58,  1.44s/it]
 37%|███▋      | 1450/3910 [35:00<59:40,  1.46s/it]
                                                   

 37%|███▋      | 1450/3910 [35:00<59:40,  1.46s/it]
 37%|███▋      | 1451/3910 [35:01<59:27,  1.45s/it]
 37%|███▋      | 1452/3910 [35:03<58:38,  1.43s/it]
 37%|███▋      | 1453/3910 [35:04<58:19,  1.42s/it]
 37%|███▋      | 1454/3910 [35:05<58:10,  1.42s/it]
 37%|███▋      | 1455/3910 [35:07<59:03,  1.44s/it]
 37%|███▋      | 1456/3910 [35:08<59:05,  1.44s/it]
 37%|███▋      | 1457/3910 [35:10<58:33,  1.43s/it]
 37%|███▋      | 1458/3910 [35:11<58:14,  1.43s/it]
 37%|███▋      | 1459/3910 [35:13<59:02,  1.45s/it]
 37%|███▋      | 1460/3910 [35:14<59:15,  1.45s/it]
 37%|███▋      | 1461/3910 [35:16<59:33,  1.46s/it]
 37%|███▋      | 1462/3910 [35:17<59:56,  1.47s/it]
 37%|███▋      | 1463/3910 [35:19<59:32,  1.46s/it]
 37%|███▋      | 1464/3910 [35:20<59:07,  1.45s/it]
 37%|███▋      | 1465/3910 [35:21<59:24,  1.46s/it]
 37%|███▋      | 1466/3910 [35:23<59:36,  1.46s/it]
 38%|███▊      | 1467/3910 [35:24<59:16,  1.46s/it]
 38%|███▊      | 1468/3910 [35:26<59:44,  1.47s/it]
 38%|███▊      | 1469/3910 [35:27<59:47,  1.47s/it]
 38%|███▊      | 1470/3910 [35:29<59:30,  1.46s/it]
 38%|███▊      | 1471/3910 [35:30<59:01,  1.45s/it]
 38%|███▊      | 1472/3910 [35:32<59:22,  1.46s/it]
 38%|███▊      | 1473/3910 [35:33<59:15,  1.46s/it]
 38%|███▊      | 1474/3910 [35:35<59:38,  1.47s/it]
 38%|███▊      | 1475/3910 [35:36<59:22,  1.46s/it]
                                                   

 38%|███▊      | 1475/3910 [35:36<59:22,  1.46s/it]
 38%|███▊      | 1476/3910 [35:38<59:11,  1.46s/it]
 38%|███▊      | 1477/3910 [35:39<59:24,  1.47s/it]
 38%|███▊      | 1478/3910 [35:40<58:23,  1.44s/it]
 38%|███▊      | 1479/3910 [35:42<58:56,  1.45s/it]
 38%|███▊      | 1480/3910 [35:43<59:04,  1.46s/it]
 38%|███▊      | 1481/3910 [35:45<58:23,  1.44s/it]
 38%|███▊      | 1482/3910 [35:46<57:32,  1.42s/it]
 38%|███▊      | 1483/3910 [35:48<58:07,  1.44s/it]
 38%|███▊      | 1484/3910 [35:49<57:52,  1.43s/it]
 38%|███▊      | 1485/3910 [35:50<58:03,  1.44s/it]
 38%|███▊      | 1486/3910 [35:52<58:29,  1.45s/it]
 38%|███▊      | 1487/3910 [35:53<58:59,  1.46s/it]
 38%|███▊      | 1488/3910 [35:55<58:38,  1.45s/it]
 38%|███▊      | 1489/3910 [35:56<58:47,  1.46s/it]
 38%|███▊      | 1490/3910 [35:58<57:40,  1.43s/it]
 38%|███▊      | 1491/3910 [35:59<57:46,  1.43s/it]
 38%|███▊      | 1492/3910 [36:01<57:30,  1.43s/it]
 38%|███▊      | 1493/3910 [36:02<57:51,  1.44s/it]
 38%|███▊      | 1494/3910 [36:03<57:50,  1.44s/it]
 38%|███▊      | 1495/3910 [36:05<57:36,  1.43s/it]
 38%|███▊      | 1496/3910 [36:06<57:30,  1.43s/it]
 38%|███▊      | 1497/3910 [36:08<58:13,  1.45s/it]
 38%|███▊      | 1498/3910 [36:09<58:00,  1.44s/it]
 38%|███▊      | 1499/3910 [36:11<57:34,  1.43s/it]
 38%|███▊      | 1500/3910 [36:12<58:09,  1.45s/it]
                                                   

 38%|███▊      | 1500/3910 [36:12<58:09,  1.45s/it]
 38%|███▊      | 1501/3910 [36:14<58:04,  1.45s/it]
 38%|███▊      | 1502/3910 [36:15<58:41,  1.46s/it]
 38%|███▊      | 1503/3910 [36:16<58:46,  1.47s/it]
 38%|███▊      | 1504/3910 [36:18<58:14,  1.45s/it]
 38%|███▊      | 1505/3910 [36:19<58:38,  1.46s/it]
 39%|███▊      | 1506/3910 [36:21<58:37,  1.46s/it]
 39%|███▊      | 1507/3910 [36:22<58:26,  1.46s/it]
 39%|███▊      | 1508/3910 [36:24<58:56,  1.47s/it]
 39%|███▊      | 1509/3910 [36:25<1:00:19,  1.51s/it]
 39%|███▊      | 1510/3910 [36:27<1:00:04,  1.50s/it]
 39%|███▊      | 1511/3910 [36:28<59:22,  1.48s/it]  
 39%|███▊      | 1512/3910 [36:30<59:28,  1.49s/it]
 39%|███▊      | 1513/3910 [36:31<59:28,  1.49s/it]
 39%|███▊      | 1514/3910 [36:33<1:00:08,  1.51s/it]
 39%|███▊      | 1515/3910 [36:34<59:12,  1.48s/it]  
 39%|███▉      | 1516/3910 [36:36<57:09,  1.43s/it]
 39%|███▉      | 1517/3910 [36:37<57:36,  1.44s/it]
 39%|███▉      | 1518/3910 [36:39<57:42,  1.45s/it]
 39%|███▉      | 1519/3910 [36:40<57:30,  1.44s/it]
 39%|███▉      | 1520/3910 [36:41<57:22,  1.44s/it]
 39%|███▉      | 1521/3910 [36:43<57:02,  1.43s/it]
 39%|███▉      | 1522/3910 [36:44<57:29,  1.44s/it]
 39%|███▉      | 1523/3910 [36:46<57:47,  1.45s/it]
 39%|███▉      | 1524/3910 [36:47<58:06,  1.46s/it]
 39%|███▉      | 1525/3910 [36:49<58:27,  1.47s/it]
                                                   

 39%|███▉      | 1525/3910 [36:49<58:27,  1.47s/it]
 39%|███▉      | 1526/3910 [36:50<58:26,  1.47s/it]
 39%|███▉      | 1527/3910 [36:52<58:27,  1.47s/it]
 39%|███▉      | 1528/3910 [36:53<58:15,  1.47s/it]
 39%|███▉      | 1529/3910 [36:55<58:28,  1.47s/it]
 39%|███▉      | 1530/3910 [36:56<58:37,  1.48s/it]
 39%|███▉      | 1531/3910 [36:58<58:33,  1.48s/it]
 39%|███▉      | 1532/3910 [36:59<57:51,  1.46s/it]
 39%|███▉      | 1533/3910 [37:00<57:43,  1.46s/it]
 39%|███▉      | 1534/3910 [37:02<56:56,  1.44s/it]
 39%|███▉      | 1535/3910 [37:03<57:34,  1.45s/it]
 39%|███▉      | 1536/3910 [37:05<58:02,  1.47s/it]
 39%|███▉      | 1537/3910 [37:06<57:40,  1.46s/it]
 39%|███▉      | 1538/3910 [37:08<56:47,  1.44s/it]
 39%|███▉      | 1539/3910 [37:09<56:21,  1.43s/it]
 39%|███▉      | 1540/3910 [37:11<56:49,  1.44s/it]
 39%|███▉      | 1541/3910 [37:12<57:25,  1.45s/it]
 39%|███▉      | 1542/3910 [37:14<57:31,  1.46s/it]
 39%|███▉      | 1543/3910 [37:15<57:56,  1.47s/it]
 39%|███▉      | 1544/3910 [37:16<57:40,  1.46s/it]
 40%|███▉      | 1545/3910 [37:18<57:08,  1.45s/it]
 40%|███▉      | 1546/3910 [37:19<57:09,  1.45s/it]
 40%|███▉      | 1547/3910 [37:21<57:36,  1.46s/it]
 40%|███▉      | 1548/3910 [37:22<57:36,  1.46s/it]
 40%|███▉      | 1549/3910 [37:24<57:48,  1.47s/it]
 40%|███▉      | 1550/3910 [37:25<58:01,  1.48s/it]
                                                   

 40%|███▉      | 1550/3910 [37:25<58:01,  1.48s/it]
 40%|███▉      | 1551/3910 [37:27<57:16,  1.46s/it]
 40%|███▉      | 1552/3910 [37:28<56:47,  1.45s/it]
 40%|███▉      | 1553/3910 [37:30<56:47,  1.45s/it]
 40%|███▉      | 1554/3910 [37:31<57:03,  1.45s/it]
 40%|███▉      | 1555/3910 [37:32<56:40,  1.44s/it]
 40%|███▉      | 1556/3910 [37:34<57:20,  1.46s/it]
 40%|███▉      | 1557/3910 [37:35<57:41,  1.47s/it]
 40%|███▉      | 1558/3910 [37:37<57:53,  1.48s/it]
 40%|███▉      | 1559/3910 [37:38<56:45,  1.45s/it]
 40%|███▉      | 1560/3910 [37:40<56:26,  1.44s/it]
 40%|███▉      | 1561/3910 [37:41<56:56,  1.45s/it]
 40%|███▉      | 1562/3910 [37:43<57:06,  1.46s/it]
 40%|███▉      | 1563/3910 [37:44<57:32,  1.47s/it]
 40%|████      | 1564/3910 [37:46<57:09,  1.46s/it]
 40%|████      | 1565/3910 [37:47<57:22,  1.47s/it]
 40%|████      | 1566/3910 [37:49<57:07,  1.46s/it]
 40%|████      | 1567/3910 [37:50<56:52,  1.46s/it]
 40%|████      | 1568/3910 [37:51<56:25,  1.45s/it]
 40%|████      | 1569/3910 [37:53<56:24,  1.45s/it]
 40%|████      | 1570/3910 [37:54<56:18,  1.44s/it]
 40%|████      | 1571/3910 [37:56<56:46,  1.46s/it]
 40%|████      | 1572/3910 [37:57<56:32,  1.45s/it]
 40%|████      | 1573/3910 [37:59<57:00,  1.46s/it]
 40%|████      | 1574/3910 [38:00<56:42,  1.46s/it]
 40%|████      | 1575/3910 [38:02<56:28,  1.45s/it]
                                                   

 40%|████      | 1575/3910 [38:02<56:28,  1.45s/it]
 40%|████      | 1576/3910 [38:03<56:38,  1.46s/it]
 40%|████      | 1577/3910 [38:05<56:36,  1.46s/it]
 40%|████      | 1578/3910 [38:06<56:00,  1.44s/it]
 40%|████      | 1579/3910 [38:07<55:44,  1.43s/it]
 40%|████      | 1580/3910 [38:09<56:22,  1.45s/it]
 40%|████      | 1581/3910 [38:10<56:55,  1.47s/it]
 40%|████      | 1582/3910 [38:12<57:03,  1.47s/it]
 40%|████      | 1583/3910 [38:13<56:40,  1.46s/it]
 41%|████      | 1584/3910 [38:15<56:02,  1.45s/it]
 41%|████      | 1585/3910 [38:16<56:08,  1.45s/it]
 41%|████      | 1586/3910 [38:18<55:32,  1.43s/it]
 41%|████      | 1587/3910 [38:19<55:49,  1.44s/it]
 41%|████      | 1588/3910 [38:20<56:03,  1.45s/it]
 41%|████      | 1589/3910 [38:22<56:14,  1.45s/it]
 41%|████      | 1590/3910 [38:23<56:37,  1.46s/it]
 41%|████      | 1591/3910 [38:25<58:04,  1.50s/it]
 41%|████      | 1592/3910 [38:26<57:58,  1.50s/it]
 41%|████      | 1593/3910 [38:28<57:42,  1.49s/it]
 41%|████      | 1594/3910 [38:29<57:35,  1.49s/it]
 41%|████      | 1595/3910 [38:31<56:55,  1.48s/it]
 41%|████      | 1596/3910 [38:32<56:41,  1.47s/it]
 41%|████      | 1597/3910 [38:34<56:33,  1.47s/it]
 41%|████      | 1598/3910 [38:35<55:16,  1.43s/it]
 41%|████      | 1599/3910 [38:37<55:06,  1.43s/it]
 41%|████      | 1600/3910 [38:38<55:11,  1.43s/it]
                                                   

 41%|████      | 1600/3910 [38:38<55:11,  1.43s/it]
 41%|████      | 1601/3910 [38:39<54:11,  1.41s/it]
 41%|████      | 1602/3910 [38:41<53:54,  1.40s/it]
 41%|████      | 1603/3910 [38:42<54:55,  1.43s/it]
 41%|████      | 1604/3910 [38:44<55:21,  1.44s/it]
 41%|████      | 1605/3910 [38:45<55:01,  1.43s/it]
 41%|████      | 1606/3910 [38:47<55:14,  1.44s/it]
 41%|████      | 1607/3910 [38:48<54:57,  1.43s/it]
 41%|████      | 1608/3910 [38:49<55:32,  1.45s/it]
 41%|████      | 1609/3910 [38:51<55:22,  1.44s/it]
 41%|████      | 1610/3910 [38:52<55:36,  1.45s/it]
 41%|████      | 1611/3910 [38:54<54:31,  1.42s/it]
 41%|████      | 1612/3910 [38:55<55:03,  1.44s/it]
 41%|████▏     | 1613/3910 [38:57<54:52,  1.43s/it]
 41%|████▏     | 1614/3910 [38:58<55:22,  1.45s/it]
 41%|████▏     | 1615/3910 [39:00<55:32,  1.45s/it]
 41%|████▏     | 1616/3910 [39:01<55:31,  1.45s/it]
 41%|████▏     | 1617/3910 [39:03<56:00,  1.47s/it]
 41%|████▏     | 1618/3910 [39:04<55:56,  1.46s/it]
 41%|████▏     | 1619/3910 [39:05<55:22,  1.45s/it]
 41%|████▏     | 1620/3910 [39:07<55:18,  1.45s/it]
 41%|████▏     | 1621/3910 [39:08<54:47,  1.44s/it]
 41%|████▏     | 1622/3910 [39:10<54:41,  1.43s/it]
 42%|████▏     | 1623/3910 [39:11<55:03,  1.44s/it]
 42%|████▏     | 1624/3910 [39:13<55:03,  1.45s/it]
 42%|████▏     | 1625/3910 [39:14<55:21,  1.45s/it]
                                                   

 42%|████▏     | 1625/3910 [39:14<55:21,  1.45s/it]
 42%|████▏     | 1626/3910 [39:16<55:30,  1.46s/it]
 42%|████▏     | 1627/3910 [39:17<55:47,  1.47s/it]
 42%|████▏     | 1628/3910 [39:19<56:02,  1.47s/it]
 42%|████▏     | 1629/3910 [39:20<55:59,  1.47s/it]
 42%|████▏     | 1630/3910 [39:21<56:06,  1.48s/it]
 42%|████▏     | 1631/3910 [39:23<55:58,  1.47s/it]
 42%|████▏     | 1632/3910 [39:24<55:10,  1.45s/it]
 42%|████▏     | 1633/3910 [39:26<54:51,  1.45s/it]
 42%|████▏     | 1634/3910 [39:27<54:54,  1.45s/it]
 42%|████▏     | 1635/3910 [39:29<55:19,  1.46s/it]
 42%|████▏     | 1636/3910 [39:30<55:24,  1.46s/it]
 42%|████▏     | 1637/3910 [39:32<54:51,  1.45s/it]
 42%|████▏     | 1638/3910 [39:33<54:41,  1.44s/it]
 42%|████▏     | 1639/3910 [39:35<54:51,  1.45s/it]
 42%|████▏     | 1640/3910 [39:36<55:16,  1.46s/it]
 42%|████▏     | 1641/3910 [39:37<55:42,  1.47s/it]
 42%|████▏     | 1642/3910 [39:39<55:53,  1.48s/it]
 42%|████▏     | 1643/3910 [39:40<55:48,  1.48s/it]
 42%|████▏     | 1644/3910 [39:42<55:53,  1.48s/it]
 42%|████▏     | 1645/3910 [39:43<55:28,  1.47s/it]
 42%|████▏     | 1646/3910 [39:45<54:25,  1.44s/it]
 42%|████▏     | 1647/3910 [39:46<54:53,  1.46s/it]
 42%|████▏     | 1648/3910 [39:48<53:13,  1.41s/it]
 42%|████▏     | 1649/3910 [39:49<53:44,  1.43s/it]
 42%|████▏     | 1650/3910 [39:50<53:55,  1.43s/it]
                                                   

 42%|████▏     | 1650/3910 [39:50<53:55,  1.43s/it]
 42%|████▏     | 1651/3910 [39:52<54:32,  1.45s/it]
 42%|████▏     | 1652/3910 [39:53<55:01,  1.46s/it]
 42%|████▏     | 1653/3910 [39:55<54:58,  1.46s/it]
 42%|████▏     | 1654/3910 [39:56<54:29,  1.45s/it]
 42%|████▏     | 1655/3910 [39:58<54:29,  1.45s/it]
 42%|████▏     | 1656/3910 [39:59<53:28,  1.42s/it]
 42%|████▏     | 1657/3910 [40:01<54:08,  1.44s/it]
 42%|████▏     | 1658/3910 [40:02<54:05,  1.44s/it]
 42%|████▏     | 1659/3910 [40:04<54:40,  1.46s/it]
 42%|████▏     | 1660/3910 [40:05<53:42,  1.43s/it]
 42%|████▏     | 1661/3910 [40:06<54:13,  1.45s/it]
 43%|████▎     | 1662/3910 [40:08<54:08,  1.45s/it]
 43%|████▎     | 1663/3910 [40:09<54:31,  1.46s/it]
 43%|████▎     | 1664/3910 [40:11<54:22,  1.45s/it]
 43%|████▎     | 1665/3910 [40:12<54:10,  1.45s/it]
 43%|████▎     | 1666/3910 [40:14<54:30,  1.46s/it]
 43%|████▎     | 1667/3910 [40:15<54:17,  1.45s/it]
 43%|████▎     | 1668/3910 [40:17<54:07,  1.45s/it]
 43%|████▎     | 1669/3910 [40:18<54:14,  1.45s/it]
 43%|████▎     | 1670/3910 [40:19<54:07,  1.45s/it]
 43%|████▎     | 1671/3910 [40:21<54:22,  1.46s/it]
 43%|████▎     | 1672/3910 [40:22<54:31,  1.46s/it]
 43%|████▎     | 1673/3910 [40:24<54:40,  1.47s/it]
 43%|████▎     | 1674/3910 [40:25<54:06,  1.45s/it]
 43%|████▎     | 1675/3910 [40:27<54:10,  1.45s/it]
                                                   

 43%|████▎     | 1675/3910 [40:27<54:10,  1.45s/it]
 43%|████▎     | 1676/3910 [40:28<54:09,  1.45s/it]
 43%|████▎     | 1677/3910 [40:30<53:45,  1.44s/it]
 43%|████▎     | 1678/3910 [40:31<53:58,  1.45s/it]
 43%|████▎     | 1679/3910 [40:33<54:04,  1.45s/it]
 43%|████▎     | 1680/3910 [40:34<54:00,  1.45s/it]
 43%|████▎     | 1681/3910 [40:36<54:24,  1.46s/it]
 43%|████▎     | 1682/3910 [40:37<54:24,  1.47s/it]
 43%|████▎     | 1683/3910 [40:38<54:06,  1.46s/it]
 43%|████▎     | 1684/3910 [40:40<53:27,  1.44s/it]
 43%|████▎     | 1685/3910 [40:41<53:36,  1.45s/it]
 43%|████▎     | 1686/3910 [40:43<54:04,  1.46s/it]
 43%|████▎     | 1687/3910 [40:44<54:18,  1.47s/it]
 43%|████▎     | 1688/3910 [40:46<53:34,  1.45s/it]
 43%|████▎     | 1689/3910 [40:47<54:04,  1.46s/it]
 43%|████▎     | 1690/3910 [40:49<53:49,  1.45s/it]
 43%|████▎     | 1691/3910 [40:50<54:00,  1.46s/it]
 43%|████▎     | 1692/3910 [40:52<53:47,  1.46s/it]
 43%|████▎     | 1693/3910 [40:53<53:53,  1.46s/it]
 43%|████▎     | 1694/3910 [40:54<54:14,  1.47s/it]
 43%|████▎     | 1695/3910 [40:56<54:07,  1.47s/it]
 43%|████▎     | 1696/3910 [40:57<54:28,  1.48s/it]
 43%|████▎     | 1697/3910 [40:59<53:35,  1.45s/it]
 43%|████▎     | 1698/3910 [41:00<53:47,  1.46s/it]
 43%|████▎     | 1699/3910 [41:02<53:58,  1.46s/it]
 43%|████▎     | 1700/3910 [41:03<54:36,  1.48s/it]
                                                   

 43%|████▎     | 1700/3910 [41:03<54:36,  1.48s/it]
 44%|████▎     | 1701/3910 [41:05<54:30,  1.48s/it]
 44%|████▎     | 1702/3910 [41:06<54:16,  1.47s/it]
 44%|████▎     | 1703/3910 [41:08<53:49,  1.46s/it]
 44%|████▎     | 1704/3910 [41:09<53:48,  1.46s/it]
 44%|████▎     | 1705/3910 [41:11<53:20,  1.45s/it]
 44%|████▎     | 1706/3910 [41:12<53:04,  1.44s/it]
 44%|████▎     | 1707/3910 [41:13<52:50,  1.44s/it]
 44%|████▎     | 1708/3910 [41:15<52:39,  1.43s/it]
 44%|████▎     | 1709/3910 [41:16<53:18,  1.45s/it]
 44%|████▎     | 1710/3910 [41:18<53:32,  1.46s/it]
 44%|████▍     | 1711/3910 [41:19<53:07,  1.45s/it]
 44%|████▍     | 1712/3910 [41:21<53:17,  1.45s/it]
 44%|████▍     | 1713/3910 [41:22<53:27,  1.46s/it]
 44%|████▍     | 1714/3910 [41:24<53:37,  1.47s/it]
 44%|████▍     | 1715/3910 [41:25<53:50,  1.47s/it]
 44%|████▍     | 1716/3910 [41:27<53:28,  1.46s/it]
 44%|████▍     | 1717/3910 [41:28<53:20,  1.46s/it]
 44%|████▍     | 1718/3910 [41:30<53:13,  1.46s/it]
 44%|████▍     | 1719/3910 [41:31<52:51,  1.45s/it]
 44%|████▍     | 1720/3910 [41:32<51:38,  1.41s/it]
 44%|████▍     | 1721/3910 [41:34<51:31,  1.41s/it]
 44%|████▍     | 1722/3910 [41:35<51:13,  1.40s/it]
 44%|████▍     | 1723/3910 [41:37<51:51,  1.42s/it]
 44%|████▍     | 1724/3910 [41:38<52:08,  1.43s/it]
 44%|████▍     | 1725/3910 [41:39<51:52,  1.42s/it]
                                                   

 44%|████▍     | 1725/3910 [41:39<51:52,  1.42s/it]
 44%|████▍     | 1726/3910 [41:41<52:33,  1.44s/it]
 44%|████▍     | 1727/3910 [41:42<53:04,  1.46s/it]
 44%|████▍     | 1728/3910 [41:44<53:18,  1.47s/it]
 44%|████▍     | 1729/3910 [41:45<53:29,  1.47s/it]
 44%|████▍     | 1730/3910 [41:47<53:26,  1.47s/it]
 44%|████▍     | 1731/3910 [41:48<53:09,  1.46s/it]
 44%|████▍     | 1732/3910 [41:50<53:25,  1.47s/it]
 44%|████▍     | 1733/3910 [41:51<53:35,  1.48s/it]
 44%|████▍     | 1734/3910 [41:53<53:33,  1.48s/it]
 44%|████▍     | 1735/3910 [41:54<53:09,  1.47s/it]
 44%|████▍     | 1736/3910 [41:56<52:36,  1.45s/it]
 44%|████▍     | 1737/3910 [41:57<52:25,  1.45s/it]
 44%|████▍     | 1738/3910 [41:58<52:05,  1.44s/it]
 44%|████▍     | 1739/3910 [42:00<52:24,  1.45s/it]
 45%|████▍     | 1740/3910 [42:01<52:05,  1.44s/it]
 45%|████▍     | 1741/3910 [42:03<52:25,  1.45s/it]
 45%|████▍     | 1742/3910 [42:04<51:36,  1.43s/it]
 45%|████▍     | 1743/3910 [42:06<51:29,  1.43s/it]
 45%|████▍     | 1744/3910 [42:07<51:30,  1.43s/it]
 45%|████▍     | 1745/3910 [42:08<51:36,  1.43s/it]
 45%|████▍     | 1746/3910 [42:10<50:52,  1.41s/it]
 45%|████▍     | 1747/3910 [42:11<51:25,  1.43s/it]
 45%|████▍     | 1748/3910 [42:13<51:39,  1.43s/it]
 45%|████▍     | 1749/3910 [42:14<51:40,  1.43s/it]
 45%|████▍     | 1750/3910 [42:16<51:42,  1.44s/it]
                                                   

 45%|████▍     | 1750/3910 [42:16<51:42,  1.44s/it]
 45%|████▍     | 1751/3910 [42:17<52:11,  1.45s/it]
 45%|████▍     | 1752/3910 [42:19<51:53,  1.44s/it]
 45%|████▍     | 1753/3910 [42:20<52:04,  1.45s/it]
 45%|████▍     | 1754/3910 [42:21<52:14,  1.45s/it]
 45%|████▍     | 1755/3910 [42:23<52:06,  1.45s/it]
 45%|████▍     | 1756/3910 [42:24<52:18,  1.46s/it]
 45%|████▍     | 1757/3910 [42:26<52:43,  1.47s/it]
 45%|████▍     | 1758/3910 [42:27<52:56,  1.48s/it]
 45%|████▍     | 1759/3910 [42:29<52:33,  1.47s/it]
 45%|████▌     | 1760/3910 [42:30<52:51,  1.48s/it]
 45%|████▌     | 1761/3910 [42:32<53:05,  1.48s/it]
 45%|████▌     | 1762/3910 [42:33<52:56,  1.48s/it]
 45%|████▌     | 1763/3910 [42:35<52:23,  1.46s/it]
 45%|████▌     | 1764/3910 [42:36<52:28,  1.47s/it]
 45%|████▌     | 1765/3910 [42:38<52:25,  1.47s/it]
 45%|████▌     | 1766/3910 [42:39<52:19,  1.46s/it]
 45%|████▌     | 1767/3910 [42:41<52:24,  1.47s/it]
 45%|████▌     | 1768/3910 [42:42<52:11,  1.46s/it]
 45%|████▌     | 1769/3910 [42:43<52:02,  1.46s/it]
 45%|████▌     | 1770/3910 [42:45<52:09,  1.46s/it]
 45%|████▌     | 1771/3910 [42:46<52:11,  1.46s/it]
 45%|████▌     | 1772/3910 [42:48<51:45,  1.45s/it]
 45%|████▌     | 1773/3910 [42:49<52:11,  1.47s/it]
 45%|████▌     | 1774/3910 [42:51<51:39,  1.45s/it]
 45%|████▌     | 1775/3910 [42:52<52:05,  1.46s/it]
                                                   

 45%|████▌     | 1775/3910 [42:52<52:05,  1.46s/it]
 45%|████▌     | 1776/3910 [42:54<52:27,  1.47s/it]
 45%|████▌     | 1777/3910 [42:55<51:57,  1.46s/it]
 45%|████▌     | 1778/3910 [42:57<51:45,  1.46s/it]
 45%|████▌     | 1779/3910 [42:58<51:22,  1.45s/it]
 46%|████▌     | 1780/3910 [42:59<51:08,  1.44s/it]
 46%|████▌     | 1781/3910 [43:01<50:55,  1.44s/it]
 46%|████▌     | 1782/3910 [43:02<51:16,  1.45s/it]
 46%|████▌     | 1783/3910 [43:04<54:01,  1.52s/it]
 46%|████▌     | 1784/3910 [43:05<52:59,  1.50s/it]
 46%|████▌     | 1785/3910 [43:07<52:25,  1.48s/it]
 46%|████▌     | 1786/3910 [43:08<52:20,  1.48s/it]
 46%|████▌     | 1787/3910 [43:10<52:26,  1.48s/it]
 46%|████▌     | 1788/3910 [43:11<51:04,  1.44s/it]
 46%|████▌     | 1789/3910 [43:13<51:01,  1.44s/it]
 46%|████▌     | 1790/3910 [43:14<50:41,  1.43s/it]
 46%|████▌     | 1791/3910 [43:16<51:15,  1.45s/it]
 46%|████▌     | 1792/3910 [43:17<51:40,  1.46s/it]
 46%|████▌     | 1793/3910 [43:19<51:15,  1.45s/it]
 46%|████▌     | 1794/3910 [43:20<51:24,  1.46s/it]
 46%|████▌     | 1795/3910 [43:21<51:10,  1.45s/it]
 46%|████▌     | 1796/3910 [43:23<51:09,  1.45s/it]
 46%|████▌     | 1797/3910 [43:24<50:54,  1.45s/it]
 46%|████▌     | 1798/3910 [43:26<50:54,  1.45s/it]
 46%|████▌     | 1799/3910 [43:27<51:14,  1.46s/it]
 46%|████▌     | 1800/3910 [43:29<51:25,  1.46s/it]
                                                   

 46%|████▌     | 1800/3910 [43:29<51:25,  1.46s/it]
 46%|████▌     | 1801/3910 [43:30<51:33,  1.47s/it]
 46%|████▌     | 1802/3910 [43:32<51:49,  1.48s/it]
 46%|████▌     | 1803/3910 [43:33<51:59,  1.48s/it]
 46%|████▌     | 1804/3910 [43:35<52:12,  1.49s/it]
 46%|████▌     | 1805/3910 [43:36<52:16,  1.49s/it]
 46%|████▌     | 1806/3910 [43:38<53:38,  1.53s/it]
 46%|████▌     | 1807/3910 [43:39<52:24,  1.50s/it]
 46%|████▌     | 1808/3910 [43:41<52:11,  1.49s/it]
 46%|████▋     | 1809/3910 [43:42<51:22,  1.47s/it]
 46%|████▋     | 1810/3910 [43:44<51:30,  1.47s/it]
 46%|████▋     | 1811/3910 [43:45<51:38,  1.48s/it]
 46%|████▋     | 1812/3910 [43:47<51:29,  1.47s/it]
 46%|████▋     | 1813/3910 [43:48<51:34,  1.48s/it]
 46%|████▋     | 1814/3910 [43:49<50:01,  1.43s/it]
 46%|████▋     | 1815/3910 [43:51<50:05,  1.43s/it]
 46%|████▋     | 1816/3910 [43:52<50:09,  1.44s/it]
 46%|████▋     | 1817/3910 [43:54<50:12,  1.44s/it]
 46%|████▋     | 1818/3910 [43:55<49:43,  1.43s/it]
 47%|████▋     | 1819/3910 [43:57<50:18,  1.44s/it]
 47%|████▋     | 1820/3910 [43:58<50:00,  1.44s/it]
 47%|████▋     | 1821/3910 [43:59<49:58,  1.44s/it]
 47%|████▋     | 1822/3910 [44:01<49:46,  1.43s/it]
 47%|████▋     | 1823/3910 [44:02<49:50,  1.43s/it]
 47%|████▋     | 1824/3910 [44:04<50:06,  1.44s/it]
 47%|████▋     | 1825/3910 [44:05<50:31,  1.45s/it]
                                                   

 47%|████▋     | 1825/3910 [44:05<50:31,  1.45s/it]
 47%|████▋     | 1826/3910 [44:07<50:27,  1.45s/it]
 47%|████▋     | 1827/3910 [44:08<50:36,  1.46s/it]
 47%|████▋     | 1828/3910 [44:10<50:33,  1.46s/it]
 47%|████▋     | 1829/3910 [44:11<50:35,  1.46s/it]
 47%|████▋     | 1830/3910 [44:13<50:36,  1.46s/it]
 47%|████▋     | 1831/3910 [44:14<50:09,  1.45s/it]
 47%|████▋     | 1832/3910 [44:15<50:27,  1.46s/it]
 47%|████▋     | 1833/3910 [44:17<50:30,  1.46s/it]
 47%|████▋     | 1834/3910 [44:18<50:37,  1.46s/it]
 47%|████▋     | 1835/3910 [44:20<49:41,  1.44s/it]
 47%|████▋     | 1836/3910 [44:21<49:51,  1.44s/it]
 47%|████▋     | 1837/3910 [44:23<49:36,  1.44s/it]
 47%|████▋     | 1838/3910 [44:24<49:04,  1.42s/it]
 47%|████▋     | 1839/3910 [44:25<49:04,  1.42s/it]
 47%|████▋     | 1840/3910 [44:27<49:37,  1.44s/it]
 47%|████▋     | 1841/3910 [44:28<49:39,  1.44s/it]
 47%|████▋     | 1842/3910 [44:30<50:00,  1.45s/it]
 47%|████▋     | 1843/3910 [44:31<49:27,  1.44s/it]
 47%|████▋     | 1844/3910 [44:33<49:29,  1.44s/it]
 47%|████▋     | 1845/3910 [44:34<49:58,  1.45s/it]
 47%|████▋     | 1846/3910 [44:36<49:35,  1.44s/it]
 47%|████▋     | 1847/3910 [44:37<49:33,  1.44s/it]
 47%|████▋     | 1848/3910 [44:38<49:16,  1.43s/it]
 47%|████▋     | 1849/3910 [44:40<49:23,  1.44s/it]
 47%|████▋     | 1850/3910 [44:41<50:00,  1.46s/it]
                                                   

 47%|████▋     | 1850/3910 [44:41<50:00,  1.46s/it]
 47%|████▋     | 1851/3910 [44:43<50:26,  1.47s/it]
 47%|████▋     | 1852/3910 [44:44<49:48,  1.45s/it]
 47%|████▋     | 1853/3910 [44:46<49:46,  1.45s/it]
 47%|████▋     | 1854/3910 [44:47<50:08,  1.46s/it]
 47%|████▋     | 1855/3910 [44:49<49:32,  1.45s/it]
 47%|████▋     | 1856/3910 [44:50<50:06,  1.46s/it]
 47%|████▋     | 1857/3910 [44:52<49:45,  1.45s/it]
 48%|████▊     | 1858/3910 [44:53<49:58,  1.46s/it]
 48%|████▊     | 1859/3910 [44:54<49:43,  1.45s/it]
 48%|████▊     | 1860/3910 [44:56<49:25,  1.45s/it]
 48%|████▊     | 1861/3910 [44:57<49:25,  1.45s/it]
 48%|████▊     | 1862/3910 [44:59<49:07,  1.44s/it]
 48%|████▊     | 1863/3910 [45:00<49:35,  1.45s/it]
 48%|████▊     | 1864/3910 [45:02<48:10,  1.41s/it]
 48%|████▊     | 1865/3910 [45:03<48:07,  1.41s/it]
 48%|████▊     | 1866/3910 [45:04<48:53,  1.44s/it]
 48%|████▊     | 1867/3910 [45:06<49:19,  1.45s/it]
 48%|████▊     | 1868/3910 [45:07<49:10,  1.44s/it]
 48%|████▊     | 1869/3910 [45:09<49:34,  1.46s/it]
 48%|████▊     | 1870/3910 [45:10<49:47,  1.46s/it]
 48%|████▊     | 1871/3910 [45:12<49:58,  1.47s/it]
 48%|████▊     | 1872/3910 [45:13<49:42,  1.46s/it]
 48%|████▊     | 1873/3910 [45:15<49:51,  1.47s/it]
 48%|████▊     | 1874/3910 [45:16<49:28,  1.46s/it]
 48%|████▊     | 1875/3910 [45:18<49:46,  1.47s/it]
                                                   

 48%|████▊     | 1875/3910 [45:18<49:46,  1.47s/it]
 48%|████▊     | 1876/3910 [45:19<49:21,  1.46s/it]
 48%|████▊     | 1877/3910 [45:21<48:44,  1.44s/it]
 48%|████▊     | 1878/3910 [45:22<48:42,  1.44s/it]
 48%|████▊     | 1879/3910 [45:24<50:46,  1.50s/it]
 48%|████▊     | 1880/3910 [45:25<50:30,  1.49s/it]
 48%|████▊     | 1881/3910 [45:27<50:32,  1.49s/it]
 48%|████▊     | 1882/3910 [45:28<49:46,  1.47s/it]
 48%|████▊     | 1883/3910 [45:29<48:45,  1.44s/it]
 48%|████▊     | 1884/3910 [45:31<49:05,  1.45s/it]
 48%|████▊     | 1885/3910 [45:32<48:47,  1.45s/it]
 48%|████▊     | 1886/3910 [45:34<49:04,  1.45s/it]
 48%|████▊     | 1887/3910 [45:35<47:55,  1.42s/it]
 48%|████▊     | 1888/3910 [45:36<47:27,  1.41s/it]
 48%|████▊     | 1889/3910 [45:38<47:46,  1.42s/it]
 48%|████▊     | 1890/3910 [45:40<49:36,  1.47s/it]
 48%|████▊     | 1891/3910 [45:41<49:45,  1.48s/it]
 48%|████▊     | 1892/3910 [45:42<49:38,  1.48s/it]
 48%|████▊     | 1893/3910 [45:44<48:49,  1.45s/it]
 48%|████▊     | 1894/3910 [45:45<48:23,  1.44s/it]
 48%|████▊     | 1895/3910 [45:47<47:47,  1.42s/it]
 48%|████▊     | 1896/3910 [45:48<48:02,  1.43s/it]
 49%|████▊     | 1897/3910 [45:50<48:38,  1.45s/it]
 49%|████▊     | 1898/3910 [45:51<49:01,  1.46s/it]
 49%|████▊     | 1899/3910 [45:53<49:05,  1.46s/it]
 49%|████▊     | 1900/3910 [45:54<49:15,  1.47s/it]
                                                   

 49%|████▊     | 1900/3910 [45:54<49:15,  1.47s/it]
 49%|████▊     | 1901/3910 [45:56<49:22,  1.47s/it]
 49%|████▊     | 1902/3910 [45:57<49:09,  1.47s/it]
 49%|████▊     | 1903/3910 [45:58<48:38,  1.45s/it]
 49%|████▊     | 1904/3910 [46:00<48:01,  1.44s/it]
 49%|████▊     | 1905/3910 [46:01<47:37,  1.43s/it]
 49%|████▊     | 1906/3910 [46:03<48:04,  1.44s/it]
 49%|████▉     | 1907/3910 [46:04<48:29,  1.45s/it]
 49%|████▉     | 1908/3910 [46:06<48:19,  1.45s/it]
 49%|████▉     | 1909/3910 [46:07<47:38,  1.43s/it]
 49%|████▉     | 1910/3910 [46:08<47:41,  1.43s/it]
 49%|████▉     | 1911/3910 [46:10<47:34,  1.43s/it]
 49%|████▉     | 1912/3910 [46:11<47:42,  1.43s/it]
 49%|████▉     | 1913/3910 [46:13<47:57,  1.44s/it]
 49%|████▉     | 1914/3910 [46:14<47:07,  1.42s/it]
 49%|████▉     | 1915/3910 [46:16<47:25,  1.43s/it]
 49%|████▉     | 1916/3910 [46:17<47:53,  1.44s/it]
 49%|████▉     | 1917/3910 [46:18<47:15,  1.42s/it]
 49%|████▉     | 1918/3910 [46:20<46:57,  1.41s/it]
 49%|████▉     | 1919/3910 [46:21<46:22,  1.40s/it]
 49%|████▉     | 1920/3910 [46:23<46:12,  1.39s/it]
 49%|████▉     | 1921/3910 [46:24<46:41,  1.41s/it]
 49%|████▉     | 1922/3910 [46:25<47:20,  1.43s/it]
 49%|████▉     | 1923/3910 [46:27<47:03,  1.42s/it]
 49%|████▉     | 1924/3910 [46:28<47:03,  1.42s/it]
 49%|████▉     | 1925/3910 [46:30<47:18,  1.43s/it]
                                                   

 49%|████▉     | 1925/3910 [46:30<47:18,  1.43s/it]
 49%|████▉     | 1926/3910 [46:31<46:29,  1.41s/it]
 49%|████▉     | 1927/3910 [46:33<46:39,  1.41s/it]
 49%|████▉     | 1928/3910 [46:34<47:06,  1.43s/it]
 49%|████▉     | 1929/3910 [46:35<47:32,  1.44s/it]
 49%|████▉     | 1930/3910 [46:37<47:48,  1.45s/it]
 49%|████▉     | 1931/3910 [46:38<48:13,  1.46s/it]
 49%|████▉     | 1932/3910 [46:40<48:06,  1.46s/it]
 49%|████▉     | 1933/3910 [46:41<47:36,  1.44s/it]
 49%|████▉     | 1934/3910 [46:43<47:28,  1.44s/it]
 49%|████▉     | 1935/3910 [46:44<46:56,  1.43s/it]
 50%|████▉     | 1936/3910 [46:45<46:25,  1.41s/it]
 50%|████▉     | 1937/3910 [46:47<47:00,  1.43s/it]
 50%|████▉     | 1938/3910 [46:48<47:38,  1.45s/it]
 50%|████▉     | 1939/3910 [46:50<47:58,  1.46s/it]
 50%|████▉     | 1940/3910 [46:51<48:00,  1.46s/it]
 50%|████▉     | 1941/3910 [46:53<47:46,  1.46s/it]
 50%|████▉     | 1942/3910 [46:54<47:42,  1.45s/it]
 50%|████▉     | 1943/3910 [46:56<47:57,  1.46s/it]
 50%|████▉     | 1944/3910 [46:57<48:11,  1.47s/it]
 50%|████▉     | 1945/3910 [46:59<47:42,  1.46s/it]
 50%|████▉     | 1946/3910 [47:00<47:33,  1.45s/it]
 50%|████▉     | 1947/3910 [47:02<47:21,  1.45s/it]
 50%|████▉     | 1948/3910 [47:03<47:18,  1.45s/it]
 50%|████▉     | 1949/3910 [47:04<47:27,  1.45s/it]
 50%|████▉     | 1950/3910 [47:06<47:45,  1.46s/it]
                                                   

 50%|████▉     | 1950/3910 [47:06<47:45,  1.46s/it]
 50%|████▉     | 1951/3910 [47:07<47:39,  1.46s/it]
 50%|████▉     | 1952/3910 [47:09<47:27,  1.45s/it]
 50%|████▉     | 1953/3910 [47:10<47:50,  1.47s/it]
 50%|████▉     | 1954/3910 [47:12<47:30,  1.46s/it]
 50%|█████     | 1955/3910 [47:13<47:35,  1.46s/it]
 50%|█████     | 1956/3910 [47:15<47:16,  1.45s/it]
 50%|█████     | 1957/3910 [47:16<47:11,  1.45s/it]
 50%|█████     | 1958/3910 [47:18<46:51,  1.44s/it]
 50%|█████     | 1959/3910 [47:19<46:33,  1.43s/it]
 50%|█████     | 1960/3910 [47:20<46:34,  1.43s/it]
 50%|█████     | 1961/3910 [47:22<45:35,  1.40s/it]
 50%|█████     | 1962/3910 [47:23<46:24,  1.43s/it]
 50%|█████     | 1963/3910 [47:25<46:43,  1.44s/it]
 50%|█████     | 1964/3910 [47:26<47:03,  1.45s/it]
 50%|█████     | 1965/3910 [47:28<48:59,  1.51s/it]
 50%|█████     | 1966/3910 [47:29<48:31,  1.50s/it]
 50%|█████     | 1967/3910 [47:31<48:13,  1.49s/it]
 50%|█████     | 1968/3910 [47:32<47:40,  1.47s/it]
 50%|█████     | 1969/3910 [47:34<47:13,  1.46s/it]
 50%|█████     | 1970/3910 [47:35<47:18,  1.46s/it]
 50%|█████     | 1971/3910 [47:37<47:32,  1.47s/it]
 50%|█████     | 1972/3910 [47:38<47:16,  1.46s/it]
 50%|█████     | 1973/3910 [47:39<47:17,  1.46s/it]
 50%|█████     | 1974/3910 [47:41<47:31,  1.47s/it]
 51%|█████     | 1975/3910 [47:42<46:53,  1.45s/it]
                                                   

 51%|█████     | 1975/3910 [47:42<46:53,  1.45s/it]
 51%|█████     | 1976/3910 [47:44<46:40,  1.45s/it]
 51%|█████     | 1977/3910 [47:45<46:43,  1.45s/it]
 51%|█████     | 1978/3910 [47:47<46:43,  1.45s/it]
 51%|█████     | 1979/3910 [47:48<46:20,  1.44s/it]
 51%|█████     | 1980/3910 [47:50<46:23,  1.44s/it]
 51%|█████     | 1981/3910 [47:51<46:49,  1.46s/it]
 51%|█████     | 1982/3910 [47:53<47:06,  1.47s/it]
 51%|█████     | 1983/3910 [47:54<47:11,  1.47s/it]
 51%|█████     | 1984/3910 [47:56<47:09,  1.47s/it]
 51%|█████     | 1985/3910 [47:57<46:46,  1.46s/it]
 51%|█████     | 1986/3910 [47:58<46:30,  1.45s/it]
 51%|█████     | 1987/3910 [48:00<46:48,  1.46s/it]
 51%|█████     | 1988/3910 [48:01<46:01,  1.44s/it]
 51%|█████     | 1989/3910 [48:03<46:07,  1.44s/it]
 51%|█████     | 1990/3910 [48:04<46:35,  1.46s/it]
 51%|█████     | 1991/3910 [48:06<46:14,  1.45s/it]
 51%|█████     | 1992/3910 [48:07<46:22,  1.45s/it]
 51%|█████     | 1993/3910 [48:09<46:42,  1.46s/it]
 51%|█████     | 1994/3910 [48:10<46:25,  1.45s/it]
 51%|█████     | 1995/3910 [48:11<46:35,  1.46s/it]
 51%|█████     | 1996/3910 [48:13<46:35,  1.46s/it]
 51%|█████     | 1997/3910 [48:14<46:30,  1.46s/it]
 51%|█████     | 1998/3910 [48:16<46:38,  1.46s/it]
 51%|█████     | 1999/3910 [48:17<46:51,  1.47s/it]
 51%|█████     | 2000/3910 [48:19<46:35,  1.46s/it]
                                                   

 51%|█████     | 2000/3910 [48:19<46:35,  1.46s/it]
 51%|█████     | 2001/3910 [48:20<46:37,  1.47s/it]
 51%|█████     | 2002/3910 [48:22<46:05,  1.45s/it]
 51%|█████     | 2003/3910 [48:23<45:39,  1.44s/it]
 51%|█████▏    | 2004/3910 [48:25<45:27,  1.43s/it]
 51%|█████▏    | 2005/3910 [48:26<45:23,  1.43s/it]
 51%|█████▏    | 2006/3910 [48:27<45:59,  1.45s/it]
 51%|█████▏    | 2007/3910 [48:29<45:52,  1.45s/it]
 51%|█████▏    | 2008/3910 [48:30<46:01,  1.45s/it]
 51%|█████▏    | 2009/3910 [48:32<45:51,  1.45s/it]
 51%|█████▏    | 2010/3910 [48:33<45:16,  1.43s/it]
 51%|█████▏    | 2011/3910 [48:35<45:06,  1.43s/it]
 51%|█████▏    | 2012/3910 [48:36<45:15,  1.43s/it]
 51%|█████▏    | 2013/3910 [48:37<45:36,  1.44s/it]
 52%|█████▏    | 2014/3910 [48:39<45:29,  1.44s/it]
 52%|█████▏    | 2015/3910 [48:40<45:08,  1.43s/it]
 52%|█████▏    | 2016/3910 [48:42<45:32,  1.44s/it]
 52%|█████▏    | 2017/3910 [48:43<45:47,  1.45s/it]
 52%|█████▏    | 2018/3910 [48:45<45:36,  1.45s/it]
 52%|█████▏    | 2019/3910 [48:46<46:00,  1.46s/it]
 52%|█████▏    | 2020/3910 [48:48<46:09,  1.47s/it]
 52%|█████▏    | 2021/3910 [48:49<46:25,  1.47s/it]
 52%|█████▏    | 2022/3910 [48:51<45:56,  1.46s/it]
 52%|█████▏    | 2023/3910 [48:52<45:36,  1.45s/it]
 52%|█████▏    | 2024/3910 [48:53<45:41,  1.45s/it]
 52%|█████▏    | 2025/3910 [48:55<45:27,  1.45s/it]
                                                   

 52%|█████▏    | 2025/3910 [48:55<45:27,  1.45s/it]
 52%|█████▏    | 2026/3910 [48:56<45:14,  1.44s/it]
 52%|█████▏    | 2027/3910 [48:58<45:32,  1.45s/it]
 52%|█████▏    | 2028/3910 [48:59<45:22,  1.45s/it]
 52%|█████▏    | 2029/3910 [49:01<45:45,  1.46s/it]
 52%|█████▏    | 2030/3910 [49:02<46:01,  1.47s/it]
 52%|█████▏    | 2031/3910 [49:04<46:15,  1.48s/it]
 52%|█████▏    | 2032/3910 [49:05<45:51,  1.47s/it]
 52%|█████▏    | 2033/3910 [49:07<45:44,  1.46s/it]
 52%|█████▏    | 2034/3910 [49:08<45:20,  1.45s/it]
 52%|█████▏    | 2035/3910 [49:10<45:24,  1.45s/it]
 52%|█████▏    | 2036/3910 [49:11<45:38,  1.46s/it]
 52%|█████▏    | 2037/3910 [49:12<45:58,  1.47s/it]
 52%|█████▏    | 2038/3910 [49:14<45:20,  1.45s/it]
 52%|█████▏    | 2039/3910 [49:15<45:35,  1.46s/it]
 52%|█████▏    | 2040/3910 [49:17<44:22,  1.42s/it]
 52%|█████▏    | 2041/3910 [49:18<44:49,  1.44s/it]
 52%|█████▏    | 2042/3910 [49:20<44:37,  1.43s/it]
 52%|█████▏    | 2043/3910 [49:21<45:01,  1.45s/it]
 52%|█████▏    | 2044/3910 [49:23<44:51,  1.44s/it]
 52%|█████▏    | 2045/3910 [49:24<44:49,  1.44s/it]
 52%|█████▏    | 2046/3910 [49:25<45:09,  1.45s/it]
 52%|█████▏    | 2047/3910 [49:27<44:27,  1.43s/it]
 52%|█████▏    | 2048/3910 [49:28<44:22,  1.43s/it]
 52%|█████▏    | 2049/3910 [49:30<44:32,  1.44s/it]
 52%|█████▏    | 2050/3910 [49:31<44:39,  1.44s/it]
                                                   

 52%|█████▏    | 2050/3910 [49:31<44:39,  1.44s/it]
 52%|█████▏    | 2051/3910 [49:33<45:07,  1.46s/it]
 52%|█████▏    | 2052/3910 [49:34<45:26,  1.47s/it]
 53%|█████▎    | 2053/3910 [49:36<44:45,  1.45s/it]
 53%|█████▎    | 2054/3910 [49:37<44:18,  1.43s/it]
 53%|█████▎    | 2055/3910 [49:38<43:54,  1.42s/it]
 53%|█████▎    | 2056/3910 [49:40<44:31,  1.44s/it]
 53%|█████▎    | 2057/3910 [49:41<44:39,  1.45s/it]
 53%|█████▎    | 2058/3910 [49:43<44:58,  1.46s/it]
 53%|█████▎    | 2059/3910 [49:44<44:51,  1.45s/it]
 53%|█████▎    | 2060/3910 [49:46<44:43,  1.45s/it]
 53%|█████▎    | 2061/3910 [49:47<44:37,  1.45s/it]
 53%|█████▎    | 2062/3910 [49:49<44:53,  1.46s/it]
 53%|█████▎    | 2063/3910 [49:50<45:00,  1.46s/it]
 53%|█████▎    | 2064/3910 [49:51<44:36,  1.45s/it]
 53%|█████▎    | 2065/3910 [49:53<45:01,  1.46s/it]
 53%|█████▎    | 2066/3910 [49:54<44:54,  1.46s/it]
 53%|█████▎    | 2067/3910 [49:56<45:11,  1.47s/it]
 53%|█████▎    | 2068/3910 [49:57<44:44,  1.46s/it]
 53%|█████▎    | 2069/3910 [49:59<44:16,  1.44s/it]
 53%|█████▎    | 2070/3910 [50:00<44:01,  1.44s/it]
 53%|█████▎    | 2071/3910 [50:02<46:06,  1.50s/it]
 53%|█████▎    | 2072/3910 [50:03<45:45,  1.49s/it]
 53%|█████▎    | 2073/3910 [50:05<45:49,  1.50s/it]
 53%|█████▎    | 2074/3910 [50:06<45:32,  1.49s/it]
 53%|█████▎    | 2075/3910 [50:08<45:05,  1.47s/it]
                                                   

 53%|█████▎    | 2075/3910 [50:08<45:05,  1.47s/it]
 53%|█████▎    | 2076/3910 [50:09<44:50,  1.47s/it]
 53%|█████▎    | 2077/3910 [50:11<44:24,  1.45s/it]
 53%|█████▎    | 2078/3910 [50:12<44:21,  1.45s/it]
 53%|█████▎    | 2079/3910 [50:13<44:04,  1.44s/it]
 53%|█████▎    | 2080/3910 [50:15<44:24,  1.46s/it]
 53%|█████▎    | 2081/3910 [50:16<43:48,  1.44s/it]
 53%|█████▎    | 2082/3910 [50:18<43:48,  1.44s/it]
 53%|█████▎    | 2083/3910 [50:19<43:46,  1.44s/it]
 53%|█████▎    | 2084/3910 [50:21<43:44,  1.44s/it]
 53%|█████▎    | 2085/3910 [50:22<43:51,  1.44s/it]
 53%|█████▎    | 2086/3910 [50:24<44:07,  1.45s/it]
 53%|█████▎    | 2087/3910 [50:25<44:21,  1.46s/it]
 53%|█████▎    | 2088/3910 [50:27<44:36,  1.47s/it]
 53%|█████▎    | 2089/3910 [50:28<44:18,  1.46s/it]
 53%|█████▎    | 2090/3910 [50:29<44:21,  1.46s/it]
 53%|█████▎    | 2091/3910 [50:31<44:06,  1.45s/it]
 54%|█████▎    | 2092/3910 [50:32<44:21,  1.46s/it]
 54%|█████▎    | 2093/3910 [50:34<44:37,  1.47s/it]
 54%|█████▎    | 2094/3910 [50:35<44:14,  1.46s/it]
 54%|█████▎    | 2095/3910 [50:37<44:29,  1.47s/it]
 54%|█████▎    | 2096/3910 [50:38<44:37,  1.48s/it]
 54%|█████▎    | 2097/3910 [50:40<44:25,  1.47s/it]
 54%|█████▎    | 2098/3910 [50:41<44:13,  1.46s/it]
 54%|█████▎    | 2099/3910 [50:43<44:06,  1.46s/it]
 54%|█████▎    | 2100/3910 [50:44<44:11,  1.47s/it]
                                                   

 54%|█████▎    | 2100/3910 [50:44<44:11,  1.47s/it]
 54%|█████▎    | 2101/3910 [50:45<43:14,  1.43s/it]
 54%|█████▍    | 2102/3910 [50:47<43:43,  1.45s/it]
 54%|█████▍    | 2103/3910 [50:48<44:04,  1.46s/it]
 54%|█████▍    | 2104/3910 [50:50<43:15,  1.44s/it]
 54%|█████▍    | 2105/3910 [50:51<43:01,  1.43s/it]
 54%|█████▍    | 2106/3910 [50:53<43:22,  1.44s/it]
 54%|█████▍    | 2107/3910 [50:54<43:37,  1.45s/it]
 54%|█████▍    | 2108/3910 [50:56<43:49,  1.46s/it]
 54%|█████▍    | 2109/3910 [50:57<44:02,  1.47s/it]
 54%|█████▍    | 2110/3910 [50:59<43:39,  1.46s/it]
 54%|█████▍    | 2111/3910 [51:00<43:29,  1.45s/it]
 54%|█████▍    | 2112/3910 [51:01<42:25,  1.42s/it]
 54%|█████▍    | 2113/3910 [51:03<43:00,  1.44s/it]
 54%|█████▍    | 2114/3910 [51:04<43:20,  1.45s/it]
 54%|█████▍    | 2115/3910 [51:06<43:06,  1.44s/it]
 54%|█████▍    | 2116/3910 [51:07<43:06,  1.44s/it]
 54%|█████▍    | 2117/3910 [51:09<42:59,  1.44s/it]
 54%|█████▍    | 2118/3910 [51:10<42:32,  1.42s/it]
 54%|█████▍    | 2119/3910 [51:11<42:57,  1.44s/it]
 54%|█████▍    | 2120/3910 [51:13<42:55,  1.44s/it]
 54%|█████▍    | 2121/3910 [51:14<42:47,  1.44s/it]
 54%|█████▍    | 2122/3910 [51:16<43:13,  1.45s/it]
 54%|█████▍    | 2123/3910 [51:17<43:13,  1.45s/it]
 54%|█████▍    | 2124/3910 [51:19<43:09,  1.45s/it]
 54%|█████▍    | 2125/3910 [51:20<43:19,  1.46s/it]
                                                   

 54%|█████▍    | 2125/3910 [51:20<43:19,  1.46s/it]
 54%|█████▍    | 2126/3910 [51:22<43:23,  1.46s/it]
 54%|█████▍    | 2127/3910 [51:23<43:09,  1.45s/it]
 54%|█████▍    | 2128/3910 [51:25<43:12,  1.45s/it]
 54%|█████▍    | 2129/3910 [51:26<43:21,  1.46s/it]
 54%|█████▍    | 2130/3910 [51:27<43:04,  1.45s/it]
 55%|█████▍    | 2131/3910 [51:29<43:26,  1.47s/it]
 55%|█████▍    | 2132/3910 [51:30<43:12,  1.46s/it]
 55%|█████▍    | 2133/3910 [51:32<42:52,  1.45s/it]
 55%|█████▍    | 2134/3910 [51:33<42:27,  1.43s/it]
 55%|█████▍    | 2135/3910 [51:35<42:27,  1.44s/it]
 55%|█████▍    | 2136/3910 [51:36<42:47,  1.45s/it]
 55%|█████▍    | 2137/3910 [51:38<42:57,  1.45s/it]
 55%|█████▍    | 2138/3910 [51:39<43:09,  1.46s/it]
 55%|█████▍    | 2139/3910 [51:41<43:08,  1.46s/it]
 55%|█████▍    | 2140/3910 [51:42<43:21,  1.47s/it]
 55%|█████▍    | 2141/3910 [51:44<43:35,  1.48s/it]
 55%|█████▍    | 2142/3910 [51:45<43:13,  1.47s/it]
 55%|█████▍    | 2143/3910 [51:46<43:26,  1.48s/it]
 55%|█████▍    | 2144/3910 [51:48<42:49,  1.46s/it]
 55%|█████▍    | 2145/3910 [51:49<42:00,  1.43s/it]
 55%|█████▍    | 2146/3910 [51:51<41:55,  1.43s/it]
 55%|█████▍    | 2147/3910 [51:52<42:01,  1.43s/it]
 55%|█████▍    | 2148/3910 [51:54<42:30,  1.45s/it]
 55%|█████▍    | 2149/3910 [51:55<42:53,  1.46s/it]
 55%|█████▍    | 2150/3910 [51:57<42:46,  1.46s/it]
                                                   

 55%|█████▍    | 2150/3910 [51:57<42:46,  1.46s/it]
 55%|█████▌    | 2151/3910 [51:58<42:15,  1.44s/it]
 55%|█████▌    | 2152/3910 [51:59<42:14,  1.44s/it]
 55%|█████▌    | 2153/3910 [52:01<42:28,  1.45s/it]
 55%|█████▌    | 2154/3910 [52:02<42:52,  1.47s/it]
 55%|█████▌    | 2155/3910 [52:04<42:32,  1.45s/it]
 55%|█████▌    | 2156/3910 [52:05<42:42,  1.46s/it]
 55%|█████▌    | 2157/3910 [52:07<44:28,  1.52s/it]
 55%|█████▌    | 2158/3910 [52:08<43:46,  1.50s/it]
 55%|█████▌    | 2159/3910 [52:10<43:04,  1.48s/it]
 55%|█████▌    | 2160/3910 [52:11<42:49,  1.47s/it]
 55%|█████▌    | 2161/3910 [52:13<43:00,  1.48s/it]
 55%|█████▌    | 2162/3910 [52:14<43:06,  1.48s/it]
 55%|█████▌    | 2163/3910 [52:16<42:29,  1.46s/it]
 55%|█████▌    | 2164/3910 [52:17<42:43,  1.47s/it]
 55%|█████▌    | 2165/3910 [52:19<42:11,  1.45s/it]
 55%|█████▌    | 2166/3910 [52:20<42:12,  1.45s/it]
 55%|█████▌    | 2167/3910 [52:21<42:32,  1.46s/it]
 55%|█████▌    | 2168/3910 [52:23<42:06,  1.45s/it]
 55%|█████▌    | 2169/3910 [52:24<42:00,  1.45s/it]
 55%|█████▌    | 2170/3910 [52:26<41:49,  1.44s/it]
 56%|█████▌    | 2171/3910 [52:27<41:49,  1.44s/it]
 56%|█████▌    | 2172/3910 [52:29<41:56,  1.45s/it]
 56%|█████▌    | 2173/3910 [52:30<41:37,  1.44s/it]
 56%|█████▌    | 2174/3910 [52:32<41:59,  1.45s/it]
 56%|█████▌    | 2175/3910 [52:33<41:11,  1.42s/it]
                                                   

 56%|█████▌    | 2175/3910 [52:33<41:11,  1.42s/it]
 56%|█████▌    | 2176/3910 [52:34<41:25,  1.43s/it]
 56%|█████▌    | 2177/3910 [52:36<43:43,  1.51s/it]
 56%|█████▌    | 2178/3910 [52:38<43:06,  1.49s/it]
 56%|█████▌    | 2179/3910 [52:39<42:34,  1.48s/it]
 56%|█████▌    | 2180/3910 [52:40<42:04,  1.46s/it]
 56%|█████▌    | 2181/3910 [52:42<40:44,  1.41s/it]
 56%|█████▌    | 2182/3910 [52:43<41:20,  1.44s/it]
 56%|█████▌    | 2183/3910 [52:45<41:49,  1.45s/it]
 56%|█████▌    | 2184/3910 [52:46<41:43,  1.45s/it]
 56%|█████▌    | 2185/3910 [52:48<41:30,  1.44s/it]
 56%|█████▌    | 2186/3910 [52:49<41:55,  1.46s/it]
 56%|█████▌    | 2187/3910 [52:50<41:40,  1.45s/it]
 56%|█████▌    | 2188/3910 [52:52<41:35,  1.45s/it]
 56%|█████▌    | 2189/3910 [52:53<41:25,  1.44s/it]
 56%|█████▌    | 2190/3910 [52:55<41:50,  1.46s/it]
 56%|█████▌    | 2191/3910 [52:56<42:07,  1.47s/it]
 56%|█████▌    | 2192/3910 [52:58<42:13,  1.47s/it]
 56%|█████▌    | 2193/3910 [52:59<42:13,  1.48s/it]
 56%|█████▌    | 2194/3910 [53:01<42:21,  1.48s/it]
 56%|█████▌    | 2195/3910 [53:02<41:34,  1.45s/it]
 56%|█████▌    | 2196/3910 [53:04<41:32,  1.45s/it]
 56%|█████▌    | 2197/3910 [53:05<41:02,  1.44s/it]
 56%|█████▌    | 2198/3910 [53:07<41:15,  1.45s/it]
 56%|█████▌    | 2199/3910 [53:08<41:23,  1.45s/it]
 56%|█████▋    | 2200/3910 [53:09<41:30,  1.46s/it]
                                                   

 56%|█████▋    | 2200/3910 [53:09<41:30,  1.46s/it]
 56%|█████▋    | 2201/3910 [53:11<41:09,  1.44s/it]
 56%|█████▋    | 2202/3910 [53:12<40:40,  1.43s/it]
 56%|█████▋    | 2203/3910 [53:14<41:11,  1.45s/it]
 56%|█████▋    | 2204/3910 [53:15<41:16,  1.45s/it]
 56%|█████▋    | 2205/3910 [53:17<41:04,  1.45s/it]
 56%|█████▋    | 2206/3910 [53:18<41:01,  1.44s/it]
 56%|█████▋    | 2207/3910 [53:20<41:18,  1.46s/it]
 56%|█████▋    | 2208/3910 [53:21<41:36,  1.47s/it]
 56%|█████▋    | 2209/3910 [53:23<41:19,  1.46s/it]
 57%|█████▋    | 2210/3910 [53:24<41:22,  1.46s/it]
 57%|█████▋    | 2211/3910 [53:25<41:37,  1.47s/it]
 57%|█████▋    | 2212/3910 [53:27<40:42,  1.44s/it]
 57%|█████▋    | 2213/3910 [53:28<40:55,  1.45s/it]
 57%|█████▋    | 2214/3910 [53:30<41:15,  1.46s/it]
 57%|█████▋    | 2215/3910 [53:31<40:57,  1.45s/it]
 57%|█████▋    | 2216/3910 [53:33<41:10,  1.46s/it]
 57%|█████▋    | 2217/3910 [53:34<41:17,  1.46s/it]
 57%|█████▋    | 2218/3910 [53:36<41:09,  1.46s/it]
 57%|█████▋    | 2219/3910 [53:37<41:15,  1.46s/it]
 57%|█████▋    | 2220/3910 [53:38<40:47,  1.45s/it]
 57%|█████▋    | 2221/3910 [53:40<40:39,  1.44s/it]
 57%|█████▋    | 2222/3910 [53:41<40:52,  1.45s/it]
 57%|█████▋    | 2223/3910 [53:43<41:06,  1.46s/it]
 57%|█████▋    | 2224/3910 [53:44<41:16,  1.47s/it]
 57%|█████▋    | 2225/3910 [53:46<40:53,  1.46s/it]
                                                   

 57%|█████▋    | 2225/3910 [53:46<40:53,  1.46s/it]
 57%|█████▋    | 2226/3910 [53:47<40:27,  1.44s/it]
 57%|█████▋    | 2227/3910 [53:49<40:33,  1.45s/it]
 57%|█████▋    | 2228/3910 [53:50<40:30,  1.44s/it]
 57%|█████▋    | 2229/3910 [53:52<40:36,  1.45s/it]
 57%|█████▋    | 2230/3910 [53:53<40:26,  1.44s/it]
 57%|█████▋    | 2231/3910 [53:54<40:20,  1.44s/it]
 57%|█████▋    | 2232/3910 [53:56<40:16,  1.44s/it]
 57%|█████▋    | 2233/3910 [53:57<40:03,  1.43s/it]
 57%|█████▋    | 2234/3910 [53:59<40:23,  1.45s/it]
 57%|█████▋    | 2235/3910 [54:00<40:13,  1.44s/it]
 57%|█████▋    | 2236/3910 [54:02<40:06,  1.44s/it]
 57%|█████▋    | 2237/3910 [54:03<39:59,  1.43s/it]
 57%|█████▋    | 2238/3910 [54:05<40:18,  1.45s/it]
 57%|█████▋    | 2239/3910 [54:06<40:17,  1.45s/it]
 57%|█████▋    | 2240/3910 [54:07<39:54,  1.43s/it]
 57%|█████▋    | 2241/3910 [54:09<40:04,  1.44s/it]
 57%|█████▋    | 2242/3910 [54:10<40:05,  1.44s/it]
 57%|█████▋    | 2243/3910 [54:12<40:06,  1.44s/it]
 57%|█████▋    | 2244/3910 [54:13<40:31,  1.46s/it]
 57%|█████▋    | 2245/3910 [54:15<40:15,  1.45s/it]
 57%|█████▋    | 2246/3910 [54:16<40:10,  1.45s/it]
 57%|█████▋    | 2247/3910 [54:18<40:26,  1.46s/it]
 57%|█████▋    | 2248/3910 [54:19<40:26,  1.46s/it]
 58%|█████▊    | 2249/3910 [54:20<39:48,  1.44s/it]
 58%|█████▊    | 2250/3910 [54:22<39:25,  1.42s/it]
                                                   

 58%|█████▊    | 2250/3910 [54:22<39:25,  1.42s/it]
 58%|█████▊    | 2251/3910 [54:23<39:56,  1.44s/it]
 58%|█████▊    | 2252/3910 [54:25<41:10,  1.49s/it]
 58%|█████▊    | 2253/3910 [54:26<40:28,  1.47s/it]
 58%|█████▊    | 2254/3910 [54:28<40:30,  1.47s/it]
 58%|█████▊    | 2255/3910 [54:29<40:36,  1.47s/it]
 58%|█████▊    | 2256/3910 [54:31<40:13,  1.46s/it]
 58%|█████▊    | 2257/3910 [54:32<39:49,  1.45s/it]
 58%|█████▊    | 2258/3910 [54:34<40:00,  1.45s/it]
 58%|█████▊    | 2259/3910 [54:35<39:47,  1.45s/it]
 58%|█████▊    | 2260/3910 [54:36<39:35,  1.44s/it]
 58%|█████▊    | 2261/3910 [54:38<39:04,  1.42s/it]
 58%|█████▊    | 2262/3910 [54:39<38:59,  1.42s/it]
 58%|█████▊    | 2263/3910 [54:41<40:51,  1.49s/it]
 58%|█████▊    | 2264/3910 [54:42<40:16,  1.47s/it]
 58%|█████▊    | 2265/3910 [54:44<39:37,  1.45s/it]
 58%|█████▊    | 2266/3910 [54:45<39:47,  1.45s/it]
 58%|█████▊    | 2267/3910 [54:47<39:53,  1.46s/it]
 58%|█████▊    | 2268/3910 [54:48<39:39,  1.45s/it]
 58%|█████▊    | 2269/3910 [54:49<39:30,  1.44s/it]
 58%|█████▊    | 2270/3910 [54:51<38:58,  1.43s/it]
 58%|█████▊    | 2271/3910 [54:52<38:24,  1.41s/it]
 58%|█████▊    | 2272/3910 [54:54<38:42,  1.42s/it]
 58%|█████▊    | 2273/3910 [54:55<38:42,  1.42s/it]
 58%|█████▊    | 2274/3910 [54:57<38:46,  1.42s/it]
 58%|█████▊    | 2275/3910 [54:58<39:07,  1.44s/it]
                                                   

 58%|█████▊    | 2275/3910 [54:58<39:07,  1.44s/it]
 58%|█████▊    | 2276/3910 [54:59<38:44,  1.42s/it]
 58%|█████▊    | 2277/3910 [55:01<38:57,  1.43s/it]
 58%|█████▊    | 2278/3910 [55:02<39:13,  1.44s/it]
 58%|█████▊    | 2279/3910 [55:04<39:09,  1.44s/it]
 58%|█████▊    | 2280/3910 [55:05<39:08,  1.44s/it]
 58%|█████▊    | 2281/3910 [55:07<39:15,  1.45s/it]
 58%|█████▊    | 2282/3910 [55:08<38:55,  1.43s/it]
 58%|█████▊    | 2283/3910 [55:09<38:53,  1.43s/it]
 58%|█████▊    | 2284/3910 [55:11<38:20,  1.41s/it]
 58%|█████▊    | 2285/3910 [55:12<38:34,  1.42s/it]
 58%|█████▊    | 2286/3910 [55:14<38:35,  1.43s/it]
 58%|█████▊    | 2287/3910 [55:15<38:43,  1.43s/it]
 59%|█████▊    | 2288/3910 [55:17<38:39,  1.43s/it]
 59%|█████▊    | 2289/3910 [55:18<38:26,  1.42s/it]
 59%|█████▊    | 2290/3910 [55:19<38:52,  1.44s/it]
 59%|█████▊    | 2291/3910 [55:21<39:07,  1.45s/it]
 59%|█████▊    | 2292/3910 [55:22<38:49,  1.44s/it]
 59%|█████▊    | 2293/3910 [55:24<39:03,  1.45s/it]
 59%|█████▊    | 2294/3910 [55:25<39:15,  1.46s/it]
 59%|█████▊    | 2295/3910 [55:27<39:13,  1.46s/it]
 59%|█████▊    | 2296/3910 [55:28<38:29,  1.43s/it]
 59%|█████▊    | 2297/3910 [55:30<39:04,  1.45s/it]
 59%|█████▉    | 2298/3910 [55:31<38:24,  1.43s/it]
 59%|█████▉    | 2299/3910 [55:33<38:54,  1.45s/it]
 59%|█████▉    | 2300/3910 [55:34<38:51,  1.45s/it]
                                                   

 59%|█████▉    | 2300/3910 [55:34<38:51,  1.45s/it]
 59%|█████▉    | 2301/3910 [55:35<38:44,  1.44s/it]
 59%|█████▉    | 2302/3910 [55:37<38:30,  1.44s/it]
 59%|█████▉    | 2303/3910 [55:38<38:22,  1.43s/it]
 59%|█████▉    | 2304/3910 [55:40<38:13,  1.43s/it]
 59%|█████▉    | 2305/3910 [55:41<38:03,  1.42s/it]
 59%|█████▉    | 2306/3910 [55:43<38:06,  1.43s/it]
 59%|█████▉    | 2307/3910 [55:44<37:29,  1.40s/it]
 59%|█████▉    | 2308/3910 [55:45<37:45,  1.41s/it]
 59%|█████▉    | 2309/3910 [55:47<38:09,  1.43s/it]
 59%|█████▉    | 2310/3910 [55:48<37:41,  1.41s/it]
 59%|█████▉    | 2311/3910 [55:50<38:13,  1.43s/it]
 59%|█████▉    | 2312/3910 [55:51<37:43,  1.42s/it]
 59%|█████▉    | 2313/3910 [55:52<37:39,  1.42s/it]
 59%|█████▉    | 2314/3910 [55:54<37:45,  1.42s/it]
 59%|█████▉    | 2315/3910 [55:55<38:06,  1.43s/it]
 59%|█████▉    | 2316/3910 [55:57<37:55,  1.43s/it]
 59%|█████▉    | 2317/3910 [55:58<38:11,  1.44s/it]
 59%|█████▉    | 2318/3910 [56:00<38:23,  1.45s/it]
 59%|█████▉    | 2319/3910 [56:01<38:01,  1.43s/it]
 59%|█████▉    | 2320/3910 [56:02<37:59,  1.43s/it]
 59%|█████▉    | 2321/3910 [56:04<37:58,  1.43s/it]
 59%|█████▉    | 2322/3910 [56:05<37:51,  1.43s/it]
 59%|█████▉    | 2323/3910 [56:07<38:08,  1.44s/it]
 59%|█████▉    | 2324/3910 [56:08<38:12,  1.45s/it]
 59%|█████▉    | 2325/3910 [56:10<38:08,  1.44s/it]
                                                   

 59%|█████▉    | 2325/3910 [56:10<38:08,  1.44s/it]
 59%|█████▉    | 2326/3910 [56:11<37:51,  1.43s/it]
 60%|█████▉    | 2327/3910 [56:13<38:06,  1.44s/it]
 60%|█████▉    | 2328/3910 [56:14<37:23,  1.42s/it]
 60%|█████▉    | 2329/3910 [56:15<37:09,  1.41s/it]
 60%|█████▉    | 2330/3910 [56:17<36:59,  1.40s/it]
 60%|█████▉    | 2331/3910 [56:18<37:26,  1.42s/it]
 60%|█████▉    | 2332/3910 [56:20<37:22,  1.42s/it]
 60%|█████▉    | 2333/3910 [56:21<37:28,  1.43s/it]
 60%|█████▉    | 2334/3910 [56:23<37:56,  1.44s/it]
 60%|█████▉    | 2335/3910 [56:24<37:16,  1.42s/it]
 60%|█████▉    | 2336/3910 [56:26<39:22,  1.50s/it]
 60%|█████▉    | 2337/3910 [56:27<39:01,  1.49s/it]
 60%|█████▉    | 2338/3910 [56:29<38:50,  1.48s/it]
 60%|█████▉    | 2339/3910 [56:30<38:29,  1.47s/it]
 60%|█████▉    | 2340/3910 [56:31<38:34,  1.47s/it]
 60%|█████▉    | 2341/3910 [56:33<37:47,  1.44s/it]
 60%|█████▉    | 2342/3910 [56:34<38:08,  1.46s/it]
 60%|█████▉    | 2343/3910 [56:36<38:21,  1.47s/it]
 60%|█████▉    | 2344/3910 [56:37<38:07,  1.46s/it]
 60%|█████▉    | 2345/3910 [56:39<37:43,  1.45s/it]
 60%|██████    | 2346/3910 [56:40<37:39,  1.44s/it]
 60%|██████    | 2347/3910 [56:42<38:01,  1.46s/it]
 60%|██████    | 2348/3910 [56:43<37:51,  1.45s/it]
 60%|██████    | 2349/3910 [56:44<37:47,  1.45s/it]
 60%|██████    | 2350/3910 [56:46<37:49,  1.45s/it]
                                                   

 60%|██████    | 2350/3910 [56:46<37:49,  1.45s/it]
 60%|██████    | 2351/3910 [56:47<37:52,  1.46s/it]
 60%|██████    | 2352/3910 [56:49<37:28,  1.44s/it]
 60%|██████    | 2353/3910 [56:50<37:50,  1.46s/it]
 60%|██████    | 2354/3910 [56:52<37:40,  1.45s/it]
 60%|██████    | 2355/3910 [56:53<37:33,  1.45s/it]
 60%|██████    | 2356/3910 [56:55<37:29,  1.45s/it]
 60%|██████    | 2357/3910 [56:56<36:34,  1.41s/it]
 60%|██████    | 2358/3910 [56:57<36:44,  1.42s/it]
 60%|██████    | 2359/3910 [56:59<37:18,  1.44s/it]
 60%|██████    | 2360/3910 [57:00<37:32,  1.45s/it]
 60%|██████    | 2361/3910 [57:02<37:48,  1.46s/it]
 60%|██████    | 2362/3910 [57:03<37:47,  1.46s/it]
 60%|██████    | 2363/3910 [57:05<37:48,  1.47s/it]
 60%|██████    | 2364/3910 [57:06<37:34,  1.46s/it]
 60%|██████    | 2365/3910 [57:08<37:09,  1.44s/it]
 61%|██████    | 2366/3910 [57:09<37:18,  1.45s/it]
 61%|██████    | 2367/3910 [57:11<37:22,  1.45s/it]
 61%|██████    | 2368/3910 [57:12<37:04,  1.44s/it]
 61%|██████    | 2369/3910 [57:14<37:28,  1.46s/it]
 61%|██████    | 2370/3910 [57:15<37:36,  1.46s/it]
 61%|██████    | 2371/3910 [57:16<37:19,  1.45s/it]
 61%|██████    | 2372/3910 [57:18<36:47,  1.44s/it]
 61%|██████    | 2373/3910 [57:19<37:16,  1.46s/it]
 61%|██████    | 2374/3910 [57:21<36:44,  1.44s/it]
 61%|██████    | 2375/3910 [57:22<37:01,  1.45s/it]
                                                   

 61%|██████    | 2375/3910 [57:22<37:01,  1.45s/it]
 61%|██████    | 2376/3910 [57:24<37:18,  1.46s/it]
 61%|██████    | 2377/3910 [57:25<37:06,  1.45s/it]
 61%|██████    | 2378/3910 [57:26<36:44,  1.44s/it]
 61%|██████    | 2379/3910 [57:28<36:26,  1.43s/it]
 61%|██████    | 2380/3910 [57:29<36:20,  1.43s/it]
 61%|██████    | 2381/3910 [57:31<35:46,  1.40s/it]
 61%|██████    | 2382/3910 [57:32<35:43,  1.40s/it]
 61%|██████    | 2383/3910 [57:34<36:05,  1.42s/it]
 61%|██████    | 2384/3910 [57:35<36:34,  1.44s/it]
 61%|██████    | 2385/3910 [57:36<36:31,  1.44s/it]
 61%|██████    | 2386/3910 [57:38<36:43,  1.45s/it]
 61%|██████    | 2387/3910 [57:39<36:52,  1.45s/it]
 61%|██████    | 2388/3910 [57:41<36:49,  1.45s/it]
 61%|██████    | 2389/3910 [57:42<37:08,  1.47s/it]
 61%|██████    | 2390/3910 [57:44<36:54,  1.46s/it]
 61%|██████    | 2391/3910 [57:45<36:43,  1.45s/it]
 61%|██████    | 2392/3910 [57:47<36:28,  1.44s/it]
 61%|██████    | 2393/3910 [57:48<36:24,  1.44s/it]
 61%|██████    | 2394/3910 [57:50<36:45,  1.45s/it]
 61%|██████▏   | 2395/3910 [57:51<36:39,  1.45s/it]
 61%|██████▏   | 2396/3910 [57:52<36:22,  1.44s/it]
 61%|██████▏   | 2397/3910 [57:54<36:21,  1.44s/it]
 61%|██████▏   | 2398/3910 [57:55<36:06,  1.43s/it]
 61%|██████▏   | 2399/3910 [57:57<36:00,  1.43s/it]
 61%|██████▏   | 2400/3910 [57:58<35:34,  1.41s/it]
                                                   

 61%|██████▏   | 2400/3910 [57:58<35:34,  1.41s/it]
 61%|██████▏   | 2401/3910 [58:00<35:46,  1.42s/it]
 61%|██████▏   | 2402/3910 [58:01<35:39,  1.42s/it]
 61%|██████▏   | 2403/3910 [58:02<35:41,  1.42s/it]
 61%|██████▏   | 2404/3910 [58:04<35:47,  1.43s/it]
 62%|██████▏   | 2405/3910 [58:05<35:12,  1.40s/it]
 62%|██████▏   | 2406/3910 [58:07<35:50,  1.43s/it]
 62%|██████▏   | 2407/3910 [58:08<35:39,  1.42s/it]
 62%|██████▏   | 2408/3910 [58:10<36:04,  1.44s/it]
 62%|██████▏   | 2409/3910 [58:11<35:52,  1.43s/it]
 62%|██████▏   | 2410/3910 [58:12<36:15,  1.45s/it]
 62%|██████▏   | 2411/3910 [58:14<36:25,  1.46s/it]
 62%|██████▏   | 2412/3910 [58:15<36:04,  1.45s/it]
 62%|██████▏   | 2413/3910 [58:17<36:01,  1.44s/it]
 62%|██████▏   | 2414/3910 [58:18<36:02,  1.45s/it]
 62%|██████▏   | 2415/3910 [58:20<36:25,  1.46s/it]
 62%|██████▏   | 2416/3910 [58:21<35:48,  1.44s/it]
 62%|██████▏   | 2417/3910 [58:23<35:54,  1.44s/it]
 62%|██████▏   | 2418/3910 [58:24<35:54,  1.44s/it]
 62%|██████▏   | 2419/3910 [58:25<35:55,  1.45s/it]
 62%|██████▏   | 2420/3910 [58:27<35:32,  1.43s/it]
 62%|██████▏   | 2421/3910 [58:28<35:16,  1.42s/it]
 62%|██████▏   | 2422/3910 [58:30<34:52,  1.41s/it]
 62%|██████▏   | 2423/3910 [58:31<35:01,  1.41s/it]
 62%|██████▏   | 2424/3910 [58:32<35:10,  1.42s/it]
 62%|██████▏   | 2425/3910 [58:34<35:33,  1.44s/it]
                                                   

 62%|██████▏   | 2425/3910 [58:34<35:33,  1.44s/it]
 62%|██████▏   | 2426/3910 [58:35<35:43,  1.44s/it]
 62%|██████▏   | 2427/3910 [58:37<35:36,  1.44s/it]
 62%|██████▏   | 2428/3910 [58:38<35:41,  1.45s/it]
 62%|██████▏   | 2429/3910 [58:40<36:01,  1.46s/it]
 62%|██████▏   | 2430/3910 [58:41<36:07,  1.46s/it]
 62%|██████▏   | 2431/3910 [58:43<36:07,  1.47s/it]
 62%|██████▏   | 2432/3910 [58:44<36:07,  1.47s/it]
 62%|██████▏   | 2433/3910 [58:46<36:10,  1.47s/it]
 62%|██████▏   | 2434/3910 [58:47<36:18,  1.48s/it]
 62%|██████▏   | 2435/3910 [58:49<36:24,  1.48s/it]
 62%|██████▏   | 2436/3910 [58:50<35:58,  1.46s/it]
 62%|██████▏   | 2437/3910 [58:52<35:49,  1.46s/it]
 62%|██████▏   | 2438/3910 [58:53<35:23,  1.44s/it]
 62%|██████▏   | 2439/3910 [58:54<35:26,  1.45s/it]
 62%|██████▏   | 2440/3910 [58:56<35:35,  1.45s/it]
 62%|██████▏   | 2441/3910 [58:57<35:26,  1.45s/it]
 62%|██████▏   | 2442/3910 [58:59<35:33,  1.45s/it]
 62%|██████▏   | 2443/3910 [59:00<35:15,  1.44s/it]
 63%|██████▎   | 2444/3910 [59:02<34:49,  1.43s/it]
 63%|██████▎   | 2445/3910 [59:03<35:13,  1.44s/it]
 63%|██████▎   | 2446/3910 [59:04<35:13,  1.44s/it]
 63%|██████▎   | 2447/3910 [59:06<35:15,  1.45s/it]
 63%|██████▎   | 2448/3910 [59:07<34:50,  1.43s/it]
 63%|██████▎   | 2449/3910 [59:09<34:43,  1.43s/it]
 63%|██████▎   | 2450/3910 [59:10<35:11,  1.45s/it]
                                                   

 63%|██████▎   | 2450/3910 [59:10<35:11,  1.45s/it]
 63%|██████▎   | 2451/3910 [59:12<35:05,  1.44s/it]
 63%|██████▎   | 2452/3910 [59:13<35:05,  1.44s/it]
 63%|██████▎   | 2453/3910 [59:15<35:21,  1.46s/it]
 63%|██████▎   | 2454/3910 [59:16<34:46,  1.43s/it]
 63%|██████▎   | 2455/3910 [59:17<34:39,  1.43s/it]
 63%|██████▎   | 2456/3910 [59:19<35:05,  1.45s/it]
 63%|██████▎   | 2457/3910 [59:20<35:06,  1.45s/it]
 63%|██████▎   | 2458/3910 [59:22<35:24,  1.46s/it]
 63%|██████▎   | 2459/3910 [59:23<35:08,  1.45s/it]
 63%|██████▎   | 2460/3910 [59:25<36:20,  1.50s/it]
 63%|██████▎   | 2461/3910 [59:26<35:40,  1.48s/it]
 63%|██████▎   | 2462/3910 [59:28<34:57,  1.45s/it]
 63%|██████▎   | 2463/3910 [59:29<34:35,  1.43s/it]
 63%|██████▎   | 2464/3910 [59:31<34:46,  1.44s/it]
 63%|██████▎   | 2465/3910 [59:32<35:04,  1.46s/it]
 63%|██████▎   | 2466/3910 [59:34<35:12,  1.46s/it]
 63%|██████▎   | 2467/3910 [59:35<34:53,  1.45s/it]
 63%|██████▎   | 2468/3910 [59:36<34:49,  1.45s/it]
 63%|██████▎   | 2469/3910 [59:38<34:56,  1.45s/it]
 63%|██████▎   | 2470/3910 [59:39<34:59,  1.46s/it]
 63%|██████▎   | 2471/3910 [59:41<35:09,  1.47s/it]
 63%|██████▎   | 2472/3910 [59:42<34:49,  1.45s/it]
 63%|██████▎   | 2473/3910 [59:44<34:36,  1.45s/it]
 63%|██████▎   | 2474/3910 [59:45<34:47,  1.45s/it]
 63%|██████▎   | 2475/3910 [59:47<34:59,  1.46s/it]
                                                   

 63%|██████▎   | 2475/3910 [59:47<34:59,  1.46s/it]
 63%|██████▎   | 2476/3910 [59:48<34:58,  1.46s/it]
 63%|██████▎   | 2477/3910 [59:50<35:01,  1.47s/it]
 63%|██████▎   | 2478/3910 [59:51<35:03,  1.47s/it]
 63%|██████▎   | 2479/3910 [59:52<34:48,  1.46s/it]
 63%|██████▎   | 2480/3910 [59:54<34:28,  1.45s/it]
 63%|██████▎   | 2481/3910 [59:55<34:24,  1.44s/it]
 63%|██████▎   | 2482/3910 [59:57<34:45,  1.46s/it]
 64%|██████▎   | 2483/3910 [59:58<34:49,  1.46s/it]
 64%|██████▎   | 2484/3910 [1:00:00<34:50,  1.47s/it]
 64%|██████▎   | 2485/3910 [1:00:01<34:58,  1.47s/it]
 64%|██████▎   | 2486/3910 [1:00:03<34:20,  1.45s/it]
 64%|██████▎   | 2487/3910 [1:00:04<34:29,  1.45s/it]
 64%|██████▎   | 2488/3910 [1:00:06<34:43,  1.47s/it]
 64%|██████▎   | 2489/3910 [1:00:07<34:47,  1.47s/it]
 64%|██████▎   | 2490/3910 [1:00:09<34:37,  1.46s/it]
 64%|██████▎   | 2491/3910 [1:00:10<34:13,  1.45s/it]
 64%|██████▎   | 2492/3910 [1:00:11<33:49,  1.43s/it]
 64%|██████▍   | 2493/3910 [1:00:13<34:12,  1.45s/it]
 64%|██████▍   | 2494/3910 [1:00:14<33:47,  1.43s/it]
 64%|██████▍   | 2495/3910 [1:00:16<33:40,  1.43s/it]
 64%|██████▍   | 2496/3910 [1:00:17<33:48,  1.43s/it]
 64%|██████▍   | 2497/3910 [1:00:18<33:20,  1.42s/it]
 64%|██████▍   | 2498/3910 [1:00:20<33:07,  1.41s/it]
 64%|██████▍   | 2499/3910 [1:00:21<33:32,  1.43s/it]
 64%|██████▍   | 2500/3910 [1:00:23<33:49,  1.44s/it]
                                                     

 64%|██████▍   | 2500/3910 [1:00:23<33:49,  1.44s/it]
 64%|██████▍   | 2501/3910 [1:00:24<33:50,  1.44s/it]
 64%|██████▍   | 2502/3910 [1:00:26<33:35,  1.43s/it]
 64%|██████▍   | 2503/3910 [1:00:27<33:31,  1.43s/it]
 64%|██████▍   | 2504/3910 [1:00:29<33:35,  1.43s/it]
 64%|██████▍   | 2505/3910 [1:00:30<33:55,  1.45s/it]
 64%|██████▍   | 2506/3910 [1:00:31<33:46,  1.44s/it]
 64%|██████▍   | 2507/3910 [1:00:33<33:32,  1.43s/it]
 64%|██████▍   | 2508/3910 [1:00:34<33:33,  1.44s/it]
 64%|██████▍   | 2509/3910 [1:00:36<33:54,  1.45s/it]
 64%|██████▍   | 2510/3910 [1:00:37<34:09,  1.46s/it]
 64%|██████▍   | 2511/3910 [1:00:39<33:52,  1.45s/it]
 64%|██████▍   | 2512/3910 [1:00:40<33:38,  1.44s/it]
 64%|██████▍   | 2513/3910 [1:00:42<33:38,  1.44s/it]
 64%|██████▍   | 2514/3910 [1:00:43<33:52,  1.46s/it]
 64%|██████▍   | 2515/3910 [1:00:45<33:56,  1.46s/it]
 64%|██████▍   | 2516/3910 [1:00:46<33:32,  1.44s/it]
 64%|██████▍   | 2517/3910 [1:00:47<33:51,  1.46s/it]
 64%|██████▍   | 2518/3910 [1:00:49<33:42,  1.45s/it]
 64%|██████▍   | 2519/3910 [1:00:50<33:34,  1.45s/it]
 64%|██████▍   | 2520/3910 [1:00:52<33:22,  1.44s/it]
 64%|██████▍   | 2521/3910 [1:00:53<33:03,  1.43s/it]
 65%|██████▍   | 2522/3910 [1:00:55<33:15,  1.44s/it]
 65%|██████▍   | 2523/3910 [1:00:56<32:53,  1.42s/it]
 65%|██████▍   | 2524/3910 [1:00:57<33:14,  1.44s/it]
 65%|██████▍   | 2525/3910 [1:00:59<33:31,  1.45s/it]
                                                     

 65%|██████▍   | 2525/3910 [1:00:59<33:31,  1.45s/it]
 65%|██████▍   | 2526/3910 [1:01:00<33:43,  1.46s/it]
 65%|██████▍   | 2527/3910 [1:01:02<33:32,  1.46s/it]
 65%|██████▍   | 2528/3910 [1:01:03<33:15,  1.44s/it]
 65%|██████▍   | 2529/3910 [1:01:05<34:28,  1.50s/it]
 65%|██████▍   | 2530/3910 [1:01:06<33:54,  1.47s/it]
 65%|██████▍   | 2531/3910 [1:01:08<33:59,  1.48s/it]
 65%|██████▍   | 2532/3910 [1:01:09<33:47,  1.47s/it]
 65%|██████▍   | 2533/3910 [1:01:11<33:38,  1.47s/it]
 65%|██████▍   | 2534/3910 [1:01:12<33:22,  1.46s/it]
 65%|██████▍   | 2535/3910 [1:01:14<33:36,  1.47s/it]
 65%|██████▍   | 2536/3910 [1:01:15<32:54,  1.44s/it]
 65%|██████▍   | 2537/3910 [1:01:16<32:45,  1.43s/it]
 65%|██████▍   | 2538/3910 [1:01:18<32:22,  1.42s/it]
 65%|██████▍   | 2539/3910 [1:01:19<32:49,  1.44s/it]
 65%|██████▍   | 2540/3910 [1:01:21<32:55,  1.44s/it]
 65%|██████▍   | 2541/3910 [1:01:22<32:58,  1.44s/it]
 65%|██████▌   | 2542/3910 [1:01:24<32:09,  1.41s/it]
 65%|██████▌   | 2543/3910 [1:01:25<32:27,  1.42s/it]
 65%|██████▌   | 2544/3910 [1:01:26<32:35,  1.43s/it]
 65%|██████▌   | 2545/3910 [1:01:28<32:45,  1.44s/it]
 65%|██████▌   | 2546/3910 [1:01:29<32:50,  1.44s/it]
 65%|██████▌   | 2547/3910 [1:01:31<32:53,  1.45s/it]
 65%|██████▌   | 2548/3910 [1:01:32<33:08,  1.46s/it]
 65%|██████▌   | 2549/3910 [1:01:34<33:13,  1.47s/it]
 65%|██████▌   | 2550/3910 [1:01:35<33:17,  1.47s/it]
                                                     

 65%|██████▌   | 2550/3910 [1:01:35<33:17,  1.47s/it]
 65%|██████▌   | 2551/3910 [1:01:37<33:20,  1.47s/it]
 65%|██████▌   | 2552/3910 [1:01:38<33:30,  1.48s/it]
 65%|██████▌   | 2553/3910 [1:01:40<33:34,  1.48s/it]
 65%|██████▌   | 2554/3910 [1:01:41<33:33,  1.48s/it]
 65%|██████▌   | 2555/3910 [1:01:43<33:38,  1.49s/it]
 65%|██████▌   | 2556/3910 [1:01:44<33:01,  1.46s/it]
 65%|██████▌   | 2557/3910 [1:01:46<32:55,  1.46s/it]
 65%|██████▌   | 2558/3910 [1:01:47<32:54,  1.46s/it]
 65%|██████▌   | 2559/3910 [1:01:48<32:53,  1.46s/it]
 65%|██████▌   | 2560/3910 [1:01:50<32:26,  1.44s/it]
 65%|██████▌   | 2561/3910 [1:01:51<31:54,  1.42s/it]
 66%|██████▌   | 2562/3910 [1:01:53<32:07,  1.43s/it]
 66%|██████▌   | 2563/3910 [1:01:54<31:59,  1.43s/it]
 66%|██████▌   | 2564/3910 [1:01:56<32:04,  1.43s/it]
 66%|██████▌   | 2565/3910 [1:01:57<31:54,  1.42s/it]
 66%|██████▌   | 2566/3910 [1:01:58<31:54,  1.42s/it]
 66%|██████▌   | 2567/3910 [1:02:00<31:48,  1.42s/it]
 66%|██████▌   | 2568/3910 [1:02:01<32:14,  1.44s/it]
 66%|██████▌   | 2569/3910 [1:02:03<32:12,  1.44s/it]
 66%|██████▌   | 2570/3910 [1:02:04<32:13,  1.44s/it]
 66%|██████▌   | 2571/3910 [1:02:06<32:10,  1.44s/it]
 66%|██████▌   | 2572/3910 [1:02:07<33:37,  1.51s/it]
 66%|██████▌   | 2573/3910 [1:02:09<33:15,  1.49s/it]
 66%|██████▌   | 2574/3910 [1:02:10<32:48,  1.47s/it]
 66%|██████▌   | 2575/3910 [1:02:12<32:22,  1.46s/it]
                                                     

 66%|██████▌   | 2575/3910 [1:02:12<32:22,  1.46s/it]
 66%|██████▌   | 2576/3910 [1:02:13<32:12,  1.45s/it]
 66%|██████▌   | 2577/3910 [1:02:14<32:32,  1.46s/it]
 66%|██████▌   | 2578/3910 [1:02:16<32:40,  1.47s/it]
 66%|██████▌   | 2579/3910 [1:02:17<32:14,  1.45s/it]
 66%|██████▌   | 2580/3910 [1:02:19<32:21,  1.46s/it]
 66%|██████▌   | 2581/3910 [1:02:20<32:05,  1.45s/it]
 66%|██████▌   | 2582/3910 [1:02:22<31:53,  1.44s/it]
 66%|██████▌   | 2583/3910 [1:02:23<31:29,  1.42s/it]
 66%|██████▌   | 2584/3910 [1:02:25<31:33,  1.43s/it]
 66%|██████▌   | 2585/3910 [1:02:26<31:45,  1.44s/it]
 66%|██████▌   | 2586/3910 [1:02:27<31:41,  1.44s/it]
 66%|██████▌   | 2587/3910 [1:02:29<31:31,  1.43s/it]
 66%|██████▌   | 2588/3910 [1:02:30<31:44,  1.44s/it]
 66%|██████▌   | 2589/3910 [1:02:32<31:46,  1.44s/it]
 66%|██████▌   | 2590/3910 [1:02:33<32:02,  1.46s/it]
 66%|██████▋   | 2591/3910 [1:02:35<31:54,  1.45s/it]
 66%|██████▋   | 2592/3910 [1:02:36<31:59,  1.46s/it]
 66%|██████▋   | 2593/3910 [1:02:38<31:57,  1.46s/it]
 66%|██████▋   | 2594/3910 [1:02:39<31:51,  1.45s/it]
 66%|██████▋   | 2595/3910 [1:02:40<31:49,  1.45s/it]
 66%|██████▋   | 2596/3910 [1:02:42<31:54,  1.46s/it]
 66%|██████▋   | 2597/3910 [1:02:43<31:40,  1.45s/it]
 66%|██████▋   | 2598/3910 [1:02:45<31:50,  1.46s/it]
 66%|██████▋   | 2599/3910 [1:02:46<31:37,  1.45s/it]
 66%|██████▋   | 2600/3910 [1:02:48<31:51,  1.46s/it]
                                                     

 66%|██████▋   | 2600/3910 [1:02:48<31:51,  1.46s/it]
 67%|██████▋   | 2601/3910 [1:02:49<31:56,  1.46s/it]
 67%|██████▋   | 2602/3910 [1:02:51<31:18,  1.44s/it]
 67%|██████▋   | 2603/3910 [1:02:52<31:13,  1.43s/it]
 67%|██████▋   | 2604/3910 [1:02:53<30:59,  1.42s/it]
 67%|██████▋   | 2605/3910 [1:02:55<31:05,  1.43s/it]
 67%|██████▋   | 2606/3910 [1:02:56<31:30,  1.45s/it]
 67%|██████▋   | 2607/3910 [1:02:58<31:29,  1.45s/it]
 67%|██████▋   | 2608/3910 [1:02:59<31:22,  1.45s/it]
 67%|██████▋   | 2609/3910 [1:03:01<31:22,  1.45s/it]
 67%|██████▋   | 2610/3910 [1:03:02<31:20,  1.45s/it]
 67%|██████▋   | 2611/3910 [1:03:04<31:24,  1.45s/it]
 67%|██████▋   | 2612/3910 [1:03:05<31:34,  1.46s/it]
 67%|██████▋   | 2613/3910 [1:03:07<31:27,  1.46s/it]
 67%|██████▋   | 2614/3910 [1:03:08<31:13,  1.45s/it]
 67%|██████▋   | 2615/3910 [1:03:09<30:59,  1.44s/it]
 67%|██████▋   | 2616/3910 [1:03:11<30:47,  1.43s/it]
 67%|██████▋   | 2617/3910 [1:03:12<30:57,  1.44s/it]
 67%|██████▋   | 2618/3910 [1:03:14<30:49,  1.43s/it]
 67%|██████▋   | 2619/3910 [1:03:15<30:15,  1.41s/it]
 67%|██████▋   | 2620/3910 [1:03:16<29:55,  1.39s/it]
 67%|██████▋   | 2621/3910 [1:03:18<30:16,  1.41s/it]
 67%|██████▋   | 2622/3910 [1:03:19<30:36,  1.43s/it]
 67%|██████▋   | 2623/3910 [1:03:21<30:47,  1.44s/it]
 67%|██████▋   | 2624/3910 [1:03:22<30:46,  1.44s/it]
 67%|██████▋   | 2625/3910 [1:03:24<30:55,  1.44s/it]
                                                     

 67%|██████▋   | 2625/3910 [1:03:24<30:55,  1.44s/it]
 67%|██████▋   | 2626/3910 [1:03:25<30:59,  1.45s/it]
 67%|██████▋   | 2627/3910 [1:03:27<31:04,  1.45s/it]
 67%|██████▋   | 2628/3910 [1:03:28<30:24,  1.42s/it]
 67%|██████▋   | 2629/3910 [1:03:29<30:46,  1.44s/it]
 67%|██████▋   | 2630/3910 [1:03:31<30:39,  1.44s/it]
 67%|██████▋   | 2631/3910 [1:03:32<30:35,  1.43s/it]
 67%|██████▋   | 2632/3910 [1:03:34<30:31,  1.43s/it]
 67%|██████▋   | 2633/3910 [1:03:35<30:38,  1.44s/it]
 67%|██████▋   | 2634/3910 [1:03:37<30:31,  1.44s/it]
 67%|██████▋   | 2635/3910 [1:03:38<31:54,  1.50s/it]
 67%|██████▋   | 2636/3910 [1:03:40<31:41,  1.49s/it]
 67%|██████▋   | 2637/3910 [1:03:41<31:19,  1.48s/it]
 67%|██████▋   | 2638/3910 [1:03:43<30:32,  1.44s/it]
 67%|██████▋   | 2639/3910 [1:03:44<30:44,  1.45s/it]
 68%|██████▊   | 2640/3910 [1:03:45<30:37,  1.45s/it]
 68%|██████▊   | 2641/3910 [1:03:47<30:48,  1.46s/it]
 68%|██████▊   | 2642/3910 [1:03:48<30:03,  1.42s/it]
 68%|██████▊   | 2643/3910 [1:03:50<29:46,  1.41s/it]
 68%|██████▊   | 2644/3910 [1:03:51<30:06,  1.43s/it]
 68%|██████▊   | 2645/3910 [1:03:53<29:56,  1.42s/it]
 68%|██████▊   | 2646/3910 [1:03:54<30:10,  1.43s/it]
 68%|██████▊   | 2647/3910 [1:03:55<30:31,  1.45s/it]
 68%|██████▊   | 2648/3910 [1:03:57<30:34,  1.45s/it]
 68%|██████▊   | 2649/3910 [1:03:59<31:55,  1.52s/it]
 68%|██████▊   | 2650/3910 [1:04:00<30:55,  1.47s/it]
                                                     

 68%|██████▊   | 2650/3910 [1:04:00<30:55,  1.47s/it]
 68%|██████▊   | 2651/3910 [1:04:01<30:56,  1.47s/it]
 68%|██████▊   | 2652/3910 [1:04:03<30:46,  1.47s/it]
 68%|██████▊   | 2653/3910 [1:04:04<30:53,  1.47s/it]
 68%|██████▊   | 2654/3910 [1:04:06<30:31,  1.46s/it]
 68%|██████▊   | 2655/3910 [1:04:07<30:33,  1.46s/it]
 68%|██████▊   | 2656/3910 [1:04:09<30:13,  1.45s/it]
 68%|██████▊   | 2657/3910 [1:04:10<30:20,  1.45s/it]
 68%|██████▊   | 2658/3910 [1:04:12<29:47,  1.43s/it]
 68%|██████▊   | 2659/3910 [1:04:13<29:43,  1.43s/it]
 68%|██████▊   | 2660/3910 [1:04:14<29:23,  1.41s/it]
 68%|██████▊   | 2661/3910 [1:04:16<29:23,  1.41s/it]
 68%|██████▊   | 2662/3910 [1:04:17<29:15,  1.41s/it]
 68%|██████▊   | 2663/3910 [1:04:19<29:24,  1.42s/it]
 68%|██████▊   | 2664/3910 [1:04:20<29:21,  1.41s/it]
 68%|██████▊   | 2665/3910 [1:04:21<29:35,  1.43s/it]
 68%|██████▊   | 2666/3910 [1:04:23<29:20,  1.42s/it]
 68%|██████▊   | 2667/3910 [1:04:24<29:23,  1.42s/it]
 68%|██████▊   | 2668/3910 [1:04:26<29:43,  1.44s/it]
 68%|██████▊   | 2669/3910 [1:04:27<29:59,  1.45s/it]
 68%|██████▊   | 2670/3910 [1:04:29<30:09,  1.46s/it]
 68%|██████▊   | 2671/3910 [1:04:30<30:19,  1.47s/it]
 68%|██████▊   | 2672/3910 [1:04:32<30:28,  1.48s/it]
 68%|██████▊   | 2673/3910 [1:04:33<30:23,  1.47s/it]
 68%|██████▊   | 2674/3910 [1:04:35<29:47,  1.45s/it]
 68%|██████▊   | 2675/3910 [1:04:36<29:47,  1.45s/it]
                                                     

 68%|██████▊   | 2675/3910 [1:04:36<29:47,  1.45s/it]
 68%|██████▊   | 2676/3910 [1:04:37<29:16,  1.42s/it]
 68%|██████▊   | 2677/3910 [1:04:39<29:30,  1.44s/it]
 68%|██████▊   | 2678/3910 [1:04:40<29:53,  1.46s/it]
 69%|██████▊   | 2679/3910 [1:04:42<29:25,  1.43s/it]
 69%|██████▊   | 2680/3910 [1:04:43<29:34,  1.44s/it]
 69%|██████▊   | 2681/3910 [1:04:45<29:19,  1.43s/it]
 69%|██████▊   | 2682/3910 [1:04:46<29:24,  1.44s/it]
 69%|██████▊   | 2683/3910 [1:04:47<29:33,  1.45s/it]
 69%|██████▊   | 2684/3910 [1:04:49<29:36,  1.45s/it]
 69%|██████▊   | 2685/3910 [1:04:50<29:22,  1.44s/it]
 69%|██████▊   | 2686/3910 [1:04:52<29:10,  1.43s/it]
 69%|██████▊   | 2687/3910 [1:04:53<29:26,  1.44s/it]
 69%|██████▊   | 2688/3910 [1:04:55<29:35,  1.45s/it]
 69%|██████▉   | 2689/3910 [1:04:56<29:19,  1.44s/it]
 69%|██████▉   | 2690/3910 [1:04:57<28:56,  1.42s/it]
 69%|██████▉   | 2691/3910 [1:04:59<28:46,  1.42s/it]
 69%|██████▉   | 2692/3910 [1:05:00<29:04,  1.43s/it]
 69%|██████▉   | 2693/3910 [1:05:02<29:25,  1.45s/it]
 69%|██████▉   | 2694/3910 [1:05:03<29:00,  1.43s/it]
 69%|██████▉   | 2695/3910 [1:05:05<28:55,  1.43s/it]
 69%|██████▉   | 2696/3910 [1:05:06<29:19,  1.45s/it]
 69%|██████▉   | 2697/3910 [1:05:08<29:05,  1.44s/it]
 69%|██████▉   | 2698/3910 [1:05:09<28:46,  1.42s/it]
 69%|██████▉   | 2699/3910 [1:05:10<29:03,  1.44s/it]
 69%|██████▉   | 2700/3910 [1:05:12<29:13,  1.45s/it]
                                                     

 69%|██████▉   | 2700/3910 [1:05:12<29:13,  1.45s/it]
 69%|██████▉   | 2701/3910 [1:05:13<29:03,  1.44s/it]
 69%|██████▉   | 2702/3910 [1:05:15<29:12,  1.45s/it]
 69%|██████▉   | 2703/3910 [1:05:16<29:16,  1.46s/it]
 69%|██████▉   | 2704/3910 [1:05:18<29:10,  1.45s/it]
 69%|██████▉   | 2705/3910 [1:05:19<29:05,  1.45s/it]
 69%|██████▉   | 2706/3910 [1:05:21<29:08,  1.45s/it]
 69%|██████▉   | 2707/3910 [1:05:22<29:05,  1.45s/it]
 69%|██████▉   | 2708/3910 [1:05:24<29:00,  1.45s/it]
 69%|██████▉   | 2709/3910 [1:05:25<28:30,  1.42s/it]
 69%|██████▉   | 2710/3910 [1:05:27<29:43,  1.49s/it]
 69%|██████▉   | 2711/3910 [1:05:28<29:17,  1.47s/it]
 69%|██████▉   | 2712/3910 [1:05:29<29:13,  1.46s/it]
 69%|██████▉   | 2713/3910 [1:05:31<29:00,  1.45s/it]
 69%|██████▉   | 2714/3910 [1:05:32<28:55,  1.45s/it]
 69%|██████▉   | 2715/3910 [1:05:34<29:00,  1.46s/it]
 69%|██████▉   | 2716/3910 [1:05:35<29:01,  1.46s/it]
 69%|██████▉   | 2717/3910 [1:05:37<29:03,  1.46s/it]
 70%|██████▉   | 2718/3910 [1:05:38<29:10,  1.47s/it]
 70%|██████▉   | 2719/3910 [1:05:40<28:57,  1.46s/it]
 70%|██████▉   | 2720/3910 [1:05:41<28:40,  1.45s/it]
 70%|██████▉   | 2721/3910 [1:05:42<28:41,  1.45s/it]
 70%|██████▉   | 2722/3910 [1:05:44<28:38,  1.45s/it]
 70%|██████▉   | 2723/3910 [1:05:45<28:47,  1.46s/it]
 70%|██████▉   | 2724/3910 [1:05:47<28:34,  1.45s/it]
 70%|██████▉   | 2725/3910 [1:05:48<28:29,  1.44s/it]
                                                     

 70%|██████▉   | 2725/3910 [1:05:48<28:29,  1.44s/it]
 70%|██████▉   | 2726/3910 [1:05:50<28:27,  1.44s/it]
 70%|██████▉   | 2727/3910 [1:05:51<28:36,  1.45s/it]
 70%|██████▉   | 2728/3910 [1:05:53<28:22,  1.44s/it]
 70%|██████▉   | 2729/3910 [1:05:54<28:29,  1.45s/it]
 70%|██████▉   | 2730/3910 [1:05:55<28:19,  1.44s/it]
 70%|██████▉   | 2731/3910 [1:05:57<28:27,  1.45s/it]
 70%|██████▉   | 2732/3910 [1:05:58<28:11,  1.44s/it]
 70%|██████▉   | 2733/3910 [1:06:00<28:15,  1.44s/it]
 70%|██████▉   | 2734/3910 [1:06:01<28:31,  1.46s/it]
 70%|██████▉   | 2735/3910 [1:06:03<28:14,  1.44s/it]
 70%|██████▉   | 2736/3910 [1:06:04<28:21,  1.45s/it]
 70%|███████   | 2737/3910 [1:06:06<28:25,  1.45s/it]
 70%|███████   | 2738/3910 [1:06:07<28:32,  1.46s/it]
 70%|███████   | 2739/3910 [1:06:09<28:33,  1.46s/it]
 70%|███████   | 2740/3910 [1:06:10<28:33,  1.46s/it]
 70%|███████   | 2741/3910 [1:06:11<28:22,  1.46s/it]
 70%|███████   | 2742/3910 [1:06:13<27:37,  1.42s/it]
 70%|███████   | 2743/3910 [1:06:14<27:45,  1.43s/it]
 70%|███████   | 2744/3910 [1:06:16<27:49,  1.43s/it]
 70%|███████   | 2745/3910 [1:06:17<28:03,  1.45s/it]
 70%|███████   | 2746/3910 [1:06:19<27:53,  1.44s/it]
 70%|███████   | 2747/3910 [1:06:20<27:50,  1.44s/it]
 70%|███████   | 2748/3910 [1:06:21<27:45,  1.43s/it]
 70%|███████   | 2749/3910 [1:06:23<27:53,  1.44s/it]
 70%|███████   | 2750/3910 [1:06:24<27:49,  1.44s/it]
                                                     

 70%|███████   | 2750/3910 [1:06:24<27:49,  1.44s/it]
 70%|███████   | 2751/3910 [1:06:26<27:30,  1.42s/it]
 70%|███████   | 2752/3910 [1:06:27<27:51,  1.44s/it]
 70%|███████   | 2753/3910 [1:06:29<27:40,  1.43s/it]
 70%|███████   | 2754/3910 [1:06:30<27:40,  1.44s/it]
 70%|███████   | 2755/3910 [1:06:32<27:49,  1.45s/it]
 70%|███████   | 2756/3910 [1:06:33<27:49,  1.45s/it]
 71%|███████   | 2757/3910 [1:06:34<27:39,  1.44s/it]
 71%|███████   | 2758/3910 [1:06:36<27:40,  1.44s/it]
 71%|███████   | 2759/3910 [1:06:37<27:50,  1.45s/it]
 71%|███████   | 2760/3910 [1:06:39<27:47,  1.45s/it]
 71%|███████   | 2761/3910 [1:06:40<27:24,  1.43s/it]
 71%|███████   | 2762/3910 [1:06:42<27:35,  1.44s/it]
 71%|███████   | 2763/3910 [1:06:43<27:46,  1.45s/it]
 71%|███████   | 2764/3910 [1:06:45<27:35,  1.44s/it]
 71%|███████   | 2765/3910 [1:06:46<27:31,  1.44s/it]
 71%|███████   | 2766/3910 [1:06:47<27:47,  1.46s/it]
 71%|███████   | 2767/3910 [1:06:49<27:26,  1.44s/it]
 71%|███████   | 2768/3910 [1:06:50<27:20,  1.44s/it]
 71%|███████   | 2769/3910 [1:06:52<27:27,  1.44s/it]
 71%|███████   | 2770/3910 [1:06:53<27:24,  1.44s/it]
 71%|███████   | 2771/3910 [1:06:55<27:06,  1.43s/it]
 71%|███████   | 2772/3910 [1:06:56<27:26,  1.45s/it]
 71%|███████   | 2773/3910 [1:06:57<27:17,  1.44s/it]
 71%|███████   | 2774/3910 [1:06:59<27:34,  1.46s/it]
 71%|███████   | 2775/3910 [1:07:00<27:44,  1.47s/it]
                                                     

 71%|███████   | 2775/3910 [1:07:00<27:44,  1.47s/it]
 71%|███████   | 2776/3910 [1:07:02<27:33,  1.46s/it]
 71%|███████   | 2777/3910 [1:07:03<27:37,  1.46s/it]
 71%|███████   | 2778/3910 [1:07:05<27:44,  1.47s/it]
 71%|███████   | 2779/3910 [1:07:06<27:32,  1.46s/it]
 71%|███████   | 2780/3910 [1:07:08<27:26,  1.46s/it]
 71%|███████   | 2781/3910 [1:07:09<27:17,  1.45s/it]
 71%|███████   | 2782/3910 [1:07:11<27:17,  1.45s/it]
 71%|███████   | 2783/3910 [1:07:12<27:28,  1.46s/it]
 71%|███████   | 2784/3910 [1:07:14<27:34,  1.47s/it]
 71%|███████   | 2785/3910 [1:07:15<27:13,  1.45s/it]
 71%|███████▏  | 2786/3910 [1:07:16<27:08,  1.45s/it]
 71%|███████▏  | 2787/3910 [1:07:18<26:48,  1.43s/it]
 71%|███████▏  | 2788/3910 [1:07:19<26:58,  1.44s/it]
 71%|███████▏  | 2789/3910 [1:07:21<27:11,  1.46s/it]
 71%|███████▏  | 2790/3910 [1:07:22<27:00,  1.45s/it]
 71%|███████▏  | 2791/3910 [1:07:24<27:08,  1.46s/it]
 71%|███████▏  | 2792/3910 [1:07:25<27:11,  1.46s/it]
 71%|███████▏  | 2793/3910 [1:07:27<27:13,  1.46s/it]
 71%|███████▏  | 2794/3910 [1:07:28<27:02,  1.45s/it]
 71%|███████▏  | 2795/3910 [1:07:30<27:07,  1.46s/it]
 72%|███████▏  | 2796/3910 [1:07:31<27:11,  1.46s/it]
 72%|███████▏  | 2797/3910 [1:07:33<27:22,  1.48s/it]
 72%|███████▏  | 2798/3910 [1:07:34<27:07,  1.46s/it]
 72%|███████▏  | 2799/3910 [1:07:35<27:15,  1.47s/it]
 72%|███████▏  | 2800/3910 [1:07:37<26:54,  1.45s/it]
                                                     

 72%|███████▏  | 2800/3910 [1:07:37<26:54,  1.45s/it]
 72%|███████▏  | 2801/3910 [1:07:38<26:44,  1.45s/it]
 72%|███████▏  | 2802/3910 [1:07:40<26:38,  1.44s/it]
 72%|███████▏  | 2803/3910 [1:07:41<26:43,  1.45s/it]
 72%|███████▏  | 2804/3910 [1:07:43<26:50,  1.46s/it]
 72%|███████▏  | 2805/3910 [1:07:44<26:37,  1.45s/it]
 72%|███████▏  | 2806/3910 [1:07:46<26:42,  1.45s/it]
 72%|███████▏  | 2807/3910 [1:07:47<26:43,  1.45s/it]
 72%|███████▏  | 2808/3910 [1:07:48<26:46,  1.46s/it]
 72%|███████▏  | 2809/3910 [1:07:50<26:38,  1.45s/it]
 72%|███████▏  | 2810/3910 [1:07:51<26:25,  1.44s/it]
 72%|███████▏  | 2811/3910 [1:07:53<26:21,  1.44s/it]
 72%|███████▏  | 2812/3910 [1:07:54<26:19,  1.44s/it]
 72%|███████▏  | 2813/3910 [1:07:56<26:12,  1.43s/it]
 72%|███████▏  | 2814/3910 [1:07:57<26:00,  1.42s/it]
 72%|███████▏  | 2815/3910 [1:07:59<26:13,  1.44s/it]
 72%|███████▏  | 2816/3910 [1:08:00<26:20,  1.44s/it]
 72%|███████▏  | 2817/3910 [1:08:01<26:32,  1.46s/it]
 72%|███████▏  | 2818/3910 [1:08:03<26:22,  1.45s/it]
 72%|███████▏  | 2819/3910 [1:08:04<26:04,  1.43s/it]
 72%|███████▏  | 2820/3910 [1:08:06<26:22,  1.45s/it]
 72%|███████▏  | 2821/3910 [1:08:07<26:14,  1.45s/it]
 72%|███████▏  | 2822/3910 [1:08:09<26:16,  1.45s/it]
 72%|███████▏  | 2823/3910 [1:08:10<26:26,  1.46s/it]
 72%|███████▏  | 2824/3910 [1:08:12<25:54,  1.43s/it]
 72%|███████▏  | 2825/3910 [1:08:13<26:13,  1.45s/it]
                                                     

 72%|███████▏  | 2825/3910 [1:08:13<26:13,  1.45s/it]
 72%|███████▏  | 2826/3910 [1:08:14<26:01,  1.44s/it]
 72%|███████▏  | 2827/3910 [1:08:16<26:06,  1.45s/it]
 72%|███████▏  | 2828/3910 [1:08:17<26:00,  1.44s/it]
 72%|███████▏  | 2829/3910 [1:08:19<25:57,  1.44s/it]
 72%|███████▏  | 2830/3910 [1:08:20<25:57,  1.44s/it]
 72%|███████▏  | 2831/3910 [1:08:22<25:52,  1.44s/it]
 72%|███████▏  | 2832/3910 [1:08:23<25:49,  1.44s/it]
 72%|███████▏  | 2833/3910 [1:08:25<26:00,  1.45s/it]
 72%|███████▏  | 2834/3910 [1:08:26<25:36,  1.43s/it]
 73%|███████▎  | 2835/3910 [1:08:27<25:36,  1.43s/it]
 73%|███████▎  | 2836/3910 [1:08:29<25:53,  1.45s/it]
 73%|███████▎  | 2837/3910 [1:08:30<25:56,  1.45s/it]
 73%|███████▎  | 2838/3910 [1:08:32<25:55,  1.45s/it]
 73%|███████▎  | 2839/3910 [1:08:33<25:34,  1.43s/it]
 73%|███████▎  | 2840/3910 [1:08:35<26:35,  1.49s/it]
 73%|███████▎  | 2841/3910 [1:08:36<26:31,  1.49s/it]
 73%|███████▎  | 2842/3910 [1:08:38<26:06,  1.47s/it]
 73%|███████▎  | 2843/3910 [1:08:39<25:57,  1.46s/it]
 73%|███████▎  | 2844/3910 [1:08:41<26:03,  1.47s/it]
 73%|███████▎  | 2845/3910 [1:08:42<26:11,  1.48s/it]
 73%|███████▎  | 2846/3910 [1:08:44<25:56,  1.46s/it]
 73%|███████▎  | 2847/3910 [1:08:45<25:58,  1.47s/it]
 73%|███████▎  | 2848/3910 [1:08:46<25:35,  1.45s/it]
 73%|███████▎  | 2849/3910 [1:08:48<25:28,  1.44s/it]
 73%|███████▎  | 2850/3910 [1:08:49<25:22,  1.44s/it]
                                                     

 73%|███████▎  | 2850/3910 [1:08:49<25:22,  1.44s/it]
 73%|███████▎  | 2851/3910 [1:08:51<25:35,  1.45s/it]
 73%|███████▎  | 2852/3910 [1:08:52<25:48,  1.46s/it]
 73%|███████▎  | 2853/3910 [1:08:54<25:48,  1.46s/it]
 73%|███████▎  | 2854/3910 [1:08:55<25:21,  1.44s/it]
 73%|███████▎  | 2855/3910 [1:08:57<25:36,  1.46s/it]
 73%|███████▎  | 2856/3910 [1:08:58<25:22,  1.44s/it]
 73%|███████▎  | 2857/3910 [1:08:59<25:29,  1.45s/it]
 73%|███████▎  | 2858/3910 [1:09:01<25:39,  1.46s/it]
 73%|███████▎  | 2859/3910 [1:09:02<25:31,  1.46s/it]
 73%|███████▎  | 2860/3910 [1:09:04<25:19,  1.45s/it]
 73%|███████▎  | 2861/3910 [1:09:05<25:15,  1.44s/it]
 73%|███████▎  | 2862/3910 [1:09:07<25:27,  1.46s/it]
 73%|███████▎  | 2863/3910 [1:09:08<25:06,  1.44s/it]
 73%|███████▎  | 2864/3910 [1:09:10<25:13,  1.45s/it]
 73%|███████▎  | 2865/3910 [1:09:11<25:18,  1.45s/it]
 73%|███████▎  | 2866/3910 [1:09:13<25:14,  1.45s/it]
 73%|███████▎  | 2867/3910 [1:09:14<25:16,  1.45s/it]
 73%|███████▎  | 2868/3910 [1:09:15<25:07,  1.45s/it]
 73%|███████▎  | 2869/3910 [1:09:17<25:00,  1.44s/it]
 73%|███████▎  | 2870/3910 [1:09:18<24:59,  1.44s/it]
 73%|███████▎  | 2871/3910 [1:09:20<25:12,  1.46s/it]
 73%|███████▎  | 2872/3910 [1:09:21<25:21,  1.47s/it]
 73%|███████▎  | 2873/3910 [1:09:23<25:22,  1.47s/it]
 74%|███████▎  | 2874/3910 [1:09:24<25:12,  1.46s/it]
 74%|███████▎  | 2875/3910 [1:09:26<24:44,  1.43s/it]
                                                     

 74%|███████▎  | 2875/3910 [1:09:26<24:44,  1.43s/it]
 74%|███████▎  | 2876/3910 [1:09:27<24:37,  1.43s/it]
 74%|███████▎  | 2877/3910 [1:09:28<24:21,  1.41s/it]
 74%|███████▎  | 2878/3910 [1:09:30<24:39,  1.43s/it]
 74%|███████▎  | 2879/3910 [1:09:31<24:22,  1.42s/it]
 74%|███████▎  | 2880/3910 [1:09:33<24:41,  1.44s/it]
 74%|███████▎  | 2881/3910 [1:09:34<24:46,  1.44s/it]
 74%|███████▎  | 2882/3910 [1:09:36<24:21,  1.42s/it]
 74%|███████▎  | 2883/3910 [1:09:37<24:21,  1.42s/it]
 74%|███████▍  | 2884/3910 [1:09:38<24:17,  1.42s/it]
 74%|███████▍  | 2885/3910 [1:09:40<24:37,  1.44s/it]
 74%|███████▍  | 2886/3910 [1:09:41<24:44,  1.45s/it]
 74%|███████▍  | 2887/3910 [1:09:43<24:37,  1.44s/it]
 74%|███████▍  | 2888/3910 [1:09:44<24:43,  1.45s/it]
 74%|███████▍  | 2889/3910 [1:09:46<24:43,  1.45s/it]
 74%|███████▍  | 2890/3910 [1:09:47<24:22,  1.43s/it]
 74%|███████▍  | 2891/3910 [1:09:48<24:07,  1.42s/it]
 74%|███████▍  | 2892/3910 [1:09:50<24:00,  1.42s/it]
 74%|███████▍  | 2893/3910 [1:09:51<24:02,  1.42s/it]
 74%|███████▍  | 2894/3910 [1:09:53<24:01,  1.42s/it]
 74%|███████▍  | 2895/3910 [1:09:54<24:17,  1.44s/it]
 74%|███████▍  | 2896/3910 [1:09:56<24:15,  1.44s/it]
 74%|███████▍  | 2897/3910 [1:09:57<24:22,  1.44s/it]
 74%|███████▍  | 2898/3910 [1:09:59<24:19,  1.44s/it]
 74%|███████▍  | 2899/3910 [1:10:00<24:30,  1.45s/it]
 74%|███████▍  | 2900/3910 [1:10:01<24:40,  1.47s/it]
                                                     

 74%|███████▍  | 2900/3910 [1:10:02<24:40,  1.47s/it]
 74%|███████▍  | 2901/3910 [1:10:03<24:37,  1.46s/it]
 74%|███████▍  | 2902/3910 [1:10:04<24:30,  1.46s/it]
 74%|███████▍  | 2903/3910 [1:10:06<24:36,  1.47s/it]
 74%|███████▍  | 2904/3910 [1:10:07<24:25,  1.46s/it]
 74%|███████▍  | 2905/3910 [1:10:09<24:14,  1.45s/it]
 74%|███████▍  | 2906/3910 [1:10:10<24:24,  1.46s/it]
 74%|███████▍  | 2907/3910 [1:10:12<24:18,  1.45s/it]
 74%|███████▍  | 2908/3910 [1:10:13<24:22,  1.46s/it]
 74%|███████▍  | 2909/3910 [1:10:15<24:32,  1.47s/it]
 74%|███████▍  | 2910/3910 [1:10:16<24:22,  1.46s/it]
 74%|███████▍  | 2911/3910 [1:10:18<24:14,  1.46s/it]
 74%|███████▍  | 2912/3910 [1:10:19<24:20,  1.46s/it]
 75%|███████▍  | 2913/3910 [1:10:20<24:16,  1.46s/it]
 75%|███████▍  | 2914/3910 [1:10:22<24:06,  1.45s/it]
 75%|███████▍  | 2915/3910 [1:10:23<23:54,  1.44s/it]
 75%|███████▍  | 2916/3910 [1:10:25<23:48,  1.44s/it]
 75%|███████▍  | 2917/3910 [1:10:26<23:42,  1.43s/it]
 75%|███████▍  | 2918/3910 [1:10:28<23:49,  1.44s/it]
 75%|███████▍  | 2919/3910 [1:10:29<24:54,  1.51s/it]
 75%|███████▍  | 2920/3910 [1:10:31<24:28,  1.48s/it]
 75%|███████▍  | 2921/3910 [1:10:32<24:05,  1.46s/it]
 75%|███████▍  | 2922/3910 [1:10:34<24:10,  1.47s/it]
 75%|███████▍  | 2923/3910 [1:10:35<23:38,  1.44s/it]
 75%|███████▍  | 2924/3910 [1:10:36<23:45,  1.45s/it]
 75%|███████▍  | 2925/3910 [1:10:38<23:29,  1.43s/it]
                                                     

 75%|███████▍  | 2925/3910 [1:10:38<23:29,  1.43s/it]
 75%|███████▍  | 2926/3910 [1:10:39<23:36,  1.44s/it]
 75%|███████▍  | 2927/3910 [1:10:41<23:44,  1.45s/it]
 75%|███████▍  | 2928/3910 [1:10:42<23:44,  1.45s/it]
 75%|███████▍  | 2929/3910 [1:10:44<23:55,  1.46s/it]
 75%|███████▍  | 2930/3910 [1:10:45<23:55,  1.47s/it]
 75%|███████▍  | 2931/3910 [1:10:47<23:41,  1.45s/it]
 75%|███████▍  | 2932/3910 [1:10:48<23:40,  1.45s/it]
 75%|███████▌  | 2933/3910 [1:10:50<23:48,  1.46s/it]
 75%|███████▌  | 2934/3910 [1:10:51<23:48,  1.46s/it]
 75%|███████▌  | 2935/3910 [1:10:52<23:52,  1.47s/it]
 75%|███████▌  | 2936/3910 [1:10:54<23:56,  1.47s/it]
 75%|███████▌  | 2937/3910 [1:10:55<23:32,  1.45s/it]
 75%|███████▌  | 2938/3910 [1:10:57<23:21,  1.44s/it]
 75%|███████▌  | 2939/3910 [1:10:58<23:28,  1.45s/it]
 75%|███████▌  | 2940/3910 [1:11:00<23:02,  1.43s/it]
 75%|███████▌  | 2941/3910 [1:11:01<22:55,  1.42s/it]
 75%|███████▌  | 2942/3910 [1:11:03<23:14,  1.44s/it]
 75%|███████▌  | 2943/3910 [1:11:04<23:25,  1.45s/it]
 75%|███████▌  | 2944/3910 [1:11:05<23:20,  1.45s/it]
 75%|███████▌  | 2945/3910 [1:11:07<23:17,  1.45s/it]
 75%|███████▌  | 2946/3910 [1:11:08<23:02,  1.43s/it]
 75%|███████▌  | 2947/3910 [1:11:10<23:33,  1.47s/it]
 75%|███████▌  | 2948/3910 [1:11:11<23:37,  1.47s/it]
 75%|███████▌  | 2949/3910 [1:11:13<23:35,  1.47s/it]
 75%|███████▌  | 2950/3910 [1:11:14<23:01,  1.44s/it]
                                                     

 75%|███████▌  | 2950/3910 [1:11:14<23:01,  1.44s/it]
 75%|███████▌  | 2951/3910 [1:11:16<23:01,  1.44s/it]
 75%|███████▌  | 2952/3910 [1:11:17<23:00,  1.44s/it]
 76%|███████▌  | 2953/3910 [1:11:19<23:13,  1.46s/it]
 76%|███████▌  | 2954/3910 [1:11:20<22:52,  1.44s/it]
 76%|███████▌  | 2955/3910 [1:11:21<23:04,  1.45s/it]
 76%|███████▌  | 2956/3910 [1:11:23<23:16,  1.46s/it]
 76%|███████▌  | 2957/3910 [1:11:24<22:55,  1.44s/it]
 76%|███████▌  | 2958/3910 [1:11:26<22:53,  1.44s/it]
 76%|███████▌  | 2959/3910 [1:11:27<23:04,  1.46s/it]
 76%|███████▌  | 2960/3910 [1:11:29<23:10,  1.46s/it]
 76%|███████▌  | 2961/3910 [1:11:30<23:01,  1.46s/it]
 76%|███████▌  | 2962/3910 [1:11:32<22:52,  1.45s/it]
 76%|███████▌  | 2963/3910 [1:11:33<23:05,  1.46s/it]
 76%|███████▌  | 2964/3910 [1:11:35<23:04,  1.46s/it]
 76%|███████▌  | 2965/3910 [1:11:36<23:10,  1.47s/it]
 76%|███████▌  | 2966/3910 [1:11:38<23:14,  1.48s/it]
 76%|███████▌  | 2967/3910 [1:11:39<23:00,  1.46s/it]
 76%|███████▌  | 2968/3910 [1:11:40<23:06,  1.47s/it]
 76%|███████▌  | 2969/3910 [1:11:42<23:11,  1.48s/it]
 76%|███████▌  | 2970/3910 [1:11:43<23:16,  1.49s/it]
 76%|███████▌  | 2971/3910 [1:11:45<23:11,  1.48s/it]
 76%|███████▌  | 2972/3910 [1:11:46<22:58,  1.47s/it]
 76%|███████▌  | 2973/3910 [1:11:48<23:05,  1.48s/it]
 76%|███████▌  | 2974/3910 [1:11:49<22:58,  1.47s/it]
 76%|███████▌  | 2975/3910 [1:11:51<22:46,  1.46s/it]
                                                     

 76%|███████▌  | 2975/3910 [1:11:51<22:46,  1.46s/it]
 76%|███████▌  | 2976/3910 [1:11:52<22:26,  1.44s/it]
 76%|███████▌  | 2977/3910 [1:11:54<22:38,  1.46s/it]
 76%|███████▌  | 2978/3910 [1:11:55<22:29,  1.45s/it]
 76%|███████▌  | 2979/3910 [1:11:57<22:35,  1.46s/it]
 76%|███████▌  | 2980/3910 [1:11:58<22:23,  1.44s/it]
 76%|███████▌  | 2981/3910 [1:11:59<22:22,  1.45s/it]
 76%|███████▋  | 2982/3910 [1:12:01<22:29,  1.45s/it]
 76%|███████▋  | 2983/3910 [1:12:02<22:37,  1.46s/it]
 76%|███████▋  | 2984/3910 [1:12:04<22:37,  1.47s/it]
 76%|███████▋  | 2985/3910 [1:12:05<22:39,  1.47s/it]
 76%|███████▋  | 2986/3910 [1:12:07<22:14,  1.44s/it]
 76%|███████▋  | 2987/3910 [1:12:08<22:20,  1.45s/it]
 76%|███████▋  | 2988/3910 [1:12:10<22:15,  1.45s/it]
 76%|███████▋  | 2989/3910 [1:12:11<22:22,  1.46s/it]
 76%|███████▋  | 2990/3910 [1:12:13<22:18,  1.46s/it]
 76%|███████▋  | 2991/3910 [1:12:14<22:22,  1.46s/it]
 77%|███████▋  | 2992/3910 [1:12:15<21:56,  1.43s/it]
 77%|███████▋  | 2993/3910 [1:12:17<22:03,  1.44s/it]
 77%|███████▋  | 2994/3910 [1:12:18<22:02,  1.44s/it]
 77%|███████▋  | 2995/3910 [1:12:20<22:11,  1.46s/it]
 77%|███████▋  | 2996/3910 [1:12:21<22:12,  1.46s/it]
 77%|███████▋  | 2997/3910 [1:12:23<22:13,  1.46s/it]
 77%|███████▋  | 2998/3910 [1:12:24<22:06,  1.45s/it]
 77%|███████▋  | 2999/3910 [1:12:26<22:01,  1.45s/it]
 77%|███████▋  | 3000/3910 [1:12:27<22:11,  1.46s/it]
                                                     

 77%|███████▋  | 3000/3910 [1:12:27<22:11,  1.46s/it]
 77%|███████▋  | 3001/3910 [1:12:29<22:01,  1.45s/it]
 77%|███████▋  | 3002/3910 [1:12:30<22:11,  1.47s/it]
 77%|███████▋  | 3003/3910 [1:12:31<22:10,  1.47s/it]
 77%|███████▋  | 3004/3910 [1:12:33<22:05,  1.46s/it]
 77%|███████▋  | 3005/3910 [1:12:34<21:56,  1.45s/it]
 77%|███████▋  | 3006/3910 [1:12:36<22:02,  1.46s/it]
 77%|███████▋  | 3007/3910 [1:12:37<21:31,  1.43s/it]
 77%|███████▋  | 3008/3910 [1:12:39<21:43,  1.45s/it]
 77%|███████▋  | 3009/3910 [1:12:40<21:51,  1.46s/it]
 77%|███████▋  | 3010/3910 [1:12:42<21:36,  1.44s/it]
 77%|███████▋  | 3011/3910 [1:12:43<21:47,  1.45s/it]
 77%|███████▋  | 3012/3910 [1:12:45<21:44,  1.45s/it]
 77%|███████▋  | 3013/3910 [1:12:46<21:20,  1.43s/it]
 77%|███████▋  | 3014/3910 [1:12:47<20:54,  1.40s/it]
 77%|███████▋  | 3015/3910 [1:12:49<21:12,  1.42s/it]
 77%|███████▋  | 3016/3910 [1:12:50<21:25,  1.44s/it]
 77%|███████▋  | 3017/3910 [1:12:52<21:39,  1.46s/it]
 77%|███████▋  | 3018/3910 [1:12:53<21:39,  1.46s/it]
 77%|███████▋  | 3019/3910 [1:12:55<21:27,  1.45s/it]
 77%|███████▋  | 3020/3910 [1:12:56<21:25,  1.44s/it]
 77%|███████▋  | 3021/3910 [1:12:57<21:30,  1.45s/it]
 77%|███████▋  | 3022/3910 [1:12:59<22:07,  1.50s/it]
 77%|███████▋  | 3023/3910 [1:13:01<22:05,  1.49s/it]
 77%|███████▋  | 3024/3910 [1:13:02<22:01,  1.49s/it]
 77%|███████▋  | 3025/3910 [1:13:03<21:53,  1.48s/it]
                                                     

 77%|███████▋  | 3025/3910 [1:13:03<21:53,  1.48s/it]
 77%|███████▋  | 3026/3910 [1:13:05<21:51,  1.48s/it]
 77%|███████▋  | 3027/3910 [1:13:06<21:49,  1.48s/it]
 77%|███████▋  | 3028/3910 [1:13:08<21:47,  1.48s/it]
 77%|███████▋  | 3029/3910 [1:13:09<21:37,  1.47s/it]
 77%|███████▋  | 3030/3910 [1:13:11<21:40,  1.48s/it]
 78%|███████▊  | 3031/3910 [1:13:13<22:31,  1.54s/it]
 78%|███████▊  | 3032/3910 [1:13:14<21:55,  1.50s/it]
 78%|███████▊  | 3033/3910 [1:13:15<21:52,  1.50s/it]
 78%|███████▊  | 3034/3910 [1:13:17<21:13,  1.45s/it]
 78%|███████▊  | 3035/3910 [1:13:18<21:12,  1.45s/it]
 78%|███████▊  | 3036/3910 [1:13:20<21:19,  1.46s/it]
 78%|███████▊  | 3037/3910 [1:13:21<21:18,  1.46s/it]
 78%|███████▊  | 3038/3910 [1:13:23<20:52,  1.44s/it]
 78%|███████▊  | 3039/3910 [1:13:24<20:30,  1.41s/it]
 78%|███████▊  | 3040/3910 [1:13:25<20:17,  1.40s/it]
 78%|███████▊  | 3041/3910 [1:13:27<20:35,  1.42s/it]
 78%|███████▊  | 3042/3910 [1:13:28<20:44,  1.43s/it]
 78%|███████▊  | 3043/3910 [1:13:30<20:52,  1.44s/it]
 78%|███████▊  | 3044/3910 [1:13:31<20:58,  1.45s/it]
 78%|███████▊  | 3045/3910 [1:13:33<20:47,  1.44s/it]
 78%|███████▊  | 3046/3910 [1:13:34<20:45,  1.44s/it]
 78%|███████▊  | 3047/3910 [1:13:36<20:50,  1.45s/it]
 78%|███████▊  | 3048/3910 [1:13:37<20:46,  1.45s/it]
 78%|███████▊  | 3049/3910 [1:13:38<20:55,  1.46s/it]
 78%|███████▊  | 3050/3910 [1:13:40<20:42,  1.45s/it]
                                                     

 78%|███████▊  | 3050/3910 [1:13:40<20:42,  1.45s/it]
 78%|███████▊  | 3051/3910 [1:13:41<20:50,  1.46s/it]
 78%|███████▊  | 3052/3910 [1:13:43<20:52,  1.46s/it]
 78%|███████▊  | 3053/3910 [1:13:44<20:43,  1.45s/it]
 78%|███████▊  | 3054/3910 [1:13:46<20:45,  1.45s/it]
 78%|███████▊  | 3055/3910 [1:13:47<20:27,  1.44s/it]
 78%|███████▊  | 3056/3910 [1:13:49<20:26,  1.44s/it]
 78%|███████▊  | 3057/3910 [1:13:50<20:07,  1.42s/it]
 78%|███████▊  | 3058/3910 [1:13:51<20:22,  1.43s/it]
 78%|███████▊  | 3059/3910 [1:13:53<20:09,  1.42s/it]
 78%|███████▊  | 3060/3910 [1:13:54<20:07,  1.42s/it]
 78%|███████▊  | 3061/3910 [1:13:56<20:12,  1.43s/it]
 78%|███████▊  | 3062/3910 [1:13:57<20:25,  1.45s/it]
 78%|███████▊  | 3063/3910 [1:13:58<19:55,  1.41s/it]
 78%|███████▊  | 3064/3910 [1:14:00<20:11,  1.43s/it]
 78%|███████▊  | 3065/3910 [1:14:01<20:23,  1.45s/it]
 78%|███████▊  | 3066/3910 [1:14:03<20:18,  1.44s/it]
 78%|███████▊  | 3067/3910 [1:14:04<20:21,  1.45s/it]
 78%|███████▊  | 3068/3910 [1:14:06<20:30,  1.46s/it]
 78%|███████▊  | 3069/3910 [1:14:07<20:27,  1.46s/it]
 79%|███████▊  | 3070/3910 [1:14:09<20:12,  1.44s/it]
 79%|███████▊  | 3071/3910 [1:14:10<20:17,  1.45s/it]
 79%|███████▊  | 3072/3910 [1:14:12<20:13,  1.45s/it]
 79%|███████▊  | 3073/3910 [1:14:13<20:10,  1.45s/it]
 79%|███████▊  | 3074/3910 [1:14:14<20:19,  1.46s/it]
 79%|███████▊  | 3075/3910 [1:14:16<20:25,  1.47s/it]
                                                     

 79%|███████▊  | 3075/3910 [1:14:16<20:25,  1.47s/it]
 79%|███████▊  | 3076/3910 [1:14:17<19:48,  1.43s/it]
 79%|███████▊  | 3077/3910 [1:14:19<19:44,  1.42s/it]
 79%|███████▊  | 3078/3910 [1:14:20<19:58,  1.44s/it]
 79%|███████▊  | 3079/3910 [1:14:22<19:54,  1.44s/it]
 79%|███████▉  | 3080/3910 [1:14:23<19:35,  1.42s/it]
 79%|███████▉  | 3081/3910 [1:14:24<19:28,  1.41s/it]
 79%|███████▉  | 3082/3910 [1:14:26<19:42,  1.43s/it]
 79%|███████▉  | 3083/3910 [1:14:27<19:44,  1.43s/it]
 79%|███████▉  | 3084/3910 [1:14:29<19:51,  1.44s/it]
 79%|███████▉  | 3085/3910 [1:14:30<19:57,  1.45s/it]
 79%|███████▉  | 3086/3910 [1:14:32<19:33,  1.42s/it]
 79%|███████▉  | 3087/3910 [1:14:33<19:45,  1.44s/it]
 79%|███████▉  | 3088/3910 [1:14:35<19:48,  1.45s/it]
 79%|███████▉  | 3089/3910 [1:14:36<19:54,  1.45s/it]
 79%|███████▉  | 3090/3910 [1:14:37<19:52,  1.45s/it]
 79%|███████▉  | 3091/3910 [1:14:39<20:01,  1.47s/it]
 79%|███████▉  | 3092/3910 [1:14:40<19:52,  1.46s/it]
 79%|███████▉  | 3093/3910 [1:14:42<19:43,  1.45s/it]
 79%|███████▉  | 3094/3910 [1:14:43<19:38,  1.44s/it]
 79%|███████▉  | 3095/3910 [1:14:45<19:22,  1.43s/it]
 79%|███████▉  | 3096/3910 [1:14:46<19:32,  1.44s/it]
 79%|███████▉  | 3097/3910 [1:14:48<19:28,  1.44s/it]
 79%|███████▉  | 3098/3910 [1:14:49<19:35,  1.45s/it]
 79%|███████▉  | 3099/3910 [1:14:50<19:18,  1.43s/it]
 79%|███████▉  | 3100/3910 [1:14:52<19:19,  1.43s/it]
                                                     

 79%|███████▉  | 3100/3910 [1:14:52<19:19,  1.43s/it]
 79%|███████▉  | 3101/3910 [1:14:53<19:29,  1.45s/it]
 79%|███████▉  | 3102/3910 [1:14:55<19:28,  1.45s/it]
 79%|███████▉  | 3103/3910 [1:14:56<19:22,  1.44s/it]
 79%|███████▉  | 3104/3910 [1:14:58<19:08,  1.42s/it]
 79%|███████▉  | 3105/3910 [1:14:59<19:11,  1.43s/it]
 79%|███████▉  | 3106/3910 [1:15:01<19:19,  1.44s/it]
 79%|███████▉  | 3107/3910 [1:15:02<19:30,  1.46s/it]
 79%|███████▉  | 3108/3910 [1:15:04<20:14,  1.51s/it]
 80%|███████▉  | 3109/3910 [1:15:05<19:49,  1.49s/it]
 80%|███████▉  | 3110/3910 [1:15:07<19:49,  1.49s/it]
 80%|███████▉  | 3111/3910 [1:15:08<19:42,  1.48s/it]
 80%|███████▉  | 3112/3910 [1:15:09<19:34,  1.47s/it]
 80%|███████▉  | 3113/3910 [1:15:11<19:37,  1.48s/it]
 80%|███████▉  | 3114/3910 [1:15:12<19:33,  1.47s/it]
 80%|███████▉  | 3115/3910 [1:15:14<19:25,  1.47s/it]
 80%|███████▉  | 3116/3910 [1:15:15<19:29,  1.47s/it]
 80%|███████▉  | 3117/3910 [1:15:17<19:33,  1.48s/it]
 80%|███████▉  | 3118/3910 [1:15:18<19:28,  1.47s/it]
 80%|███████▉  | 3119/3910 [1:15:20<19:26,  1.47s/it]
 80%|███████▉  | 3120/3910 [1:15:21<19:09,  1.46s/it]
 80%|███████▉  | 3121/3910 [1:15:23<18:59,  1.44s/it]
 80%|███████▉  | 3122/3910 [1:15:24<18:34,  1.41s/it]
 80%|███████▉  | 3123/3910 [1:15:25<18:26,  1.41s/it]
 80%|███████▉  | 3124/3910 [1:15:27<18:43,  1.43s/it]
 80%|███████▉  | 3125/3910 [1:15:28<18:39,  1.43s/it]
                                                     

 80%|███████▉  | 3125/3910 [1:15:28<18:39,  1.43s/it]
 80%|███████▉  | 3126/3910 [1:15:30<18:36,  1.42s/it]
 80%|███████▉  | 3127/3910 [1:15:31<18:49,  1.44s/it]
 80%|████████  | 3128/3910 [1:15:33<19:00,  1.46s/it]
 80%|████████  | 3129/3910 [1:15:34<18:58,  1.46s/it]
 80%|████████  | 3130/3910 [1:15:36<19:04,  1.47s/it]
 80%|████████  | 3131/3910 [1:15:37<18:57,  1.46s/it]
 80%|████████  | 3132/3910 [1:15:38<18:47,  1.45s/it]
 80%|████████  | 3133/3910 [1:15:40<18:45,  1.45s/it]
 80%|████████  | 3134/3910 [1:15:41<18:36,  1.44s/it]
 80%|████████  | 3135/3910 [1:15:43<18:47,  1.45s/it]
 80%|████████  | 3136/3910 [1:15:44<18:37,  1.44s/it]
 80%|████████  | 3137/3910 [1:15:46<18:25,  1.43s/it]
 80%|████████  | 3138/3910 [1:15:47<18:33,  1.44s/it]
 80%|████████  | 3139/3910 [1:15:49<18:42,  1.46s/it]
 80%|████████  | 3140/3910 [1:15:50<18:22,  1.43s/it]
 80%|████████  | 3141/3910 [1:15:51<17:57,  1.40s/it]
 80%|████████  | 3142/3910 [1:15:53<17:53,  1.40s/it]
 80%|████████  | 3143/3910 [1:15:54<18:08,  1.42s/it]
 80%|████████  | 3144/3910 [1:15:56<18:20,  1.44s/it]
 80%|████████  | 3145/3910 [1:15:57<18:23,  1.44s/it]
 80%|████████  | 3146/3910 [1:15:59<18:32,  1.46s/it]
 80%|████████  | 3147/3910 [1:16:00<18:28,  1.45s/it]
 81%|████████  | 3148/3910 [1:16:01<18:17,  1.44s/it]
 81%|████████  | 3149/3910 [1:16:03<18:15,  1.44s/it]
 81%|████████  | 3150/3910 [1:16:04<18:19,  1.45s/it]
                                                     

 81%|████████  | 3150/3910 [1:16:04<18:19,  1.45s/it]
 81%|████████  | 3151/3910 [1:16:06<18:26,  1.46s/it]
 81%|████████  | 3152/3910 [1:16:07<18:27,  1.46s/it]
 81%|████████  | 3153/3910 [1:16:09<18:16,  1.45s/it]
 81%|████████  | 3154/3910 [1:16:10<18:01,  1.43s/it]
 81%|████████  | 3155/3910 [1:16:11<17:45,  1.41s/it]
 81%|████████  | 3156/3910 [1:16:13<17:56,  1.43s/it]
 81%|████████  | 3157/3910 [1:16:14<17:58,  1.43s/it]
 81%|████████  | 3158/3910 [1:16:16<17:38,  1.41s/it]
 81%|████████  | 3159/3910 [1:16:17<17:18,  1.38s/it]
 81%|████████  | 3160/3910 [1:16:19<17:36,  1.41s/it]
 81%|████████  | 3161/3910 [1:16:20<17:52,  1.43s/it]
 81%|████████  | 3162/3910 [1:16:21<18:01,  1.45s/it]
 81%|████████  | 3163/3910 [1:16:23<18:04,  1.45s/it]
 81%|████████  | 3164/3910 [1:16:24<17:49,  1.43s/it]
 81%|████████  | 3165/3910 [1:16:26<17:44,  1.43s/it]
 81%|████████  | 3166/3910 [1:16:27<17:54,  1.44s/it]
 81%|████████  | 3167/3910 [1:16:29<17:53,  1.44s/it]
 81%|████████  | 3168/3910 [1:16:30<17:59,  1.46s/it]
 81%|████████  | 3169/3910 [1:16:32<17:40,  1.43s/it]
 81%|████████  | 3170/3910 [1:16:33<17:51,  1.45s/it]
 81%|████████  | 3171/3910 [1:16:35<17:55,  1.46s/it]
 81%|████████  | 3172/3910 [1:16:36<17:55,  1.46s/it]
 81%|████████  | 3173/3910 [1:16:37<17:44,  1.44s/it]
 81%|████████  | 3174/3910 [1:16:39<17:51,  1.46s/it]
 81%|████████  | 3175/3910 [1:16:40<17:42,  1.45s/it]
                                                     

 81%|████████  | 3175/3910 [1:16:40<17:42,  1.45s/it]
 81%|████████  | 3176/3910 [1:16:42<17:47,  1.45s/it]
 81%|████████▏ | 3177/3910 [1:16:43<17:53,  1.46s/it]
 81%|████████▏ | 3178/3910 [1:16:45<17:39,  1.45s/it]
 81%|████████▏ | 3179/3910 [1:16:46<17:45,  1.46s/it]
 81%|████████▏ | 3180/3910 [1:16:48<17:52,  1.47s/it]
 81%|████████▏ | 3181/3910 [1:16:49<17:29,  1.44s/it]
 81%|████████▏ | 3182/3910 [1:16:50<17:35,  1.45s/it]
 81%|████████▏ | 3183/3910 [1:16:52<17:26,  1.44s/it]
 81%|████████▏ | 3184/3910 [1:16:53<17:36,  1.46s/it]
 81%|████████▏ | 3185/3910 [1:16:55<17:18,  1.43s/it]
 81%|████████▏ | 3186/3910 [1:16:56<17:16,  1.43s/it]
 82%|████████▏ | 3187/3910 [1:16:58<17:24,  1.44s/it]
 82%|████████▏ | 3188/3910 [1:16:59<17:19,  1.44s/it]
 82%|████████▏ | 3189/3910 [1:17:01<17:26,  1.45s/it]
 82%|████████▏ | 3190/3910 [1:17:02<17:22,  1.45s/it]
 82%|████████▏ | 3191/3910 [1:17:04<17:30,  1.46s/it]
 82%|████████▏ | 3192/3910 [1:17:05<17:11,  1.44s/it]
 82%|████████▏ | 3193/3910 [1:17:06<17:17,  1.45s/it]
 82%|████████▏ | 3194/3910 [1:17:08<17:24,  1.46s/it]
 82%|████████▏ | 3195/3910 [1:17:09<17:15,  1.45s/it]
 82%|████████▏ | 3196/3910 [1:17:11<17:06,  1.44s/it]
 82%|████████▏ | 3197/3910 [1:17:12<17:16,  1.45s/it]
 82%|████████▏ | 3198/3910 [1:17:14<17:12,  1.45s/it]
 82%|████████▏ | 3199/3910 [1:17:15<17:11,  1.45s/it]
 82%|████████▏ | 3200/3910 [1:17:17<17:14,  1.46s/it]
                                                     

 82%|████████▏ | 3200/3910 [1:17:17<17:14,  1.46s/it]
 82%|████████▏ | 3201/3910 [1:17:18<17:05,  1.45s/it]
 82%|████████▏ | 3202/3910 [1:17:19<17:02,  1.44s/it]
 82%|████████▏ | 3203/3910 [1:17:21<16:59,  1.44s/it]
 82%|████████▏ | 3204/3910 [1:17:22<16:57,  1.44s/it]
 82%|████████▏ | 3205/3910 [1:17:24<17:03,  1.45s/it]
 82%|████████▏ | 3206/3910 [1:17:25<16:53,  1.44s/it]
 82%|████████▏ | 3207/3910 [1:17:27<16:45,  1.43s/it]
 82%|████████▏ | 3208/3910 [1:17:28<16:46,  1.43s/it]
 82%|████████▏ | 3209/3910 [1:17:29<16:46,  1.44s/it]
 82%|████████▏ | 3210/3910 [1:17:31<16:50,  1.44s/it]
 82%|████████▏ | 3211/3910 [1:17:32<16:55,  1.45s/it]
 82%|████████▏ | 3212/3910 [1:17:34<16:49,  1.45s/it]
 82%|████████▏ | 3213/3910 [1:17:35<17:24,  1.50s/it]
 82%|████████▏ | 3214/3910 [1:17:37<16:59,  1.46s/it]
 82%|████████▏ | 3215/3910 [1:17:38<16:47,  1.45s/it]
 82%|████████▏ | 3216/3910 [1:17:40<16:49,  1.45s/it]
 82%|████████▏ | 3217/3910 [1:17:41<16:49,  1.46s/it]
 82%|████████▏ | 3218/3910 [1:17:43<16:54,  1.47s/it]
 82%|████████▏ | 3219/3910 [1:17:44<16:49,  1.46s/it]
 82%|████████▏ | 3220/3910 [1:17:46<16:40,  1.45s/it]
 82%|████████▏ | 3221/3910 [1:17:47<16:27,  1.43s/it]
 82%|████████▏ | 3222/3910 [1:17:48<16:35,  1.45s/it]
 82%|████████▏ | 3223/3910 [1:17:50<16:32,  1.44s/it]
 82%|████████▏ | 3224/3910 [1:17:51<16:24,  1.43s/it]
 82%|████████▏ | 3225/3910 [1:17:53<16:28,  1.44s/it]
                                                     

 82%|████████▏ | 3225/3910 [1:17:53<16:28,  1.44s/it]
 83%|████████▎ | 3226/3910 [1:17:54<16:23,  1.44s/it]
 83%|████████▎ | 3227/3910 [1:17:56<16:33,  1.45s/it]
 83%|████████▎ | 3228/3910 [1:17:57<16:28,  1.45s/it]
 83%|████████▎ | 3229/3910 [1:17:59<16:21,  1.44s/it]
 83%|████████▎ | 3230/3910 [1:18:00<16:00,  1.41s/it]
 83%|████████▎ | 3231/3910 [1:18:01<16:07,  1.43s/it]
 83%|████████▎ | 3232/3910 [1:18:03<16:08,  1.43s/it]
 83%|████████▎ | 3233/3910 [1:18:04<16:17,  1.44s/it]
 83%|████████▎ | 3234/3910 [1:18:06<16:17,  1.45s/it]
 83%|████████▎ | 3235/3910 [1:18:07<16:00,  1.42s/it]
 83%|████████▎ | 3236/3910 [1:18:08<15:48,  1.41s/it]
 83%|████████▎ | 3237/3910 [1:18:10<16:02,  1.43s/it]
 83%|████████▎ | 3238/3910 [1:18:11<15:57,  1.42s/it]
 83%|████████▎ | 3239/3910 [1:18:13<15:58,  1.43s/it]
 83%|████████▎ | 3240/3910 [1:18:14<16:09,  1.45s/it]
 83%|████████▎ | 3241/3910 [1:18:16<15:53,  1.43s/it]
 83%|████████▎ | 3242/3910 [1:18:17<16:00,  1.44s/it]
 83%|████████▎ | 3243/3910 [1:18:19<15:59,  1.44s/it]
 83%|████████▎ | 3244/3910 [1:18:20<15:59,  1.44s/it]
 83%|████████▎ | 3245/3910 [1:18:21<15:57,  1.44s/it]
 83%|████████▎ | 3246/3910 [1:18:23<15:58,  1.44s/it]
 83%|████████▎ | 3247/3910 [1:18:24<16:04,  1.45s/it]
 83%|████████▎ | 3248/3910 [1:18:26<15:51,  1.44s/it]
 83%|████████▎ | 3249/3910 [1:18:27<15:56,  1.45s/it]
 83%|████████▎ | 3250/3910 [1:18:29<15:54,  1.45s/it]
                                                     

 83%|████████▎ | 3250/3910 [1:18:29<15:54,  1.45s/it]
 83%|████████▎ | 3251/3910 [1:18:30<15:57,  1.45s/it]
 83%|████████▎ | 3252/3910 [1:18:32<16:02,  1.46s/it]
 83%|████████▎ | 3253/3910 [1:18:33<15:54,  1.45s/it]
 83%|████████▎ | 3254/3910 [1:18:34<15:54,  1.46s/it]
 83%|████████▎ | 3255/3910 [1:18:36<15:50,  1.45s/it]
 83%|████████▎ | 3256/3910 [1:18:37<15:52,  1.46s/it]
 83%|████████▎ | 3257/3910 [1:18:39<15:51,  1.46s/it]
 83%|████████▎ | 3258/3910 [1:18:40<15:48,  1.45s/it]
 83%|████████▎ | 3259/3910 [1:18:42<15:48,  1.46s/it]
 83%|████████▎ | 3260/3910 [1:18:43<15:39,  1.45s/it]
 83%|████████▎ | 3261/3910 [1:18:45<15:44,  1.46s/it]
 83%|████████▎ | 3262/3910 [1:18:46<15:38,  1.45s/it]
 83%|████████▎ | 3263/3910 [1:18:48<15:32,  1.44s/it]
 83%|████████▎ | 3264/3910 [1:18:49<15:35,  1.45s/it]
 84%|████████▎ | 3265/3910 [1:18:50<15:40,  1.46s/it]
 84%|████████▎ | 3266/3910 [1:18:52<15:40,  1.46s/it]
 84%|████████▎ | 3267/3910 [1:18:53<15:35,  1.46s/it]
 84%|████████▎ | 3268/3910 [1:18:55<15:31,  1.45s/it]
 84%|████████▎ | 3269/3910 [1:18:56<15:36,  1.46s/it]
 84%|████████▎ | 3270/3910 [1:18:58<15:41,  1.47s/it]
 84%|████████▎ | 3271/3910 [1:18:59<15:30,  1.46s/it]
 84%|████████▎ | 3272/3910 [1:19:01<15:25,  1.45s/it]
 84%|████████▎ | 3273/3910 [1:19:02<15:18,  1.44s/it]
 84%|████████▎ | 3274/3910 [1:19:04<15:20,  1.45s/it]
 84%|████████▍ | 3275/3910 [1:19:05<15:09,  1.43s/it]
                                                     

 84%|████████▍ | 3275/3910 [1:19:05<15:09,  1.43s/it]
 84%|████████▍ | 3276/3910 [1:19:06<15:11,  1.44s/it]
 84%|████████▍ | 3277/3910 [1:19:08<15:12,  1.44s/it]
 84%|████████▍ | 3278/3910 [1:19:09<15:04,  1.43s/it]
 84%|████████▍ | 3279/3910 [1:19:11<15:05,  1.43s/it]
 84%|████████▍ | 3280/3910 [1:19:12<15:01,  1.43s/it]
 84%|████████▍ | 3281/3910 [1:19:14<15:07,  1.44s/it]
 84%|████████▍ | 3282/3910 [1:19:15<15:15,  1.46s/it]
 84%|████████▍ | 3283/3910 [1:19:16<15:07,  1.45s/it]
 84%|████████▍ | 3284/3910 [1:19:18<15:10,  1.45s/it]
 84%|████████▍ | 3285/3910 [1:19:19<14:58,  1.44s/it]
 84%|████████▍ | 3286/3910 [1:19:21<14:57,  1.44s/it]
 84%|████████▍ | 3287/3910 [1:19:22<15:01,  1.45s/it]
 84%|████████▍ | 3288/3910 [1:19:24<15:08,  1.46s/it]
 84%|████████▍ | 3289/3910 [1:19:25<15:08,  1.46s/it]
 84%|████████▍ | 3290/3910 [1:19:27<15:03,  1.46s/it]
 84%|████████▍ | 3291/3910 [1:19:28<15:01,  1.46s/it]
 84%|████████▍ | 3292/3910 [1:19:30<15:06,  1.47s/it]
 84%|████████▍ | 3293/3910 [1:19:31<15:10,  1.48s/it]
 84%|████████▍ | 3294/3910 [1:19:33<15:08,  1.47s/it]
 84%|████████▍ | 3295/3910 [1:19:34<14:57,  1.46s/it]
 84%|████████▍ | 3296/3910 [1:19:36<15:01,  1.47s/it]
 84%|████████▍ | 3297/3910 [1:19:37<14:53,  1.46s/it]
 84%|████████▍ | 3298/3910 [1:19:38<14:49,  1.45s/it]
 84%|████████▍ | 3299/3910 [1:19:40<14:51,  1.46s/it]
 84%|████████▍ | 3300/3910 [1:19:42<15:24,  1.52s/it]
                                                     

 84%|████████▍ | 3300/3910 [1:19:42<15:24,  1.52s/it]
 84%|████████▍ | 3301/3910 [1:19:43<15:05,  1.49s/it]
 84%|████████▍ | 3302/3910 [1:19:44<15:05,  1.49s/it]
 84%|████████▍ | 3303/3910 [1:19:46<15:01,  1.49s/it]
 85%|████████▍ | 3304/3910 [1:19:47<14:55,  1.48s/it]
 85%|████████▍ | 3305/3910 [1:19:49<14:55,  1.48s/it]
 85%|████████▍ | 3306/3910 [1:19:50<14:54,  1.48s/it]
 85%|████████▍ | 3307/3910 [1:19:52<14:41,  1.46s/it]
 85%|████████▍ | 3308/3910 [1:19:53<14:40,  1.46s/it]
 85%|████████▍ | 3309/3910 [1:19:55<14:43,  1.47s/it]
 85%|████████▍ | 3310/3910 [1:19:56<14:33,  1.46s/it]
 85%|████████▍ | 3311/3910 [1:19:58<14:37,  1.47s/it]
 85%|████████▍ | 3312/3910 [1:19:59<14:35,  1.46s/it]
 85%|████████▍ | 3313/3910 [1:20:00<14:20,  1.44s/it]
 85%|████████▍ | 3314/3910 [1:20:02<14:15,  1.44s/it]
 85%|████████▍ | 3315/3910 [1:20:03<14:13,  1.43s/it]
 85%|████████▍ | 3316/3910 [1:20:05<14:09,  1.43s/it]
 85%|████████▍ | 3317/3910 [1:20:06<14:10,  1.43s/it]
 85%|████████▍ | 3318/3910 [1:20:08<14:14,  1.44s/it]
 85%|████████▍ | 3319/3910 [1:20:09<14:56,  1.52s/it]
 85%|████████▍ | 3320/3910 [1:20:11<14:46,  1.50s/it]
 85%|████████▍ | 3321/3910 [1:20:12<14:34,  1.48s/it]
 85%|████████▍ | 3322/3910 [1:20:14<14:17,  1.46s/it]
 85%|████████▍ | 3323/3910 [1:20:15<14:22,  1.47s/it]
 85%|████████▌ | 3324/3910 [1:20:17<14:24,  1.48s/it]
 85%|████████▌ | 3325/3910 [1:20:18<14:23,  1.48s/it]
                                                     

 85%|████████▌ | 3325/3910 [1:20:18<14:23,  1.48s/it]
 85%|████████▌ | 3326/3910 [1:20:19<14:02,  1.44s/it]
 85%|████████▌ | 3327/3910 [1:20:21<14:07,  1.45s/it]
 85%|████████▌ | 3328/3910 [1:20:22<14:12,  1.47s/it]
 85%|████████▌ | 3329/3910 [1:20:24<14:09,  1.46s/it]
 85%|████████▌ | 3330/3910 [1:20:25<14:01,  1.45s/it]
 85%|████████▌ | 3331/3910 [1:20:27<14:02,  1.45s/it]
 85%|████████▌ | 3332/3910 [1:20:28<13:58,  1.45s/it]
 85%|████████▌ | 3333/3910 [1:20:30<14:02,  1.46s/it]
 85%|████████▌ | 3334/3910 [1:20:31<14:00,  1.46s/it]
 85%|████████▌ | 3335/3910 [1:20:33<13:43,  1.43s/it]
 85%|████████▌ | 3336/3910 [1:20:34<13:53,  1.45s/it]
 85%|████████▌ | 3337/3910 [1:20:36<14:00,  1.47s/it]
 85%|████████▌ | 3338/3910 [1:20:37<13:55,  1.46s/it]
 85%|████████▌ | 3339/3910 [1:20:38<13:45,  1.45s/it]
 85%|████████▌ | 3340/3910 [1:20:40<13:41,  1.44s/it]
 85%|████████▌ | 3341/3910 [1:20:41<13:48,  1.46s/it]
 85%|████████▌ | 3342/3910 [1:20:43<13:40,  1.45s/it]
 85%|████████▌ | 3343/3910 [1:20:44<13:30,  1.43s/it]
 86%|████████▌ | 3344/3910 [1:20:46<13:28,  1.43s/it]
 86%|████████▌ | 3345/3910 [1:20:47<13:37,  1.45s/it]
 86%|████████▌ | 3346/3910 [1:20:48<13:24,  1.43s/it]
 86%|████████▌ | 3347/3910 [1:20:50<13:25,  1.43s/it]
 86%|████████▌ | 3348/3910 [1:20:51<13:20,  1.42s/it]
 86%|████████▌ | 3349/3910 [1:20:53<13:29,  1.44s/it]
 86%|████████▌ | 3350/3910 [1:20:54<13:36,  1.46s/it]
                                                     

 86%|████████▌ | 3350/3910 [1:20:54<13:36,  1.46s/it]
 86%|████████▌ | 3351/3910 [1:20:56<13:39,  1.47s/it]
 86%|████████▌ | 3352/3910 [1:20:57<13:34,  1.46s/it]
 86%|████████▌ | 3353/3910 [1:20:59<13:30,  1.46s/it]
 86%|████████▌ | 3354/3910 [1:21:00<13:29,  1.46s/it]
 86%|████████▌ | 3355/3910 [1:21:02<13:26,  1.45s/it]
 86%|████████▌ | 3356/3910 [1:21:03<13:27,  1.46s/it]
 86%|████████▌ | 3357/3910 [1:21:04<13:22,  1.45s/it]
 86%|████████▌ | 3358/3910 [1:21:06<13:15,  1.44s/it]
 86%|████████▌ | 3359/3910 [1:21:07<13:05,  1.43s/it]
 86%|████████▌ | 3360/3910 [1:21:09<13:05,  1.43s/it]
 86%|████████▌ | 3361/3910 [1:21:10<13:13,  1.45s/it]
 86%|████████▌ | 3362/3910 [1:21:12<13:11,  1.44s/it]
 86%|████████▌ | 3363/3910 [1:21:13<13:18,  1.46s/it]
 86%|████████▌ | 3364/3910 [1:21:15<13:19,  1.46s/it]
 86%|████████▌ | 3365/3910 [1:21:16<13:13,  1.46s/it]
 86%|████████▌ | 3366/3910 [1:21:17<13:05,  1.44s/it]
 86%|████████▌ | 3367/3910 [1:21:19<13:05,  1.45s/it]
 86%|████████▌ | 3368/3910 [1:21:20<13:00,  1.44s/it]
 86%|████████▌ | 3369/3910 [1:21:22<12:54,  1.43s/it]
 86%|████████▌ | 3370/3910 [1:21:23<12:46,  1.42s/it]
 86%|████████▌ | 3371/3910 [1:21:25<12:49,  1.43s/it]
 86%|████████▌ | 3372/3910 [1:21:26<12:45,  1.42s/it]
 86%|████████▋ | 3373/3910 [1:21:27<12:52,  1.44s/it]
 86%|████████▋ | 3374/3910 [1:21:29<12:44,  1.43s/it]
 86%|████████▋ | 3375/3910 [1:21:30<12:38,  1.42s/it]
                                                     

 86%|████████▋ | 3375/3910 [1:21:30<12:38,  1.42s/it]
 86%|████████▋ | 3376/3910 [1:21:32<12:47,  1.44s/it]
 86%|████████▋ | 3377/3910 [1:21:33<12:55,  1.45s/it]
 86%|████████▋ | 3378/3910 [1:21:35<12:56,  1.46s/it]
 86%|████████▋ | 3379/3910 [1:21:36<12:49,  1.45s/it]
 86%|████████▋ | 3380/3910 [1:21:37<12:41,  1.44s/it]
 86%|████████▋ | 3381/3910 [1:21:39<12:46,  1.45s/it]
 86%|████████▋ | 3382/3910 [1:21:40<12:45,  1.45s/it]
 87%|████████▋ | 3383/3910 [1:21:42<12:42,  1.45s/it]
 87%|████████▋ | 3384/3910 [1:21:43<12:45,  1.45s/it]
 87%|████████▋ | 3385/3910 [1:21:45<12:47,  1.46s/it]
 87%|████████▋ | 3386/3910 [1:21:46<12:49,  1.47s/it]
 87%|████████▋ | 3387/3910 [1:21:48<12:51,  1.48s/it]
 87%|████████▋ | 3388/3910 [1:21:49<12:33,  1.44s/it]
 87%|████████▋ | 3389/3910 [1:21:51<12:36,  1.45s/it]
 87%|████████▋ | 3390/3910 [1:21:52<12:38,  1.46s/it]
 87%|████████▋ | 3391/3910 [1:21:54<12:34,  1.45s/it]
 87%|████████▋ | 3392/3910 [1:21:55<13:06,  1.52s/it]
 87%|████████▋ | 3393/3910 [1:21:57<12:54,  1.50s/it]
 87%|████████▋ | 3394/3910 [1:21:58<12:48,  1.49s/it]
 87%|████████▋ | 3395/3910 [1:22:00<12:43,  1.48s/it]
 87%|████████▋ | 3396/3910 [1:22:01<12:44,  1.49s/it]
 87%|████████▋ | 3397/3910 [1:22:03<12:35,  1.47s/it]
 87%|████████▋ | 3398/3910 [1:22:04<12:18,  1.44s/it]
 87%|████████▋ | 3399/3910 [1:22:05<12:16,  1.44s/it]
 87%|████████▋ | 3400/3910 [1:22:07<12:05,  1.42s/it]
                                                     

 87%|████████▋ | 3400/3910 [1:22:07<12:05,  1.42s/it]
 87%|████████▋ | 3401/3910 [1:22:08<12:03,  1.42s/it]
 87%|████████▋ | 3402/3910 [1:22:10<12:03,  1.42s/it]
 87%|████████▋ | 3403/3910 [1:22:11<12:05,  1.43s/it]
 87%|████████▋ | 3404/3910 [1:22:12<11:52,  1.41s/it]
 87%|████████▋ | 3405/3910 [1:22:14<12:04,  1.43s/it]
 87%|████████▋ | 3406/3910 [1:22:15<11:55,  1.42s/it]
 87%|████████▋ | 3407/3910 [1:22:17<12:23,  1.48s/it]
 87%|████████▋ | 3408/3910 [1:22:18<12:18,  1.47s/it]
 87%|████████▋ | 3409/3910 [1:22:20<11:59,  1.44s/it]
 87%|████████▋ | 3410/3910 [1:22:21<11:49,  1.42s/it]
 87%|████████▋ | 3411/3910 [1:22:22<11:43,  1.41s/it]
 87%|████████▋ | 3412/3910 [1:22:24<11:51,  1.43s/it]
 87%|████████▋ | 3413/3910 [1:22:25<11:48,  1.43s/it]
 87%|████████▋ | 3414/3910 [1:22:27<11:52,  1.44s/it]
 87%|████████▋ | 3415/3910 [1:22:28<11:34,  1.40s/it]
 87%|████████▋ | 3416/3910 [1:22:30<11:41,  1.42s/it]
 87%|████████▋ | 3417/3910 [1:22:31<11:43,  1.43s/it]
 87%|████████▋ | 3418/3910 [1:22:32<11:45,  1.43s/it]
 87%|████████▋ | 3419/3910 [1:22:34<11:51,  1.45s/it]
 87%|████████▋ | 3420/3910 [1:22:35<11:45,  1.44s/it]
 87%|████████▋ | 3421/3910 [1:22:37<11:44,  1.44s/it]
 88%|████████▊ | 3422/3910 [1:22:38<11:49,  1.45s/it]
 88%|████████▊ | 3423/3910 [1:22:40<11:46,  1.45s/it]
 88%|████████▊ | 3424/3910 [1:22:41<11:51,  1.46s/it]
 88%|████████▊ | 3425/3910 [1:22:43<11:37,  1.44s/it]
                                                     

 88%|████████▊ | 3425/3910 [1:22:43<11:37,  1.44s/it]
 88%|████████▊ | 3426/3910 [1:22:44<11:44,  1.45s/it]
 88%|████████▊ | 3427/3910 [1:22:46<11:48,  1.47s/it]
 88%|████████▊ | 3428/3910 [1:22:47<11:44,  1.46s/it]
 88%|████████▊ | 3429/3910 [1:22:49<11:47,  1.47s/it]
 88%|████████▊ | 3430/3910 [1:22:50<11:42,  1.46s/it]
 88%|████████▊ | 3431/3910 [1:22:51<11:37,  1.46s/it]
 88%|████████▊ | 3432/3910 [1:22:53<11:40,  1.47s/it]
 88%|████████▊ | 3433/3910 [1:22:54<11:32,  1.45s/it]
 88%|████████▊ | 3434/3910 [1:22:56<11:15,  1.42s/it]
 88%|████████▊ | 3435/3910 [1:22:57<11:16,  1.42s/it]
 88%|████████▊ | 3436/3910 [1:22:59<11:18,  1.43s/it]
 88%|████████▊ | 3437/3910 [1:23:00<11:17,  1.43s/it]
 88%|████████▊ | 3438/3910 [1:23:01<11:17,  1.44s/it]
 88%|████████▊ | 3439/3910 [1:23:03<11:18,  1.44s/it]
 88%|████████▊ | 3440/3910 [1:23:04<11:17,  1.44s/it]
 88%|████████▊ | 3441/3910 [1:23:06<11:11,  1.43s/it]
 88%|████████▊ | 3442/3910 [1:23:07<11:10,  1.43s/it]
 88%|████████▊ | 3443/3910 [1:23:09<10:59,  1.41s/it]
 88%|████████▊ | 3444/3910 [1:23:10<11:03,  1.42s/it]
 88%|████████▊ | 3445/3910 [1:23:11<11:08,  1.44s/it]
 88%|████████▊ | 3446/3910 [1:23:13<11:07,  1.44s/it]
 88%|████████▊ | 3447/3910 [1:23:14<11:06,  1.44s/it]
 88%|████████▊ | 3448/3910 [1:23:16<11:05,  1.44s/it]
 88%|████████▊ | 3449/3910 [1:23:17<11:08,  1.45s/it]
 88%|████████▊ | 3450/3910 [1:23:19<11:01,  1.44s/it]
                                                     

 88%|████████▊ | 3450/3910 [1:23:19<11:01,  1.44s/it]
 88%|████████▊ | 3451/3910 [1:23:20<11:07,  1.45s/it]
 88%|████████▊ | 3452/3910 [1:23:22<11:07,  1.46s/it]
 88%|████████▊ | 3453/3910 [1:23:23<11:08,  1.46s/it]
 88%|████████▊ | 3454/3910 [1:23:25<11:00,  1.45s/it]
 88%|████████▊ | 3455/3910 [1:23:26<10:58,  1.45s/it]
 88%|████████▊ | 3456/3910 [1:23:27<10:49,  1.43s/it]
 88%|████████▊ | 3457/3910 [1:23:29<10:56,  1.45s/it]
 88%|████████▊ | 3458/3910 [1:23:30<11:00,  1.46s/it]
 88%|████████▊ | 3459/3910 [1:23:32<11:01,  1.47s/it]
 88%|████████▊ | 3460/3910 [1:23:33<10:55,  1.46s/it]
 89%|████████▊ | 3461/3910 [1:23:35<10:55,  1.46s/it]
 89%|████████▊ | 3462/3910 [1:23:36<10:49,  1.45s/it]
 89%|████████▊ | 3463/3910 [1:23:38<10:45,  1.44s/it]
 89%|████████▊ | 3464/3910 [1:23:39<10:45,  1.45s/it]
 89%|████████▊ | 3465/3910 [1:23:41<10:46,  1.45s/it]
 89%|████████▊ | 3466/3910 [1:23:42<10:46,  1.46s/it]
 89%|████████▊ | 3467/3910 [1:23:43<10:35,  1.43s/it]
 89%|████████▊ | 3468/3910 [1:23:45<10:35,  1.44s/it]
 89%|████████▊ | 3469/3910 [1:23:46<10:40,  1.45s/it]
 89%|████████▊ | 3470/3910 [1:23:48<10:34,  1.44s/it]
 89%|████████▉ | 3471/3910 [1:23:49<10:25,  1.43s/it]
 89%|████████▉ | 3472/3910 [1:23:51<10:33,  1.45s/it]
 89%|████████▉ | 3473/3910 [1:23:52<10:30,  1.44s/it]
 89%|████████▉ | 3474/3910 [1:23:53<10:27,  1.44s/it]
 89%|████████▉ | 3475/3910 [1:23:55<10:29,  1.45s/it]
                                                     

 89%|████████▉ | 3475/3910 [1:23:55<10:29,  1.45s/it]
 89%|████████▉ | 3476/3910 [1:23:56<10:30,  1.45s/it]
 89%|████████▉ | 3477/3910 [1:23:58<10:19,  1.43s/it]
 89%|████████▉ | 3478/3910 [1:23:59<10:25,  1.45s/it]
 89%|████████▉ | 3479/3910 [1:24:01<10:30,  1.46s/it]
 89%|████████▉ | 3480/3910 [1:24:02<10:29,  1.46s/it]
 89%|████████▉ | 3481/3910 [1:24:04<10:32,  1.47s/it]
 89%|████████▉ | 3482/3910 [1:24:05<10:58,  1.54s/it]
 89%|████████▉ | 3483/3910 [1:24:07<10:41,  1.50s/it]
 89%|████████▉ | 3484/3910 [1:24:08<10:36,  1.49s/it]
 89%|████████▉ | 3485/3910 [1:24:10<10:24,  1.47s/it]
 89%|████████▉ | 3486/3910 [1:24:11<10:26,  1.48s/it]
 89%|████████▉ | 3487/3910 [1:24:13<10:15,  1.45s/it]
 89%|████████▉ | 3488/3910 [1:24:14<10:16,  1.46s/it]
 89%|████████▉ | 3489/3910 [1:24:16<10:12,  1.46s/it]
 89%|████████▉ | 3490/3910 [1:24:17<10:04,  1.44s/it]
 89%|████████▉ | 3491/3910 [1:24:18<09:50,  1.41s/it]
 89%|████████▉ | 3492/3910 [1:24:20<09:53,  1.42s/it]
 89%|████████▉ | 3493/3910 [1:24:21<09:56,  1.43s/it]
 89%|████████▉ | 3494/3910 [1:24:23<09:57,  1.44s/it]
 89%|████████▉ | 3495/3910 [1:24:24<09:50,  1.42s/it]
 89%|████████▉ | 3496/3910 [1:24:25<09:53,  1.43s/it]
 89%|████████▉ | 3497/3910 [1:24:27<09:55,  1.44s/it]
 89%|████████▉ | 3498/3910 [1:24:28<09:54,  1.44s/it]
 89%|████████▉ | 3499/3910 [1:24:30<09:55,  1.45s/it]
 90%|████████▉ | 3500/3910 [1:24:31<09:56,  1.45s/it]
                                                     

 90%|████████▉ | 3500/3910 [1:24:31<09:56,  1.45s/it]
 90%|████████▉ | 3501/3910 [1:24:33<09:54,  1.45s/it]
 90%|████████▉ | 3502/3910 [1:24:34<09:53,  1.46s/it]
 90%|████████▉ | 3503/3910 [1:24:36<09:55,  1.46s/it]
 90%|████████▉ | 3504/3910 [1:24:37<09:44,  1.44s/it]
 90%|████████▉ | 3505/3910 [1:24:39<09:49,  1.46s/it]
 90%|████████▉ | 3506/3910 [1:24:40<09:48,  1.46s/it]
 90%|████████▉ | 3507/3910 [1:24:42<09:49,  1.46s/it]
 90%|████████▉ | 3508/3910 [1:24:43<09:40,  1.44s/it]
 90%|████████▉ | 3509/3910 [1:24:44<09:42,  1.45s/it]
 90%|████████▉ | 3510/3910 [1:24:46<09:34,  1.44s/it]
 90%|████████▉ | 3511/3910 [1:24:47<09:35,  1.44s/it]
 90%|████████▉ | 3512/3910 [1:24:49<09:34,  1.44s/it]
 90%|████████▉ | 3513/3910 [1:24:50<09:39,  1.46s/it]
 90%|████████▉ | 3514/3910 [1:24:52<09:33,  1.45s/it]
 90%|████████▉ | 3515/3910 [1:24:53<09:32,  1.45s/it]
 90%|████████▉ | 3516/3910 [1:24:54<09:27,  1.44s/it]
 90%|████████▉ | 3517/3910 [1:24:56<09:29,  1.45s/it]
 90%|████████▉ | 3518/3910 [1:24:57<09:28,  1.45s/it]
 90%|█████████ | 3519/3910 [1:24:59<09:31,  1.46s/it]
 90%|█████████ | 3520/3910 [1:25:00<09:34,  1.47s/it]
 90%|█████████ | 3521/3910 [1:25:02<09:28,  1.46s/it]
 90%|█████████ | 3522/3910 [1:25:03<09:25,  1.46s/it]
 90%|█████████ | 3523/3910 [1:25:05<09:28,  1.47s/it]
 90%|█████████ | 3524/3910 [1:25:06<09:27,  1.47s/it]
 90%|█████████ | 3525/3910 [1:25:08<09:18,  1.45s/it]
                                                     

 90%|█████████ | 3525/3910 [1:25:08<09:18,  1.45s/it]
 90%|█████████ | 3526/3910 [1:25:09<09:22,  1.47s/it]
 90%|█████████ | 3527/3910 [1:25:11<09:21,  1.47s/it]
 90%|█████████ | 3528/3910 [1:25:12<09:22,  1.47s/it]
 90%|█████████ | 3529/3910 [1:25:14<09:23,  1.48s/it]
 90%|█████████ | 3530/3910 [1:25:15<09:22,  1.48s/it]
 90%|█████████ | 3531/3910 [1:25:17<09:17,  1.47s/it]
 90%|█████████ | 3532/3910 [1:25:18<09:17,  1.48s/it]
 90%|█████████ | 3533/3910 [1:25:19<09:08,  1.46s/it]
 90%|█████████ | 3534/3910 [1:25:21<09:04,  1.45s/it]
 90%|█████████ | 3535/3910 [1:25:22<09:03,  1.45s/it]
 90%|█████████ | 3536/3910 [1:25:24<09:06,  1.46s/it]
 90%|█████████ | 3537/3910 [1:25:25<09:00,  1.45s/it]
 90%|█████████ | 3538/3910 [1:25:27<09:03,  1.46s/it]
 91%|█████████ | 3539/3910 [1:25:28<08:57,  1.45s/it]
 91%|█████████ | 3540/3910 [1:25:30<08:54,  1.44s/it]
 91%|█████████ | 3541/3910 [1:25:31<08:51,  1.44s/it]
 91%|█████████ | 3542/3910 [1:25:32<08:55,  1.46s/it]
 91%|█████████ | 3543/3910 [1:25:34<08:58,  1.47s/it]
 91%|█████████ | 3544/3910 [1:25:35<08:57,  1.47s/it]
 91%|█████████ | 3545/3910 [1:25:37<08:49,  1.45s/it]
 91%|█████████ | 3546/3910 [1:25:38<08:42,  1.43s/it]
 91%|█████████ | 3547/3910 [1:25:40<08:40,  1.43s/it]
 91%|█████████ | 3548/3910 [1:25:41<08:44,  1.45s/it]
 91%|█████████ | 3549/3910 [1:25:43<08:38,  1.44s/it]
 91%|█████████ | 3550/3910 [1:25:44<08:34,  1.43s/it]
                                                     

 91%|█████████ | 3550/3910 [1:25:44<08:34,  1.43s/it]
 91%|█████████ | 3551/3910 [1:25:45<08:32,  1.43s/it]
 91%|█████████ | 3552/3910 [1:25:47<08:37,  1.45s/it]
 91%|█████████ | 3553/3910 [1:25:48<08:39,  1.46s/it]
 91%|█████████ | 3554/3910 [1:25:50<08:31,  1.44s/it]
 91%|█████████ | 3555/3910 [1:25:51<08:34,  1.45s/it]
 91%|█████████ | 3556/3910 [1:25:53<08:34,  1.45s/it]
 91%|█████████ | 3557/3910 [1:25:54<08:27,  1.44s/it]
 91%|█████████ | 3558/3910 [1:25:56<08:26,  1.44s/it]
 91%|█████████ | 3559/3910 [1:25:57<08:29,  1.45s/it]
 91%|█████████ | 3560/3910 [1:25:58<08:23,  1.44s/it]
 91%|█████████ | 3561/3910 [1:26:00<08:22,  1.44s/it]
 91%|█████████ | 3562/3910 [1:26:01<08:20,  1.44s/it]
 91%|█████████ | 3563/3910 [1:26:03<08:16,  1.43s/it]
 91%|█████████ | 3564/3910 [1:26:04<08:18,  1.44s/it]
 91%|█████████ | 3565/3910 [1:26:06<08:17,  1.44s/it]
 91%|█████████ | 3566/3910 [1:26:07<08:19,  1.45s/it]
 91%|█████████ | 3567/3910 [1:26:09<08:21,  1.46s/it]
 91%|█████████▏| 3568/3910 [1:26:10<08:17,  1.46s/it]
 91%|█████████▏| 3569/3910 [1:26:12<08:18,  1.46s/it]
 91%|█████████▏| 3570/3910 [1:26:13<08:11,  1.45s/it]
 91%|█████████▏| 3571/3910 [1:26:14<08:13,  1.46s/it]
 91%|█████████▏| 3572/3910 [1:26:16<08:12,  1.46s/it]
 91%|█████████▏| 3573/3910 [1:26:17<08:11,  1.46s/it]
 91%|█████████▏| 3574/3910 [1:26:19<08:09,  1.46s/it]
 91%|█████████▏| 3575/3910 [1:26:20<08:09,  1.46s/it]
                                                     

 91%|█████████▏| 3575/3910 [1:26:20<08:09,  1.46s/it]
 91%|█████████▏| 3576/3910 [1:26:22<07:59,  1.44s/it]
 91%|█████████▏| 3577/3910 [1:26:23<07:59,  1.44s/it]
 92%|█████████▏| 3578/3910 [1:26:25<08:00,  1.45s/it]
 92%|█████████▏| 3579/3910 [1:26:26<07:52,  1.43s/it]
 92%|█████████▏| 3580/3910 [1:26:27<07:57,  1.45s/it]
 92%|█████████▏| 3581/3910 [1:26:29<07:58,  1.46s/it]
 92%|█████████▏| 3582/3910 [1:26:30<07:55,  1.45s/it]
 92%|█████████▏| 3583/3910 [1:26:32<07:56,  1.46s/it]
 92%|█████████▏| 3584/3910 [1:26:33<08:13,  1.51s/it]
 92%|█████████▏| 3585/3910 [1:26:35<08:09,  1.51s/it]
 92%|█████████▏| 3586/3910 [1:26:36<07:59,  1.48s/it]
 92%|█████████▏| 3587/3910 [1:26:38<07:46,  1.44s/it]
 92%|█████████▏| 3588/3910 [1:26:39<07:34,  1.41s/it]
 92%|█████████▏| 3589/3910 [1:26:40<07:34,  1.42s/it]
 92%|█████████▏| 3590/3910 [1:26:42<07:31,  1.41s/it]
 92%|█████████▏| 3591/3910 [1:26:43<07:37,  1.43s/it]
 92%|█████████▏| 3592/3910 [1:26:45<07:36,  1.44s/it]
 92%|█████████▏| 3593/3910 [1:26:46<07:35,  1.44s/it]
 92%|█████████▏| 3594/3910 [1:26:48<07:38,  1.45s/it]
 92%|█████████▏| 3595/3910 [1:26:49<07:37,  1.45s/it]
 92%|█████████▏| 3596/3910 [1:26:51<07:38,  1.46s/it]
 92%|█████████▏| 3597/3910 [1:26:52<07:37,  1.46s/it]
 92%|█████████▏| 3598/3910 [1:26:54<07:38,  1.47s/it]
 92%|█████████▏| 3599/3910 [1:26:55<07:33,  1.46s/it]
 92%|█████████▏| 3600/3910 [1:26:57<07:34,  1.47s/it]
                                                     

 92%|█████████▏| 3600/3910 [1:26:57<07:34,  1.47s/it]
 92%|█████████▏| 3601/3910 [1:26:58<07:32,  1.46s/it]
 92%|█████████▏| 3602/3910 [1:26:59<07:31,  1.47s/it]
 92%|█████████▏| 3603/3910 [1:27:01<07:32,  1.48s/it]
 92%|█████████▏| 3604/3910 [1:27:02<07:24,  1.45s/it]
 92%|█████████▏| 3605/3910 [1:27:04<07:24,  1.46s/it]
 92%|█████████▏| 3606/3910 [1:27:05<07:21,  1.45s/it]
 92%|█████████▏| 3607/3910 [1:27:07<07:15,  1.44s/it]
 92%|█████████▏| 3608/3910 [1:27:08<07:10,  1.43s/it]
 92%|█████████▏| 3609/3910 [1:27:10<07:10,  1.43s/it]
 92%|█████████▏| 3610/3910 [1:27:11<07:09,  1.43s/it]
 92%|█████████▏| 3611/3910 [1:27:12<07:11,  1.44s/it]
 92%|█████████▏| 3612/3910 [1:27:14<07:14,  1.46s/it]
 92%|█████████▏| 3613/3910 [1:27:15<07:15,  1.47s/it]
 92%|█████████▏| 3614/3910 [1:27:17<07:13,  1.47s/it]
 92%|█████████▏| 3615/3910 [1:27:18<07:09,  1.45s/it]
 92%|█████████▏| 3616/3910 [1:27:20<07:00,  1.43s/it]
 93%|█████████▎| 3617/3910 [1:27:21<07:04,  1.45s/it]
 93%|█████████▎| 3618/3910 [1:27:23<07:04,  1.45s/it]
 93%|█████████▎| 3619/3910 [1:27:24<07:04,  1.46s/it]
 93%|█████████▎| 3620/3910 [1:27:26<07:04,  1.46s/it]
 93%|█████████▎| 3621/3910 [1:27:27<07:03,  1.46s/it]
 93%|█████████▎| 3622/3910 [1:27:28<06:59,  1.46s/it]
 93%|█████████▎| 3623/3910 [1:27:30<06:59,  1.46s/it]
 93%|█████████▎| 3624/3910 [1:27:31<06:59,  1.47s/it]
 93%|█████████▎| 3625/3910 [1:27:33<06:54,  1.45s/it]
                                                     

 93%|█████████▎| 3625/3910 [1:27:33<06:54,  1.45s/it]
 93%|█████████▎| 3626/3910 [1:27:34<06:53,  1.46s/it]
 93%|█████████▎| 3627/3910 [1:27:36<06:48,  1.44s/it]
 93%|█████████▎| 3628/3910 [1:27:37<06:50,  1.46s/it]
 93%|█████████▎| 3629/3910 [1:27:39<06:51,  1.46s/it]
 93%|█████████▎| 3630/3910 [1:27:40<06:46,  1.45s/it]
 93%|█████████▎| 3631/3910 [1:27:42<06:47,  1.46s/it]
 93%|█████████▎| 3632/3910 [1:27:43<06:43,  1.45s/it]
 93%|█████████▎| 3633/3910 [1:27:44<06:39,  1.44s/it]
 93%|█████████▎| 3634/3910 [1:27:46<06:38,  1.44s/it]
 93%|█████████▎| 3635/3910 [1:27:47<06:37,  1.44s/it]
 93%|█████████▎| 3636/3910 [1:27:49<06:39,  1.46s/it]
 93%|█████████▎| 3637/3910 [1:27:50<06:39,  1.46s/it]
 93%|█████████▎| 3638/3910 [1:27:52<06:38,  1.47s/it]
 93%|█████████▎| 3639/3910 [1:27:53<06:38,  1.47s/it]
 93%|█████████▎| 3640/3910 [1:27:55<06:33,  1.46s/it]
 93%|█████████▎| 3641/3910 [1:27:56<06:31,  1.45s/it]
 93%|█████████▎| 3642/3910 [1:27:57<06:18,  1.41s/it]
 93%|█████████▎| 3643/3910 [1:27:59<06:17,  1.41s/it]
 93%|█████████▎| 3644/3910 [1:28:00<06:14,  1.41s/it]
 93%|█████████▎| 3645/3910 [1:28:02<06:19,  1.43s/it]
 93%|█████████▎| 3646/3910 [1:28:03<06:17,  1.43s/it]
 93%|█████████▎| 3647/3910 [1:28:05<06:17,  1.44s/it]
 93%|█████████▎| 3648/3910 [1:28:06<06:16,  1.44s/it]
 93%|█████████▎| 3649/3910 [1:28:08<06:17,  1.45s/it]
 93%|█████████▎| 3650/3910 [1:28:09<06:15,  1.44s/it]
                                                     

 93%|█████████▎| 3650/3910 [1:28:09<06:15,  1.44s/it]
 93%|█████████▎| 3651/3910 [1:28:10<06:17,  1.46s/it]
 93%|█████████▎| 3652/3910 [1:28:12<06:14,  1.45s/it]
 93%|█████████▎| 3653/3910 [1:28:13<06:12,  1.45s/it]
 93%|█████████▎| 3654/3910 [1:28:15<06:10,  1.45s/it]
 93%|█████████▎| 3655/3910 [1:28:16<06:05,  1.43s/it]
 94%|█████████▎| 3656/3910 [1:28:18<06:02,  1.43s/it]
 94%|█████████▎| 3657/3910 [1:28:19<05:59,  1.42s/it]
 94%|█████████▎| 3658/3910 [1:28:20<05:59,  1.43s/it]
 94%|█████████▎| 3659/3910 [1:28:22<06:01,  1.44s/it]
 94%|█████████▎| 3660/3910 [1:28:23<06:02,  1.45s/it]
 94%|█████████▎| 3661/3910 [1:28:25<06:04,  1.46s/it]
 94%|█████████▎| 3662/3910 [1:28:26<05:59,  1.45s/it]
 94%|█████████▎| 3663/3910 [1:28:28<05:55,  1.44s/it]
 94%|█████████▎| 3664/3910 [1:28:29<05:57,  1.45s/it]
 94%|█████████▎| 3665/3910 [1:28:31<05:58,  1.46s/it]
 94%|█████████▍| 3666/3910 [1:28:32<05:50,  1.44s/it]
 94%|█████████▍| 3667/3910 [1:28:34<05:51,  1.45s/it]
 94%|█████████▍| 3668/3910 [1:28:35<05:52,  1.46s/it]
 94%|█████████▍| 3669/3910 [1:28:36<05:50,  1.45s/it]
 94%|█████████▍| 3670/3910 [1:28:38<05:47,  1.45s/it]
 94%|█████████▍| 3671/3910 [1:28:39<05:46,  1.45s/it]
 94%|█████████▍| 3672/3910 [1:28:41<05:43,  1.44s/it]
 94%|█████████▍| 3673/3910 [1:28:42<05:42,  1.44s/it]
 94%|█████████▍| 3674/3910 [1:28:44<05:58,  1.52s/it]
 94%|█████████▍| 3675/3910 [1:28:45<05:49,  1.49s/it]
                                                     

 94%|█████████▍| 3675/3910 [1:28:45<05:49,  1.49s/it]
 94%|█████████▍| 3676/3910 [1:28:47<05:44,  1.47s/it]
 94%|█████████▍| 3677/3910 [1:28:48<05:43,  1.47s/it]
 94%|█████████▍| 3678/3910 [1:28:50<05:36,  1.45s/it]
 94%|█████████▍| 3679/3910 [1:28:51<05:32,  1.44s/it]
 94%|█████████▍| 3680/3910 [1:28:52<05:28,  1.43s/it]
 94%|█████████▍| 3681/3910 [1:28:54<05:26,  1.43s/it]
 94%|█████████▍| 3682/3910 [1:28:55<05:28,  1.44s/it]
 94%|█████████▍| 3683/3910 [1:28:57<05:27,  1.44s/it]
 94%|█████████▍| 3684/3910 [1:28:58<05:28,  1.45s/it]
 94%|█████████▍| 3685/3910 [1:29:00<05:24,  1.44s/it]
 94%|█████████▍| 3686/3910 [1:29:01<05:22,  1.44s/it]
 94%|█████████▍| 3687/3910 [1:29:03<05:19,  1.43s/it]
 94%|█████████▍| 3688/3910 [1:29:04<05:21,  1.45s/it]
 94%|█████████▍| 3689/3910 [1:29:05<05:21,  1.46s/it]
 94%|█████████▍| 3690/3910 [1:29:07<05:26,  1.49s/it]
 94%|█████████▍| 3691/3910 [1:29:09<05:24,  1.48s/it]
 94%|█████████▍| 3692/3910 [1:29:10<05:20,  1.47s/it]
 94%|█████████▍| 3693/3910 [1:29:11<05:19,  1.47s/it]
 94%|█████████▍| 3694/3910 [1:29:13<05:17,  1.47s/it]
 95%|█████████▍| 3695/3910 [1:29:14<05:17,  1.47s/it]
 95%|█████████▍| 3696/3910 [1:29:16<05:16,  1.48s/it]
 95%|█████████▍| 3697/3910 [1:29:17<05:09,  1.45s/it]
 95%|█████████▍| 3698/3910 [1:29:19<05:06,  1.44s/it]
 95%|█████████▍| 3699/3910 [1:29:20<04:59,  1.42s/it]
 95%|█████████▍| 3700/3910 [1:29:21<04:56,  1.41s/it]
                                                     

 95%|█████████▍| 3700/3910 [1:29:21<04:56,  1.41s/it]
 95%|█████████▍| 3701/3910 [1:29:23<04:59,  1.43s/it]
 95%|█████████▍| 3702/3910 [1:29:24<04:55,  1.42s/it]
 95%|█████████▍| 3703/3910 [1:29:26<04:55,  1.43s/it]
 95%|█████████▍| 3704/3910 [1:29:27<04:51,  1.41s/it]
 95%|█████████▍| 3705/3910 [1:29:29<04:50,  1.42s/it]
 95%|█████████▍| 3706/3910 [1:29:30<04:53,  1.44s/it]
 95%|█████████▍| 3707/3910 [1:29:31<04:51,  1.43s/it]
 95%|█████████▍| 3708/3910 [1:29:33<04:52,  1.45s/it]
 95%|█████████▍| 3709/3910 [1:29:34<04:50,  1.45s/it]
 95%|█████████▍| 3710/3910 [1:29:36<04:43,  1.42s/it]
 95%|█████████▍| 3711/3910 [1:29:37<04:43,  1.42s/it]
 95%|█████████▍| 3712/3910 [1:29:39<04:45,  1.44s/it]
 95%|█████████▍| 3713/3910 [1:29:40<04:43,  1.44s/it]
 95%|█████████▍| 3714/3910 [1:29:42<04:42,  1.44s/it]
 95%|█████████▌| 3715/3910 [1:29:43<04:40,  1.44s/it]
 95%|█████████▌| 3716/3910 [1:29:44<04:37,  1.43s/it]
 95%|█████████▌| 3717/3910 [1:29:46<04:36,  1.43s/it]
 95%|█████████▌| 3718/3910 [1:29:47<04:35,  1.44s/it]
 95%|█████████▌| 3719/3910 [1:29:49<04:33,  1.43s/it]
 95%|█████████▌| 3720/3910 [1:29:50<04:32,  1.44s/it]
 95%|█████████▌| 3721/3910 [1:29:52<04:33,  1.45s/it]
 95%|█████████▌| 3722/3910 [1:29:53<04:29,  1.43s/it]
 95%|█████████▌| 3723/3910 [1:29:54<04:30,  1.44s/it]
 95%|█████████▌| 3724/3910 [1:29:56<04:30,  1.45s/it]
 95%|█████████▌| 3725/3910 [1:29:57<04:24,  1.43s/it]
                                                     

 95%|█████████▌| 3725/3910 [1:29:57<04:24,  1.43s/it]
 95%|█████████▌| 3726/3910 [1:29:59<04:19,  1.41s/it]
 95%|█████████▌| 3727/3910 [1:30:00<04:19,  1.42s/it]
 95%|█████████▌| 3728/3910 [1:30:02<04:21,  1.44s/it]
 95%|█████████▌| 3729/3910 [1:30:03<04:20,  1.44s/it]
 95%|█████████▌| 3730/3910 [1:30:04<04:17,  1.43s/it]
 95%|█████████▌| 3731/3910 [1:30:06<04:13,  1.42s/it]
 95%|█████████▌| 3732/3910 [1:30:07<04:12,  1.42s/it]
 95%|█████████▌| 3733/3910 [1:30:09<04:12,  1.43s/it]
 95%|█████████▌| 3734/3910 [1:30:10<04:14,  1.45s/it]
 96%|█████████▌| 3735/3910 [1:30:12<04:14,  1.46s/it]
 96%|█████████▌| 3736/3910 [1:30:13<04:10,  1.44s/it]
 96%|█████████▌| 3737/3910 [1:30:15<04:09,  1.44s/it]
 96%|█████████▌| 3738/3910 [1:30:16<04:08,  1.44s/it]
 96%|█████████▌| 3739/3910 [1:30:17<04:06,  1.44s/it]
 96%|█████████▌| 3740/3910 [1:30:19<04:07,  1.45s/it]
 96%|█████████▌| 3741/3910 [1:30:20<04:07,  1.46s/it]
 96%|█████████▌| 3742/3910 [1:30:22<04:03,  1.45s/it]
 96%|█████████▌| 3743/3910 [1:30:23<04:04,  1.46s/it]
 96%|█████████▌| 3744/3910 [1:30:25<04:02,  1.46s/it]
 96%|█████████▌| 3745/3910 [1:30:26<04:01,  1.46s/it]
 96%|█████████▌| 3746/3910 [1:30:28<03:58,  1.46s/it]
 96%|█████████▌| 3747/3910 [1:30:29<03:53,  1.43s/it]
 96%|█████████▌| 3748/3910 [1:30:30<03:47,  1.40s/it]
 96%|█████████▌| 3749/3910 [1:30:32<03:49,  1.42s/it]
 96%|█████████▌| 3750/3910 [1:30:33<03:45,  1.41s/it]
                                                     

 96%|█████████▌| 3750/3910 [1:30:33<03:45,  1.41s/it]
 96%|█████████▌| 3751/3910 [1:30:35<03:44,  1.42s/it]
 96%|█████████▌| 3752/3910 [1:30:36<03:45,  1.42s/it]
 96%|█████████▌| 3753/3910 [1:30:38<03:44,  1.43s/it]
 96%|█████████▌| 3754/3910 [1:30:39<03:40,  1.41s/it]
 96%|█████████▌| 3755/3910 [1:30:40<03:42,  1.44s/it]
 96%|█████████▌| 3756/3910 [1:30:42<03:43,  1.45s/it]
 96%|█████████▌| 3757/3910 [1:30:43<03:40,  1.44s/it]
 96%|█████████▌| 3758/3910 [1:30:45<03:41,  1.46s/it]
 96%|█████████▌| 3759/3910 [1:30:46<03:36,  1.44s/it]
 96%|█████████▌| 3760/3910 [1:30:48<03:35,  1.44s/it]
 96%|█████████▌| 3761/3910 [1:30:49<03:36,  1.45s/it]
 96%|█████████▌| 3762/3910 [1:30:51<03:33,  1.44s/it]
 96%|█████████▌| 3763/3910 [1:30:52<03:33,  1.45s/it]
 96%|█████████▋| 3764/3910 [1:30:53<03:32,  1.45s/it]
 96%|█████████▋| 3765/3910 [1:30:55<03:40,  1.52s/it]
 96%|█████████▋| 3766/3910 [1:30:57<03:37,  1.51s/it]
 96%|█████████▋| 3767/3910 [1:30:58<03:35,  1.50s/it]
 96%|█████████▋| 3768/3910 [1:31:00<03:33,  1.50s/it]
 96%|█████████▋| 3769/3910 [1:31:01<03:30,  1.50s/it]
 96%|█████████▋| 3770/3910 [1:31:03<03:25,  1.47s/it]
 96%|█████████▋| 3771/3910 [1:31:04<03:20,  1.44s/it]
 96%|█████████▋| 3772/3910 [1:31:05<03:18,  1.44s/it]
 96%|█████████▋| 3773/3910 [1:31:07<03:16,  1.43s/it]
 97%|█████████▋| 3774/3910 [1:31:08<03:16,  1.45s/it]
 97%|█████████▋| 3775/3910 [1:31:10<03:14,  1.44s/it]
                                                     

 97%|█████████▋| 3775/3910 [1:31:10<03:14,  1.44s/it]
 97%|█████████▋| 3776/3910 [1:31:11<03:13,  1.45s/it]
 97%|█████████▋| 3777/3910 [1:31:13<03:13,  1.45s/it]
 97%|█████████▋| 3778/3910 [1:31:14<03:12,  1.46s/it]
 97%|█████████▋| 3779/3910 [1:31:16<03:12,  1.47s/it]
 97%|█████████▋| 3780/3910 [1:31:17<03:19,  1.54s/it]
 97%|█████████▋| 3781/3910 [1:31:19<03:16,  1.52s/it]
 97%|█████████▋| 3782/3910 [1:31:20<03:12,  1.50s/it]
 97%|█████████▋| 3783/3910 [1:31:22<03:06,  1.47s/it]
 97%|█████████▋| 3784/3910 [1:31:23<03:04,  1.46s/it]
 97%|█████████▋| 3785/3910 [1:31:24<03:01,  1.45s/it]
 97%|█████████▋| 3786/3910 [1:31:26<03:01,  1.46s/it]
 97%|█████████▋| 3787/3910 [1:31:27<02:58,  1.45s/it]
 97%|█████████▋| 3788/3910 [1:31:29<02:57,  1.46s/it]
 97%|█████████▋| 3789/3910 [1:31:30<02:55,  1.45s/it]
 97%|█████████▋| 3790/3910 [1:31:32<02:55,  1.46s/it]
 97%|█████████▋| 3791/3910 [1:31:33<02:54,  1.47s/it]
 97%|█████████▋| 3792/3910 [1:31:35<02:50,  1.44s/it]
 97%|█████████▋| 3793/3910 [1:31:36<02:48,  1.44s/it]
 97%|█████████▋| 3794/3910 [1:31:37<02:46,  1.44s/it]
 97%|█████████▋| 3795/3910 [1:31:39<02:46,  1.45s/it]
 97%|█████████▋| 3796/3910 [1:31:40<02:45,  1.45s/it]
 97%|█████████▋| 3797/3910 [1:31:42<02:45,  1.46s/it]
 97%|█████████▋| 3798/3910 [1:31:43<02:39,  1.42s/it]
 97%|█████████▋| 3799/3910 [1:31:45<02:40,  1.44s/it]
 97%|█████████▋| 3800/3910 [1:31:46<02:38,  1.44s/it]
                                                     

 97%|█████████▋| 3800/3910 [1:31:46<02:38,  1.44s/it]
 97%|█████████▋| 3801/3910 [1:31:48<02:37,  1.44s/it]
 97%|█████████▋| 3802/3910 [1:31:49<02:35,  1.44s/it]
 97%|█████████▋| 3803/3910 [1:31:50<02:33,  1.44s/it]
 97%|█████████▋| 3804/3910 [1:31:52<02:32,  1.44s/it]
 97%|█████████▋| 3805/3910 [1:31:53<02:31,  1.44s/it]
 97%|█████████▋| 3806/3910 [1:31:55<02:30,  1.45s/it]
 97%|█████████▋| 3807/3910 [1:31:56<02:28,  1.44s/it]
 97%|█████████▋| 3808/3910 [1:31:58<02:27,  1.45s/it]
 97%|█████████▋| 3809/3910 [1:31:59<02:25,  1.44s/it]
 97%|█████████▋| 3810/3910 [1:32:01<02:24,  1.44s/it]
 97%|█████████▋| 3811/3910 [1:32:02<02:24,  1.46s/it]
 97%|█████████▋| 3812/3910 [1:32:04<02:22,  1.45s/it]
 98%|█████████▊| 3813/3910 [1:32:05<02:21,  1.45s/it]
 98%|█████████▊| 3814/3910 [1:32:06<02:18,  1.44s/it]
 98%|█████████▊| 3815/3910 [1:32:08<02:18,  1.46s/it]
 98%|█████████▊| 3816/3910 [1:32:09<02:16,  1.45s/it]
 98%|█████████▊| 3817/3910 [1:32:11<02:14,  1.45s/it]
 98%|█████████▊| 3818/3910 [1:32:12<02:13,  1.45s/it]
 98%|█████████▊| 3819/3910 [1:32:14<02:12,  1.46s/it]
 98%|█████████▊| 3820/3910 [1:32:15<02:10,  1.45s/it]
 98%|█████████▊| 3821/3910 [1:32:17<02:10,  1.46s/it]
 98%|█████████▊| 3822/3910 [1:32:18<02:05,  1.43s/it]
 98%|█████████▊| 3823/3910 [1:32:19<02:03,  1.42s/it]
 98%|█████████▊| 3824/3910 [1:32:21<02:03,  1.43s/it]
 98%|█████████▊| 3825/3910 [1:32:22<02:02,  1.45s/it]
                                                     

 98%|█████████▊| 3825/3910 [1:32:22<02:02,  1.45s/it]
 98%|█████████▊| 3826/3910 [1:32:24<02:01,  1.44s/it]
 98%|█████████▊| 3827/3910 [1:32:25<01:58,  1.43s/it]
 98%|█████████▊| 3828/3910 [1:32:27<01:57,  1.43s/it]
 98%|█████████▊| 3829/3910 [1:32:28<01:56,  1.44s/it]
 98%|█████████▊| 3830/3910 [1:32:30<01:56,  1.45s/it]
 98%|█████████▊| 3831/3910 [1:32:31<01:53,  1.44s/it]
 98%|█████████▊| 3832/3910 [1:32:32<01:50,  1.42s/it]
 98%|█████████▊| 3833/3910 [1:32:34<01:50,  1.44s/it]
 98%|█████████▊| 3834/3910 [1:32:35<01:49,  1.44s/it]
 98%|█████████▊| 3835/3910 [1:32:37<01:47,  1.43s/it]
 98%|█████████▊| 3836/3910 [1:32:38<01:47,  1.45s/it]
 98%|█████████▊| 3837/3910 [1:32:40<01:46,  1.46s/it]
 98%|█████████▊| 3838/3910 [1:32:41<01:45,  1.47s/it]
 98%|█████████▊| 3839/3910 [1:32:43<01:43,  1.45s/it]
 98%|█████████▊| 3840/3910 [1:32:44<01:42,  1.46s/it]
 98%|█████████▊| 3841/3910 [1:32:45<01:40,  1.46s/it]
 98%|█████████▊| 3842/3910 [1:32:47<01:40,  1.47s/it]
 98%|█████████▊| 3843/3910 [1:32:48<01:37,  1.46s/it]
 98%|█████████▊| 3844/3910 [1:32:50<01:34,  1.44s/it]
 98%|█████████▊| 3845/3910 [1:32:51<01:33,  1.44s/it]
 98%|█████████▊| 3846/3910 [1:32:53<01:32,  1.45s/it]
 98%|█████████▊| 3847/3910 [1:32:54<01:31,  1.45s/it]
 98%|█████████▊| 3848/3910 [1:32:56<01:30,  1.47s/it]
 98%|█████████▊| 3849/3910 [1:32:57<01:27,  1.44s/it]
 98%|█████████▊| 3850/3910 [1:32:58<01:25,  1.43s/it]
                                                     

 98%|█████████▊| 3850/3910 [1:32:58<01:25,  1.43s/it]
 98%|█████████▊| 3851/3910 [1:33:00<01:23,  1.42s/it]
 99%|█████████▊| 3852/3910 [1:33:01<01:22,  1.42s/it]
 99%|█████████▊| 3853/3910 [1:33:03<01:24,  1.49s/it]
 99%|█████████▊| 3854/3910 [1:33:04<01:22,  1.48s/it]
 99%|█████████▊| 3855/3910 [1:33:06<01:20,  1.46s/it]
 99%|█████████▊| 3856/3910 [1:33:07<01:19,  1.47s/it]
 99%|█████████▊| 3857/3910 [1:33:09<01:18,  1.47s/it]
 99%|█████████▊| 3858/3910 [1:33:10<01:16,  1.46s/it]
 99%|█████████▊| 3859/3910 [1:33:12<01:14,  1.46s/it]
 99%|█████████▊| 3860/3910 [1:33:13<01:12,  1.45s/it]
 99%|█████████▊| 3861/3910 [1:33:15<01:11,  1.46s/it]
 99%|█████████▉| 3862/3910 [1:33:16<01:10,  1.46s/it]
 99%|█████████▉| 3863/3910 [1:33:17<01:08,  1.47s/it]
 99%|█████████▉| 3864/3910 [1:33:19<01:07,  1.47s/it]
 99%|█████████▉| 3865/3910 [1:33:20<01:06,  1.48s/it]
 99%|█████████▉| 3866/3910 [1:33:22<01:04,  1.46s/it]
 99%|█████████▉| 3867/3910 [1:33:23<01:03,  1.47s/it]
 99%|█████████▉| 3868/3910 [1:33:25<01:01,  1.47s/it]
 99%|█████████▉| 3869/3910 [1:33:26<01:00,  1.47s/it]
 99%|█████████▉| 3870/3910 [1:33:28<00:58,  1.46s/it]
 99%|█████████▉| 3871/3910 [1:33:29<00:56,  1.45s/it]
 99%|█████████▉| 3872/3910 [1:33:31<00:55,  1.46s/it]
 99%|█████████▉| 3873/3910 [1:33:32<00:53,  1.45s/it]
 99%|█████████▉| 3874/3910 [1:33:34<00:51,  1.44s/it]
 99%|█████████▉| 3875/3910 [1:33:35<00:50,  1.46s/it]
                                                     

 99%|█████████▉| 3875/3910 [1:33:35<00:50,  1.46s/it]
 99%|█████████▉| 3876/3910 [1:33:36<00:49,  1.46s/it]
 99%|█████████▉| 3877/3910 [1:33:38<00:48,  1.47s/it]
 99%|█████████▉| 3878/3910 [1:33:39<00:46,  1.46s/it]
 99%|█████████▉| 3879/3910 [1:33:41<00:44,  1.44s/it]
 99%|█████████▉| 3880/3910 [1:33:42<00:43,  1.46s/it]
 99%|█████████▉| 3881/3910 [1:33:44<00:42,  1.45s/it]
 99%|█████████▉| 3882/3910 [1:33:45<00:40,  1.44s/it]
 99%|█████████▉| 3883/3910 [1:33:47<00:38,  1.44s/it]
 99%|█████████▉| 3884/3910 [1:33:48<00:36,  1.42s/it]
 99%|█████████▉| 3885/3910 [1:33:49<00:35,  1.42s/it]
 99%|█████████▉| 3886/3910 [1:33:51<00:34,  1.42s/it]
 99%|█████████▉| 3887/3910 [1:33:52<00:32,  1.43s/it]
 99%|█████████▉| 3888/3910 [1:33:54<00:31,  1.45s/it]
 99%|█████████▉| 3889/3910 [1:33:55<00:30,  1.46s/it]
 99%|█████████▉| 3890/3910 [1:33:57<00:28,  1.45s/it]
100%|█████████▉| 3891/3910 [1:33:58<00:27,  1.45s/it]
100%|█████████▉| 3892/3910 [1:34:00<00:26,  1.46s/it]
100%|█████████▉| 3893/3910 [1:34:01<00:24,  1.44s/it]
100%|█████████▉| 3894/3910 [1:34:02<00:23,  1.44s/it]
100%|█████████▉| 3895/3910 [1:34:04<00:21,  1.41s/it]
100%|█████████▉| 3896/3910 [1:34:05<00:19,  1.41s/it]
100%|█████████▉| 3897/3910 [1:34:07<00:18,  1.42s/it]
100%|█████████▉| 3898/3910 [1:34:08<00:17,  1.43s/it]
100%|█████████▉| 3899/3910 [1:34:10<00:15,  1.43s/it]
100%|█████████▉| 3900/3910 [1:34:11<00:14,  1.44s/it]
                                                     

100%|█████████▉| 3900/3910 [1:34:11<00:14,  1.44s/it]
100%|█████████▉| 3901/3910 [1:34:12<00:12,  1.44s/it]
100%|█████████▉| 3902/3910 [1:34:14<00:11,  1.41s/it]
100%|█████████▉| 3903/3910 [1:34:15<00:10,  1.44s/it]
100%|█████████▉| 3904/3910 [1:34:17<00:08,  1.43s/it]
100%|█████████▉| 3905/3910 [1:34:18<00:07,  1.44s/it]
100%|█████████▉| 3906/3910 [1:34:20<00:05,  1.42s/it]
100%|█████████▉| 3907/3910 [1:34:21<00:04,  1.42s/it]
100%|█████████▉| 3908/3910 [1:34:22<00:02,  1.43s/it]
100%|█████████▉| 3909/3910 [1:34:24<00:01,  1.44s/it]
100%|██████████| 3910/3910 [1:34:25<00:00,  1.44s/it]Saving model checkpoint to data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910
Configuration saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/config.json
Configuration saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/generation_config.json
The model is bigger than the maximum size per checkpoint (5GB) and is going to be split in 3 checkpoint shards. You can find where each parameters has been saved in the index located at data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/model.safetensors.index.json.
tokenizer config file saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/tokenizer_config.json
Special tokens file saved in data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910/special_tokens_map.json
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once


Training completed. Do not forget to share your model on huggingface.co/models =)



                                                     

100%|██████████| 3910/3910 [1:36:02<00:00,  1.44s/it]
100%|██████████| 3910/3910 [1:36:02<00:00,  1.47s/it]

***** Running Evaluation *****
  Num examples = 208
  Batch size = 1

  0%|          | 0/52 [00:00<?, ?it/s]
  4%|▍         | 2/52 [00:00<00:03, 15.86it/s]
  8%|▊         | 4/52 [00:00<00:04, 10.08it/s]
 12%|█▏        | 6/52 [00:00<00:05,  8.96it/s]
 13%|█▎        | 7/52 [00:00<00:04,  9.12it/s]
 15%|█▌        | 8/52 [00:00<00:04,  8.91it/s]
 17%|█▋        | 9/52 [00:00<00:04,  8.62it/s]
 19%|█▉        | 10/52 [00:01<00:05,  8.39it/s]
 21%|██        | 11/52 [00:01<00:04,  8.24it/s]
 23%|██▎       | 12/52 [00:01<00:04,  8.12it/s]
 25%|██▌       | 13/52 [00:01<00:04,  8.05it/s]
 27%|██▋       | 14/52 [00:01<00:04,  8.02it/s]
 29%|██▉       | 15/52 [00:01<00:04,  8.09it/s]
 31%|███       | 16/52 [00:01<00:04,  8.01it/s]
 33%|███▎      | 17/52 [00:01<00:04,  7.96it/s]
 35%|███▍      | 18/52 [00:02<00:04,  7.92it/s]
 37%|███▋      | 19/52 [00:02<00:04,  7.92it/s]
 38%|███▊      | 20/52 [00:02<00:04,  7.89it/s]
 40%|████      | 21/52 [00:02<00:03,  8.21it/s]
 42%|████▏     | 22/52 [00:02<00:03,  8.08it/s]
 44%|████▍     | 23/52 [00:02<00:03,  8.01it/s]
 46%|████▌     | 24/52 [00:02<00:03,  8.07it/s]
 48%|████▊     | 25/52 [00:02<00:03,  8.03it/s]
 50%|█████     | 26/52 [00:03<00:03,  7.99it/s]
 52%|█████▏    | 27/52 [00:03<00:03,  7.98it/s]
 54%|█████▍    | 28/52 [00:03<00:02,  8.29it/s]
 56%|█████▌    | 29/52 [00:03<00:02,  8.18it/s]
 58%|█████▊    | 30/52 [00:03<00:02,  8.09it/s]
 60%|█████▉    | 31/52 [00:03<00:02,  8.05it/s]
 62%|██████▏   | 32/52 [00:03<00:02,  8.14it/s]
 63%|██████▎   | 33/52 [00:03<00:02,  8.04it/s]
 65%|██████▌   | 34/52 [00:04<00:02,  8.02it/s]
 67%|██████▋   | 35/52 [00:04<00:02,  8.00it/s]
 69%|██████▉   | 36/52 [00:04<00:01,  8.11it/s]
 71%|███████   | 37/52 [00:04<00:01,  8.21it/s]
 73%|███████▎  | 38/52 [00:04<00:01,  8.11it/s]
 75%|███████▌  | 39/52 [00:04<00:01,  8.03it/s]
 77%|███████▋  | 40/52 [00:04<00:01,  7.96it/s]
 79%|███████▉  | 41/52 [00:04<00:01,  7.92it/s]
 81%|████████  | 42/52 [00:05<00:01,  7.91it/s]
 83%|████████▎ | 43/52 [00:05<00:01,  8.38it/s]
 85%|████████▍ | 44/52 [00:05<00:00,  8.22it/s]
 87%|████████▋ | 45/52 [00:05<00:00,  8.22it/s]
 88%|████████▊ | 46/52 [00:05<00:00,  8.10it/s]
 90%|█████████ | 47/52 [00:05<00:00,  7.99it/s]
 92%|█████████▏| 48/52 [00:05<00:00,  7.96it/s]
 94%|█████████▍| 49/52 [00:05<00:00,  8.29it/s]
 96%|█████████▌| 50/52 [00:06<00:00,  8.25it/s]
 98%|█████████▊| 51/52 [00:06<00:00,  8.11it/s]
100%|██████████| 52/52 [00:06<00:00,  8.23it/s]Error executing job with overrides: []
Error executing job with overrides: []
Error executing job with overrides: []

100%|██████████| 52/52 [00:06<00:00,  8.23it/s]
Traceback (most recent call last):
Traceback (most recent call last):
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 208, in main
    eval_results = trainer.evaluate()
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 208, in main
    eval_results = trainer.evaluate()
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 208, in main
    eval_results = trainer.evaluate()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3999, in evaluate
    self.log(output.metrics)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3999, in evaluate
    self.log(output.metrics)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3999, in evaluate
    self.log(output.metrics)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3495, in log
    self.control = self.callback_handler.on_log(self.args, self.state, self.control, logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3495, in log
    self.control = self.callback_handler.on_log(self.args, self.state, self.control, logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3495, in log
    self.control = self.callback_handler.on_log(self.args, self.state, self.control, logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 511, in on_log
    return self.call_event("on_log", args, state, control, logs=logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 511, in on_log
    return self.call_event("on_log", args, state, control, logs=logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 511, in on_log
    return self.call_event("on_log", args, state, control, logs=logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 518, in call_event
    result = getattr(callback, event)(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 518, in call_event
    result = getattr(callback, event)(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 518, in call_event
    result = getattr(callback, event)(
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 129, in on_log
    wandb.log(eval_logs, step=state.global_step)
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 129, in on_log
    wandb.log(eval_logs, step=state.global_step)
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 129, in on_log
    wandb.log(eval_logs, step=state.global_step)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/wandb/sdk/lib/preinit.py", line 36, in preinit_wrapper
    raise wandb.Error(f"You must call wandb.init() before {name}()")
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/wandb/sdk/lib/preinit.py", line 36, in preinit_wrapper
    raise wandb.Error(f"You must call wandb.init() before {name}()")
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/wandb/sdk/lib/preinit.py", line 36, in preinit_wrapper
    raise wandb.Error(f"You must call wandb.init() before {name}()")
wandb.errors.errors.Error: You must call wandb.init() before wandb.log()
wandb.errors.errors.Error: You must call wandb.init() before wandb.log()
wandb.errors.errors.Error: You must call wandb.init() before wandb.log()

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:             dataset_test_size ▁
wandb:            dataset_train_size ▁
wandb:              dataset_val_size ▁
wandb:                     eval/loss ▁
wandb:                  eval/runtime ▁
wandb:       eval/samples_per_second ▁
wandb:         eval/steps_per_second ▁
wandb:                     eval_loss ▁
wandb:                  eval_runtime ▁
wandb:       eval_samples_per_second ▁
wandb:         eval_steps_per_second ▁
wandb:                   final_epoch ▁
wandb:               final_eval_loss ▁
wandb:            final_eval_runtime ▁
wandb: final_eval_samples_per_second ▁
wandb:   final_eval_steps_per_second ▁
wandb:              model_parameters ▁
wandb:              n_router_weights ▁
wandb:                     n_weights ▁
wandb:                   train/epoch ▁▁▁▂▂▂▂▂▂▃▃▃▃▃▄▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▇▇▇▇▇▇████
wandb:             train/global_step ▁▁▁▁▁▂▂▂▂▂▂▃▃▃▃▄▄▄▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▆▆▇▇▇██
wandb:               train/grad_norm █▅▆▅▅▅▅▄▅▄▄▄▄▄▄▄▄▃▃▃▂▃▃▃▃▃▂▂▂▂▂▂▂▁▂▁▁▁▂▁
wandb:           train/learning_rate ███▇▇▇▇▇▇▇▆▆▆▅▅▅▅▅▅▄▄▄▄▃▃▃▃▃▃▃▃▂▂▂▂▂▂▂▂▁
wandb:                    train/loss █▆▆▆▅▄▃▃▃▃▃▃▂▂▂▂▂▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:          trainable_parameters ▁
wandb:          trainable_percentage ▁
wandb: 
wandb: Run summary:
wandb:             dataset_test_size 487
wandb:            dataset_train_size 6254
wandb:              dataset_val_size 208
wandb:                     eval/loss 0.06868
wandb:                  eval/runtime 6.4586
wandb:       eval/samples_per_second 32.205
wandb:         eval/steps_per_second 8.051
wandb:                     eval_loss 0.06868
wandb:                  eval_runtime 6.4586
wandb:       eval_samples_per_second 32.205
wandb:         eval_steps_per_second 8.051
wandb:                   final_epoch 10
wandb:               final_eval_loss 0.06868
wandb:            final_eval_runtime 6.4586
wandb: final_eval_samples_per_second 32.205
wandb:   final_eval_steps_per_second 8.051
wandb:              model_parameters 7356420096
wandb:              n_router_weights 256
wandb:                     n_weights 771
wandb:                    total_flos 8.288256040475034e+17
wandb:                   train/epoch 10
wandb:             train/global_step 3910
wandb:               train/grad_norm 0.16098
wandb:           train/learning_rate 0.0
wandb:                    train/loss 0.0571
wandb:                    train_loss 0.30228
wandb:                 train_runtime 5762.4929
wandb:      train_samples_per_second 10.853
wandb:        train_steps_per_second 0.679
wandb:          trainable_parameters 1918238720
wandb:          trainable_percentage 26.07571
wandb: 
wandb: 🚀 View run mistral-drug_abuse_pubmedqau-moe at: https://wandb.ai/sarath-chandar/mistral-moe-training/runs/0wzzl6bc
wandb: ⭐️ View project at: https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: Synced 5 W&B file(s), 0 media file(s), 0 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250626_143624-0wzzl6bc/logs
[rank3]:[E626 16:28:25.838931798 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 3] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank2]:[E626 16:28:25.838933300 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 2] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank1]:[E626 16:28:25.838931818 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 1] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank3]:[E626 16:28:25.840292595 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 3] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank2]:[E626 16:28:25.840359752 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 2] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank1]:[E626 16:28:25.840477303 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 1] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank1]:[F626 16:36:25.849585884 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 1] [PG ID 0 PG GUID 0(default_pg) Rank 1] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
[rank3]:[F626 16:36:25.849586915 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 3] [PG ID 0 PG GUID 0(default_pg) Rank 3] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
[rank2]:[F626 16:36:25.849586535 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 2] [PG ID 0 PG GUID 0(default_pg) Rank 2] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
W0626 16:36:26.078000 2506579 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2506661 closing signal SIGTERM
W0626 16:36:26.081000 2506579 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2506663 closing signal SIGTERM
W0626 16:36:26.081000 2506579 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2506664 closing signal SIGTERM
E0626 16:36:26.848000 2506579 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: -6) local_rank: 1 (pid: 2506662) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
========================================================
exmp_1.py FAILED
--------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
--------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-06-26_16:36:26
  host      : cn-g012.server.mila.quebec
  rank      : 1 (local_rank: 1)
  exitcode  : -6 (pid: 2506662)
  error_file: <N/A>
  traceback : Signal 6 (SIGABRT) received by PID 2506662
========================================================
