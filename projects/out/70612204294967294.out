==================================================
LoRA Fine-tuning Configuration:
Base model: mistralai/Mistral-7B-v0.1
Dataset path: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_unsafe_drug_abuse_weapons_banned_substance.json
Output directory: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse/
Wandb project: lora-finetuning
Run name: 
==================================================
Dataset loaded: 6949 total samples
Training samples: 6254
Evaluation samples: 695
[2025-06-25 16:22:58,348] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Starting LoRA fine-tuning...
{'loss': 1.8935, 'grad_norm': 6.234731197357178, 'learning_rate': 0.00019976958525345624, 'epoch': 0.0}
{'loss': 1.4647, 'grad_norm': 1.594862461090088, 'learning_rate': 0.00019769585253456222, 'epoch': 0.05}
{'loss': 1.3602, 'grad_norm': 1.6214826107025146, 'learning_rate': 0.00019539170506912442, 'epoch': 0.09}
{'loss': 1.3275, 'grad_norm': 1.57422935962677, 'learning_rate': 0.00019308755760368663, 'epoch': 0.14}
{'loss': 1.2737, 'grad_norm': 1.5863317251205444, 'learning_rate': 0.00019078341013824886, 'epoch': 0.18}
{'loss': 1.2582, 'grad_norm': 1.5975720882415771, 'learning_rate': 0.00018847926267281107, 'epoch': 0.23}
{'loss': 1.199, 'grad_norm': 1.76157808303833, 'learning_rate': 0.00018617511520737328, 'epoch': 0.28}
{'loss': 1.1699, 'grad_norm': 1.7999874353408813, 'learning_rate': 0.00018387096774193548, 'epoch': 0.32}
{'loss': 1.1578, 'grad_norm': 1.6948373317718506, 'learning_rate': 0.0001815668202764977, 'epoch': 0.37}
{'loss': 1.1209, 'grad_norm': 1.6032253503799438, 'learning_rate': 0.0001792626728110599, 'epoch': 0.41}
{'loss': 1.102, 'grad_norm': 1.9902859926223755, 'learning_rate': 0.00017695852534562213, 'epoch': 0.46}
{'loss': 1.0786, 'grad_norm': 1.7655659914016724, 'learning_rate': 0.00017465437788018436, 'epoch': 0.51}
{'loss': 1.0593, 'grad_norm': 1.875245451927185, 'learning_rate': 0.00017235023041474657, 'epoch': 0.55}
{'loss': 1.0335, 'grad_norm': 1.9501055479049683, 'learning_rate': 0.00017004608294930878, 'epoch': 0.6}
{'loss': 1.0484, 'grad_norm': 1.9582949876785278, 'learning_rate': 0.00016774193548387098, 'epoch': 0.64}
{'loss': 1.071, 'grad_norm': 1.9045233726501465, 'learning_rate': 0.0001654377880184332, 'epoch': 0.69}
{'loss': 0.9876, 'grad_norm': 2.0465800762176514, 'learning_rate': 0.0001631336405529954, 'epoch': 0.74}
{'loss': 0.9981, 'grad_norm': 1.9675742387771606, 'learning_rate': 0.0001608294930875576, 'epoch': 0.78}
{'loss': 0.9861, 'grad_norm': 2.070966958999634, 'learning_rate': 0.00015852534562211984, 'epoch': 0.83}
{'loss': 0.9206, 'grad_norm': 2.1034274101257324, 'learning_rate': 0.00015622119815668204, 'epoch': 0.87}
{'loss': 0.9659, 'grad_norm': 2.161008358001709, 'learning_rate': 0.00015391705069124425, 'epoch': 0.92}
{'loss': 0.9034, 'grad_norm': 2.1765284538269043, 'learning_rate': 0.00015161290322580646, 'epoch': 0.97}
{'loss': 0.7634, 'grad_norm': 2.1035208702087402, 'learning_rate': 0.00014930875576036866, 'epoch': 1.01}
{'loss': 0.5987, 'grad_norm': 2.101219654083252, 'learning_rate': 0.00014700460829493087, 'epoch': 1.06}
{'loss': 0.5891, 'grad_norm': 2.491426467895508, 'learning_rate': 0.0001447004608294931, 'epoch': 1.1}
{'loss': 0.5734, 'grad_norm': 2.288226842880249, 'learning_rate': 0.0001423963133640553, 'epoch': 1.15}
{'loss': 0.5509, 'grad_norm': 2.4574058055877686, 'learning_rate': 0.00014009216589861752, 'epoch': 1.2}
{'loss': 0.5335, 'grad_norm': 2.258432388305664, 'learning_rate': 0.00013778801843317972, 'epoch': 1.24}
{'loss': 0.5679, 'grad_norm': 2.2584521770477295, 'learning_rate': 0.00013548387096774193, 'epoch': 1.29}
{'loss': 0.5457, 'grad_norm': 2.4729013442993164, 'learning_rate': 0.00013317972350230414, 'epoch': 1.33}
{'loss': 0.5135, 'grad_norm': 2.67273211479187, 'learning_rate': 0.00013087557603686637, 'epoch': 1.38}
{'loss': 0.5337, 'grad_norm': 2.5501201152801514, 'learning_rate': 0.00012857142857142858, 'epoch': 1.43}
{'loss': 0.5308, 'grad_norm': 2.581540107727051, 'learning_rate': 0.0001262672811059908, 'epoch': 1.47}
{'loss': 0.5139, 'grad_norm': 2.353632688522339, 'learning_rate': 0.00012396313364055302, 'epoch': 1.52}
{'loss': 0.4997, 'grad_norm': 2.7728238105773926, 'learning_rate': 0.00012165898617511522, 'epoch': 1.56}
{'loss': 0.4722, 'grad_norm': 2.596442222595215, 'learning_rate': 0.00011935483870967743, 'epoch': 1.61}
{'loss': 0.496, 'grad_norm': 2.523505687713623, 'learning_rate': 0.00011705069124423964, 'epoch': 1.66}
{'loss': 0.4816, 'grad_norm': 2.079328775405884, 'learning_rate': 0.00011474654377880186, 'epoch': 1.7}
{'loss': 0.4471, 'grad_norm': 2.0721147060394287, 'learning_rate': 0.00011244239631336406, 'epoch': 1.75}
{'loss': 0.461, 'grad_norm': 2.609833002090454, 'learning_rate': 0.00011013824884792627, 'epoch': 1.79}
{'loss': 0.4897, 'grad_norm': 2.6138575077056885, 'learning_rate': 0.00010783410138248849, 'epoch': 1.84}
{'loss': 0.4656, 'grad_norm': 2.584921360015869, 'learning_rate': 0.0001055299539170507, 'epoch': 1.89}
{'loss': 0.4406, 'grad_norm': 2.4103236198425293, 'learning_rate': 0.0001032258064516129, 'epoch': 1.93}
{'loss': 0.4468, 'grad_norm': 2.5012571811676025, 'learning_rate': 0.00010092165898617512, 'epoch': 1.98}
{'loss': 0.37, 'grad_norm': 2.195446491241455, 'learning_rate': 9.861751152073733e-05, 'epoch': 2.02}
{'loss': 0.2469, 'grad_norm': 2.032707929611206, 'learning_rate': 9.631336405529955e-05, 'epoch': 2.07}
{'loss': 0.2419, 'grad_norm': 2.5692272186279297, 'learning_rate': 9.400921658986176e-05, 'epoch': 2.11}
{'loss': 0.2395, 'grad_norm': 2.2111358642578125, 'learning_rate': 9.170506912442398e-05, 'epoch': 2.16}
{'loss': 0.2561, 'grad_norm': 2.7316598892211914, 'learning_rate': 8.940092165898618e-05, 'epoch': 2.21}
{'loss': 0.2367, 'grad_norm': 2.607454538345337, 'learning_rate': 8.709677419354839e-05, 'epoch': 2.25}
{'loss': 0.2219, 'grad_norm': 2.770090103149414, 'learning_rate': 8.479262672811061e-05, 'epoch': 2.3}
{'loss': 0.2314, 'grad_norm': 2.3176426887512207, 'learning_rate': 8.248847926267282e-05, 'epoch': 2.34}
{'loss': 0.2394, 'grad_norm': 2.814910411834717, 'learning_rate': 8.018433179723502e-05, 'epoch': 2.39}
{'loss': 0.2528, 'grad_norm': 2.3224668502807617, 'learning_rate': 7.788018433179723e-05, 'epoch': 2.44}
{'loss': 0.2401, 'grad_norm': 2.853928327560425, 'learning_rate': 7.557603686635945e-05, 'epoch': 2.48}
{'loss': 0.2205, 'grad_norm': 1.9425122737884521, 'learning_rate': 7.327188940092167e-05, 'epoch': 2.53}
{'loss': 0.239, 'grad_norm': 2.1718392372131348, 'learning_rate': 7.096774193548388e-05, 'epoch': 2.57}
{'loss': 0.2483, 'grad_norm': 2.2732651233673096, 'learning_rate': 6.86635944700461e-05, 'epoch': 2.62}
{'loss': 0.2469, 'grad_norm': 2.6080565452575684, 'learning_rate': 6.63594470046083e-05, 'epoch': 2.67}
{'loss': 0.233, 'grad_norm': 2.373870372772217, 'learning_rate': 6.405529953917051e-05, 'epoch': 2.71}
{'loss': 0.2566, 'grad_norm': 2.406656503677368, 'learning_rate': 6.175115207373272e-05, 'epoch': 2.76}
{'loss': 0.2316, 'grad_norm': 2.226036310195923, 'learning_rate': 5.944700460829493e-05, 'epoch': 2.8}
{'loss': 0.2231, 'grad_norm': 2.3459818363189697, 'learning_rate': 5.714285714285714e-05, 'epoch': 2.85}
{'loss': 0.2301, 'grad_norm': 2.0686240196228027, 'learning_rate': 5.4838709677419355e-05, 'epoch': 2.9}
{'loss': 0.2093, 'grad_norm': 2.1627089977264404, 'learning_rate': 5.253456221198156e-05, 'epoch': 2.94}
{'loss': 0.2165, 'grad_norm': 2.615729808807373, 'learning_rate': 5.023041474654379e-05, 'epoch': 2.99}
{'loss': 0.1522, 'grad_norm': 1.9701573848724365, 'learning_rate': 4.792626728110599e-05, 'epoch': 3.03}
{'loss': 0.1357, 'grad_norm': 2.198946952819824, 'learning_rate': 4.562211981566821e-05, 'epoch': 3.08}
{'loss': 0.143, 'grad_norm': 1.8609991073608398, 'learning_rate': 4.3317972350230415e-05, 'epoch': 3.13}
{'loss': 0.1298, 'grad_norm': 2.3042073249816895, 'learning_rate': 4.101382488479263e-05, 'epoch': 3.17}
{'loss': 0.13, 'grad_norm': 1.7188389301300049, 'learning_rate': 3.870967741935484e-05, 'epoch': 3.22}
{'loss': 0.1441, 'grad_norm': 1.9659851789474487, 'learning_rate': 3.640552995391705e-05, 'epoch': 3.26}
{'loss': 0.1275, 'grad_norm': 2.459677219390869, 'learning_rate': 3.410138248847927e-05, 'epoch': 3.31}
{'loss': 0.1355, 'grad_norm': 2.1026175022125244, 'learning_rate': 3.1797235023041475e-05, 'epoch': 3.36}
{'loss': 0.1257, 'grad_norm': 2.287853479385376, 'learning_rate': 2.9493087557603688e-05, 'epoch': 3.4}
{'loss': 0.1425, 'grad_norm': 2.369732618331909, 'learning_rate': 2.7188940092165898e-05, 'epoch': 3.45}
{'loss': 0.1339, 'grad_norm': 1.6479662656784058, 'learning_rate': 2.488479262672811e-05, 'epoch': 3.49}
{'loss': 0.1241, 'grad_norm': 2.2361879348754883, 'learning_rate': 2.258064516129032e-05, 'epoch': 3.54}
{'loss': 0.1285, 'grad_norm': 2.0438268184661865, 'learning_rate': 2.0276497695852538e-05, 'epoch': 3.59}
{'loss': 0.1263, 'grad_norm': 1.5818936824798584, 'learning_rate': 1.7972350230414748e-05, 'epoch': 3.63}
{'loss': 0.1207, 'grad_norm': 1.3734722137451172, 'learning_rate': 1.5668202764976958e-05, 'epoch': 3.68}
{'loss': 0.1299, 'grad_norm': 1.8439756631851196, 'learning_rate': 1.3364055299539171e-05, 'epoch': 3.72}
{'loss': 0.1279, 'grad_norm': 1.9470194578170776, 'learning_rate': 1.1059907834101383e-05, 'epoch': 3.77}
{'loss': 0.1271, 'grad_norm': 2.0254178047180176, 'learning_rate': 8.755760368663595e-06, 'epoch': 3.82}
{'loss': 0.1313, 'grad_norm': 2.1937849521636963, 'learning_rate': 6.451612903225806e-06, 'epoch': 3.86}
{'loss': 0.1293, 'grad_norm': 1.9068676233291626, 'learning_rate': 4.147465437788019e-06, 'epoch': 3.91}
{'loss': 0.1235, 'grad_norm': 1.5013800859451294, 'learning_rate': 1.8433179723502305e-06, 'epoch': 3.95}
{'train_runtime': 1036.1588, 'train_samples_per_second': 26.826, 'train_steps_per_second': 0.838, 'train_loss': 0.4982165293210113, 'epoch': 3.99}
Saving LoRA adapter to: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse/
LoRA fine-tuning completed!
Adapter saved in: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse/
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Wed Jun 25 16:40:19 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2335605
            GPU Utilization               : 70 %
            Memory Utilization            : 27 %
            Max memory usage              : 50158 MiB
            Time                          : 1045995 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2335605
            GPU Utilization               : 70 %
            Memory Utilization            : 26 %
            Max memory usage              : 35124 MiB
            Time                          : 1045994 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2335605
            GPU Utilization               : 70 %
            Memory Utilization            : 26 %
            Max memory usage              : 35206 MiB
            Time                          : 1045993 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2335605
            GPU Utilization               : 70 %
            Memory Utilization            : 26 %
            Max memory usage              : 34820 MiB
            Time                          : 1045992 ms
            Is Running                    : 0

Wed Jun 25 16:40:20 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   41C    P0             110W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   37C    P0             119W / 500W |      0MiB / 81920MiB |     90%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   42C    P0             126W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   36C    P0             123W / 500W |      0MiB / 81920MiB |    100%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
