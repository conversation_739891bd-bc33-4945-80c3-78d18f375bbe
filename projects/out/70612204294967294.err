[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: marya<PERSON><PERSON> (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250625_162252-rst3632g
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run worthy-spaceship-2
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/lora-finetuning
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/lora-finetuning/runs/rst3632g

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:00<00:00,  7.07it/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:00<00:00,  8.49it/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:00<00:00,  8.21it/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/6949 [00:00<?, ? examples/s]
Map:  43%|████▎     | 3000/6949 [00:00<00:00, 19585.85 examples/s]
Map: 100%|██████████| 6949/6949 [00:00<00:00, 21841.83 examples/s]
Map: 100%|██████████| 6949/6949 [00:00<00:00, 21075.47 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.

  0%|          | 0/868 [00:00<?, ?it/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

  0%|          | 1/868 [00:03<54:49,  3.79s/it]
                                               

  0%|          | 1/868 [00:03<54:49,  3.79s/it]
  0%|          | 2/868 [00:04<31:51,  2.21s/it]
  0%|          | 3/868 [00:06<24:52,  1.73s/it]
  0%|          | 4/868 [00:07<22:08,  1.54s/it]
  1%|          | 5/868 [00:08<19:43,  1.37s/it]
  1%|          | 6/868 [00:09<19:00,  1.32s/it]
  1%|          | 7/868 [00:10<17:51,  1.24s/it]
  1%|          | 8/868 [00:11<18:10,  1.27s/it]
  1%|          | 9/868 [00:13<17:17,  1.21s/it]
  1%|          | 10/868 [00:14<17:18,  1.21s/it]
                                                

  1%|          | 10/868 [00:14<17:18,  1.21s/it]
  1%|▏         | 11/868 [00:15<16:49,  1.18s/it]
  1%|▏         | 12/868 [00:16<17:03,  1.20s/it]
  1%|▏         | 13/868 [00:17<16:48,  1.18s/it]
  2%|▏         | 14/868 [00:18<16:30,  1.16s/it]
  2%|▏         | 15/868 [00:20<16:17,  1.15s/it]
  2%|▏         | 16/868 [00:21<16:53,  1.19s/it]
  2%|▏         | 17/868 [00:22<16:28,  1.16s/it]
  2%|▏         | 18/868 [00:23<15:56,  1.13s/it]
  2%|▏         | 19/868 [00:24<16:34,  1.17s/it]
  2%|▏         | 20/868 [00:25<16:04,  1.14s/it]
                                                

  2%|▏         | 20/868 [00:25<16:04,  1.14s/it]
  2%|▏         | 21/868 [00:26<15:43,  1.11s/it]
  3%|▎         | 22/868 [00:28<16:07,  1.14s/it]
  3%|▎         | 23/868 [00:29<16:44,  1.19s/it]
  3%|▎         | 24/868 [00:30<16:21,  1.16s/it]
  3%|▎         | 25/868 [00:31<16:06,  1.15s/it]
  3%|▎         | 26/868 [00:32<15:53,  1.13s/it]
  3%|▎         | 27/868 [00:33<16:25,  1.17s/it]
  3%|▎         | 28/868 [00:34<15:58,  1.14s/it]
  3%|▎         | 29/868 [00:36<15:51,  1.13s/it]
  3%|▎         | 30/868 [00:37<15:32,  1.11s/it]
                                                

  3%|▎         | 30/868 [00:37<15:32,  1.11s/it]
  4%|▎         | 31/868 [00:38<16:45,  1.20s/it]
  4%|▎         | 32/868 [00:39<16:42,  1.20s/it]
  4%|▍         | 33/868 [00:40<16:36,  1.19s/it]
  4%|▍         | 34/868 [00:42<16:10,  1.16s/it]
  4%|▍         | 35/868 [00:43<16:43,  1.20s/it]
  4%|▍         | 36/868 [00:44<16:28,  1.19s/it]
  4%|▍         | 37/868 [00:45<16:09,  1.17s/it]
  4%|▍         | 38/868 [00:46<16:57,  1.23s/it]
  4%|▍         | 39/868 [00:48<16:39,  1.21s/it]
  5%|▍         | 40/868 [00:49<16:44,  1.21s/it]
                                                

  5%|▍         | 40/868 [00:49<16:44,  1.21s/it]
  5%|▍         | 41/868 [00:50<16:05,  1.17s/it]
  5%|▍         | 42/868 [00:51<16:31,  1.20s/it]
  5%|▍         | 43/868 [00:52<16:28,  1.20s/it]
  5%|▌         | 44/868 [00:53<16:03,  1.17s/it]
  5%|▌         | 45/868 [00:55<15:42,  1.14s/it]
  5%|▌         | 46/868 [00:56<16:29,  1.20s/it]
  5%|▌         | 47/868 [00:57<16:18,  1.19s/it]
  6%|▌         | 48/868 [00:58<16:32,  1.21s/it]
  6%|▌         | 49/868 [00:59<16:11,  1.19s/it]
  6%|▌         | 50/868 [01:01<17:02,  1.25s/it]
                                                

  6%|▌         | 50/868 [01:01<17:02,  1.25s/it]
  6%|▌         | 51/868 [01:02<16:20,  1.20s/it]
  6%|▌         | 52/868 [01:03<15:43,  1.16s/it]
  6%|▌         | 53/868 [01:04<15:24,  1.13s/it]
  6%|▌         | 54/868 [01:05<15:34,  1.15s/it]
  6%|▋         | 55/868 [01:06<15:30,  1.14s/it]
  6%|▋         | 56/868 [01:08<15:43,  1.16s/it]
  7%|▋         | 57/868 [01:09<15:17,  1.13s/it]
  7%|▋         | 58/868 [01:10<16:12,  1.20s/it]
  7%|▋         | 59/868 [01:11<15:56,  1.18s/it]
  7%|▋         | 60/868 [01:12<15:37,  1.16s/it]
                                                

  7%|▋         | 60/868 [01:12<15:37,  1.16s/it]
  7%|▋         | 61/868 [01:14<16:39,  1.24s/it]
  7%|▋         | 62/868 [01:15<16:16,  1.21s/it]
  7%|▋         | 63/868 [01:16<15:40,  1.17s/it]
  7%|▋         | 64/868 [01:17<15:34,  1.16s/it]
  7%|▋         | 65/868 [01:18<16:28,  1.23s/it]
  8%|▊         | 66/868 [01:20<15:45,  1.18s/it]
  8%|▊         | 67/868 [01:21<15:35,  1.17s/it]
  8%|▊         | 68/868 [01:22<15:26,  1.16s/it]
  8%|▊         | 69/868 [01:23<15:48,  1.19s/it]
  8%|▊         | 70/868 [01:24<15:22,  1.16s/it]
                                                

  8%|▊         | 70/868 [01:24<15:22,  1.16s/it]
  8%|▊         | 71/868 [01:25<15:07,  1.14s/it]
  8%|▊         | 72/868 [01:26<15:03,  1.14s/it]
  8%|▊         | 73/868 [01:28<16:00,  1.21s/it]
  9%|▊         | 74/868 [01:29<16:43,  1.26s/it]
  9%|▊         | 75/868 [01:30<16:03,  1.22s/it]
  9%|▉         | 76/868 [01:32<16:18,  1.24s/it]
  9%|▉         | 77/868 [01:33<15:57,  1.21s/it]
  9%|▉         | 78/868 [01:34<15:23,  1.17s/it]
  9%|▉         | 79/868 [01:35<14:56,  1.14s/it]
  9%|▉         | 80/868 [01:36<15:48,  1.20s/it]
                                                

  9%|▉         | 80/868 [01:36<15:48,  1.20s/it]
  9%|▉         | 81/868 [01:37<15:47,  1.20s/it]
  9%|▉         | 82/868 [01:39<15:50,  1.21s/it]
 10%|▉         | 83/868 [01:40<15:17,  1.17s/it]
 10%|▉         | 84/868 [01:41<15:39,  1.20s/it]
 10%|▉         | 85/868 [01:42<15:08,  1.16s/it]
 10%|▉         | 86/868 [01:43<15:17,  1.17s/it]
 10%|█         | 87/868 [01:44<15:11,  1.17s/it]
 10%|█         | 88/868 [01:46<16:02,  1.23s/it]
 10%|█         | 89/868 [01:48<18:41,  1.44s/it]
 10%|█         | 90/868 [01:49<17:27,  1.35s/it]
                                                

 10%|█         | 90/868 [01:49<17:27,  1.35s/it]
 10%|█         | 91/868 [01:50<17:05,  1.32s/it]
 11%|█         | 92/868 [01:51<16:04,  1.24s/it]
 11%|█         | 93/868 [01:52<15:25,  1.19s/it]
 11%|█         | 94/868 [01:53<15:07,  1.17s/it]
 11%|█         | 95/868 [01:55<15:48,  1.23s/it]
 11%|█         | 96/868 [01:56<15:39,  1.22s/it]
 11%|█         | 97/868 [01:57<15:01,  1.17s/it]
 11%|█▏        | 98/868 [01:58<14:44,  1.15s/it]
 11%|█▏        | 99/868 [01:59<15:10,  1.18s/it]
 12%|█▏        | 100/868 [02:00<14:46,  1.15s/it]
                                                 

 12%|█▏        | 100/868 [02:00<14:46,  1.15s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 12%|█▏        | 101/868 [02:03<19:15,  1.51s/it]
 12%|█▏        | 102/868 [02:04<18:07,  1.42s/it]
 12%|█▏        | 103/868 [02:05<17:32,  1.38s/it]
 12%|█▏        | 104/868 [02:06<16:29,  1.29s/it]
 12%|█▏        | 105/868 [02:07<15:35,  1.23s/it]
 12%|█▏        | 106/868 [02:08<15:00,  1.18s/it]
 12%|█▏        | 107/868 [02:10<15:45,  1.24s/it]
 12%|█▏        | 108/868 [02:11<15:03,  1.19s/it]
 13%|█▎        | 109/868 [02:12<14:35,  1.15s/it]
 13%|█▎        | 110/868 [02:13<15:05,  1.19s/it]
                                                 

 13%|█▎        | 110/868 [02:13<15:05,  1.19s/it]
 13%|█▎        | 111/868 [02:14<14:34,  1.15s/it]
 13%|█▎        | 112/868 [02:15<14:39,  1.16s/it]
 13%|█▎        | 113/868 [02:17<14:14,  1.13s/it]
 13%|█▎        | 114/868 [02:18<14:47,  1.18s/it]
 13%|█▎        | 115/868 [02:19<14:45,  1.18s/it]
 13%|█▎        | 116/868 [02:20<14:46,  1.18s/it]
 13%|█▎        | 117/868 [02:21<15:04,  1.20s/it]
 14%|█▎        | 118/868 [02:23<15:25,  1.23s/it]
 14%|█▎        | 119/868 [02:24<14:54,  1.19s/it]
 14%|█▍        | 120/868 [02:25<14:47,  1.19s/it]
                                                 

 14%|█▍        | 120/868 [02:25<14:47,  1.19s/it]
 14%|█▍        | 121/868 [02:26<14:56,  1.20s/it]
 14%|█▍        | 122/868 [02:28<15:07,  1.22s/it]
 14%|█▍        | 123/868 [02:29<15:03,  1.21s/it]
 14%|█▍        | 124/868 [02:30<14:32,  1.17s/it]
 14%|█▍        | 125/868 [02:31<14:09,  1.14s/it]
 15%|█▍        | 126/868 [02:32<14:36,  1.18s/it]
 15%|█▍        | 127/868 [02:33<14:41,  1.19s/it]
 15%|█▍        | 128/868 [02:35<14:53,  1.21s/it]
 15%|█▍        | 129/868 [02:36<15:30,  1.26s/it]
 15%|█▍        | 130/868 [02:37<14:49,  1.21s/it]
                                                 

 15%|█▍        | 130/868 [02:37<14:49,  1.21s/it]
 15%|█▌        | 131/868 [02:38<15:01,  1.22s/it]
 15%|█▌        | 132/868 [02:39<14:49,  1.21s/it]
 15%|█▌        | 133/868 [02:41<15:17,  1.25s/it]
 15%|█▌        | 134/868 [02:42<14:34,  1.19s/it]
 16%|█▌        | 135/868 [02:43<14:55,  1.22s/it]
 16%|█▌        | 136/868 [02:44<14:52,  1.22s/it]
 16%|█▌        | 137/868 [02:46<15:09,  1.24s/it]
 16%|█▌        | 138/868 [02:47<14:57,  1.23s/it]
 16%|█▌        | 139/868 [02:48<14:52,  1.22s/it]
 16%|█▌        | 140/868 [02:49<14:46,  1.22s/it]
                                                 

 16%|█▌        | 140/868 [02:49<14:46,  1.22s/it]
 16%|█▌        | 141/868 [02:51<14:49,  1.22s/it]
 16%|█▋        | 142/868 [02:52<14:38,  1.21s/it]
 16%|█▋        | 143/868 [02:53<14:05,  1.17s/it]
 17%|█▋        | 144/868 [02:54<14:34,  1.21s/it]
 17%|█▋        | 145/868 [02:55<14:31,  1.20s/it]
 17%|█▋        | 146/868 [02:56<13:53,  1.16s/it]
 17%|█▋        | 147/868 [02:58<14:07,  1.18s/it]
 17%|█▋        | 148/868 [02:59<14:31,  1.21s/it]
 17%|█▋        | 149/868 [03:00<14:27,  1.21s/it]
 17%|█▋        | 150/868 [03:01<14:21,  1.20s/it]
                                                 

 17%|█▋        | 150/868 [03:01<14:21,  1.20s/it]
 17%|█▋        | 151/868 [03:02<13:47,  1.15s/it]
 18%|█▊        | 152/868 [03:04<14:07,  1.18s/it]
 18%|█▊        | 153/868 [03:05<13:34,  1.14s/it]
 18%|█▊        | 154/868 [03:06<13:42,  1.15s/it]
 18%|█▊        | 155/868 [03:07<13:22,  1.13s/it]
 18%|█▊        | 156/868 [03:08<13:45,  1.16s/it]
 18%|█▊        | 157/868 [03:09<13:38,  1.15s/it]
 18%|█▊        | 158/868 [03:10<13:18,  1.13s/it]
 18%|█▊        | 159/868 [03:12<13:46,  1.17s/it]
 18%|█▊        | 160/868 [03:13<13:27,  1.14s/it]
                                                 

 18%|█▊        | 160/868 [03:13<13:27,  1.14s/it]
 19%|█▊        | 161/868 [03:14<14:04,  1.19s/it]
 19%|█▊        | 162/868 [03:15<13:45,  1.17s/it]
 19%|█▉        | 163/868 [03:16<13:59,  1.19s/it]
 19%|█▉        | 164/868 [03:17<13:35,  1.16s/it]
 19%|█▉        | 165/868 [03:18<13:12,  1.13s/it]
 19%|█▉        | 166/868 [03:19<12:57,  1.11s/it]
 19%|█▉        | 167/868 [03:21<13:49,  1.18s/it]
 19%|█▉        | 168/868 [03:22<13:32,  1.16s/it]
 19%|█▉        | 169/868 [03:23<13:43,  1.18s/it]
 20%|█▉        | 170/868 [03:24<13:23,  1.15s/it]
                                                 

 20%|█▉        | 170/868 [03:24<13:23,  1.15s/it]
 20%|█▉        | 171/868 [03:25<13:40,  1.18s/it]
 20%|█▉        | 172/868 [03:27<13:10,  1.14s/it]
 20%|█▉        | 173/868 [03:28<12:59,  1.12s/it]
 20%|██        | 174/868 [03:29<13:24,  1.16s/it]
 20%|██        | 175/868 [03:30<13:32,  1.17s/it]
 20%|██        | 176/868 [03:31<13:16,  1.15s/it]
 20%|██        | 177/868 [03:32<13:07,  1.14s/it]
 21%|██        | 178/868 [03:34<13:38,  1.19s/it]
 21%|██        | 179/868 [03:35<13:51,  1.21s/it]
 21%|██        | 180/868 [03:36<13:50,  1.21s/it]
                                                 

 21%|██        | 180/868 [03:36<13:50,  1.21s/it]
 21%|██        | 181/868 [03:37<13:31,  1.18s/it]
 21%|██        | 182/868 [03:38<14:00,  1.23s/it]
 21%|██        | 183/868 [03:40<13:28,  1.18s/it]
 21%|██        | 184/868 [03:41<13:24,  1.18s/it]
 21%|██▏       | 185/868 [03:42<13:02,  1.15s/it]
 21%|██▏       | 186/868 [03:43<13:25,  1.18s/it]
 22%|██▏       | 187/868 [03:44<13:09,  1.16s/it]
 22%|██▏       | 188/868 [03:45<12:47,  1.13s/it]
 22%|██▏       | 189/868 [03:46<12:41,  1.12s/it]
 22%|██▏       | 190/868 [03:48<12:57,  1.15s/it]
                                                 

 22%|██▏       | 190/868 [03:48<12:57,  1.15s/it]
 22%|██▏       | 191/868 [03:49<12:51,  1.14s/it]
 22%|██▏       | 192/868 [03:50<12:37,  1.12s/it]
 22%|██▏       | 193/868 [03:51<13:52,  1.23s/it]
 22%|██▏       | 194/868 [03:52<13:57,  1.24s/it]
 22%|██▏       | 195/868 [03:54<13:21,  1.19s/it]
 23%|██▎       | 196/868 [03:55<13:24,  1.20s/it]
 23%|██▎       | 197/868 [03:56<13:29,  1.21s/it]
 23%|██▎       | 198/868 [03:57<13:28,  1.21s/it]
 23%|██▎       | 199/868 [03:58<13:23,  1.20s/it]
 23%|██▎       | 200/868 [04:00<13:06,  1.18s/it]
                                                 

 23%|██▎       | 200/868 [04:00<13:06,  1.18s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 23%|██▎       | 201/868 [04:02<18:01,  1.62s/it]
 23%|██▎       | 202/868 [04:03<16:49,  1.52s/it]
 23%|██▎       | 203/868 [04:05<15:17,  1.38s/it]
 24%|██▎       | 204/868 [04:06<14:44,  1.33s/it]
 24%|██▎       | 205/868 [04:07<14:27,  1.31s/it]
 24%|██▎       | 206/868 [04:08<13:40,  1.24s/it]
 24%|██▍       | 207/868 [04:09<13:27,  1.22s/it]
 24%|██▍       | 208/868 [04:11<13:56,  1.27s/it]
 24%|██▍       | 209/868 [04:12<13:11,  1.20s/it]
 24%|██▍       | 210/868 [04:13<12:45,  1.16s/it]
                                                 

 24%|██▍       | 210/868 [04:13<12:45,  1.16s/it]
 24%|██▍       | 211/868 [04:14<12:31,  1.14s/it]
 24%|██▍       | 212/868 [04:15<13:09,  1.20s/it]
 25%|██▍       | 213/868 [04:16<13:19,  1.22s/it]
 25%|██▍       | 214/868 [04:18<13:12,  1.21s/it]
 25%|██▍       | 215/868 [04:19<12:48,  1.18s/it]
 25%|██▍       | 216/868 [04:20<12:58,  1.19s/it]
 25%|██▌       | 217/868 [04:21<12:58,  1.20s/it]
 25%|██▌       | 218/868 [04:22<12:55,  1.19s/it]
 25%|██▌       | 219/868 [04:23<12:32,  1.16s/it]
 25%|██▌       | 220/868 [04:25<12:46,  1.18s/it]
                                                 

 25%|██▌       | 220/868 [04:25<12:46,  1.18s/it]
 25%|██▌       | 221/868 [04:26<12:22,  1.15s/it]
 26%|██▌       | 222/868 [04:27<12:31,  1.16s/it]
 26%|██▌       | 223/868 [04:28<12:10,  1.13s/it]
 26%|██▌       | 224/868 [04:29<12:27,  1.16s/it]
 26%|██▌       | 225/868 [04:30<12:32,  1.17s/it]
 26%|██▌       | 226/868 [04:31<12:12,  1.14s/it]
 26%|██▌       | 227/868 [04:33<12:05,  1.13s/it]
 26%|██▋       | 228/868 [04:34<12:38,  1.18s/it]
 26%|██▋       | 229/868 [04:35<12:17,  1.15s/it]
 26%|██▋       | 230/868 [04:36<12:26,  1.17s/it]
                                                 

 26%|██▋       | 230/868 [04:36<12:26,  1.17s/it]
 27%|██▋       | 231/868 [04:38<12:54,  1.22s/it]
 27%|██▋       | 232/868 [04:39<13:19,  1.26s/it]
 27%|██▋       | 233/868 [04:40<12:39,  1.20s/it]
 27%|██▋       | 234/868 [04:41<12:32,  1.19s/it]
 27%|██▋       | 235/868 [04:42<12:53,  1.22s/it]
 27%|██▋       | 236/868 [04:43<12:23,  1.18s/it]
 27%|██▋       | 237/868 [04:45<12:38,  1.20s/it]
 27%|██▋       | 238/868 [04:46<12:11,  1.16s/it]
 28%|██▊       | 239/868 [04:47<12:37,  1.20s/it]
 28%|██▊       | 240/868 [04:48<12:21,  1.18s/it]
                                                 

 28%|██▊       | 240/868 [04:48<12:21,  1.18s/it]
 28%|██▊       | 241/868 [04:50<12:41,  1.21s/it]
 28%|██▊       | 242/868 [04:51<12:16,  1.18s/it]
 28%|██▊       | 243/868 [04:52<12:51,  1.23s/it]
 28%|██▊       | 244/868 [04:53<12:26,  1.20s/it]
 28%|██▊       | 245/868 [04:54<12:04,  1.16s/it]
 28%|██▊       | 246/868 [04:55<11:50,  1.14s/it]
 28%|██▊       | 247/868 [04:57<12:20,  1.19s/it]
 29%|██▊       | 248/868 [04:58<12:00,  1.16s/it]
 29%|██▊       | 249/868 [04:59<12:11,  1.18s/it]
 29%|██▉       | 250/868 [05:00<12:29,  1.21s/it]
                                                 

 29%|██▉       | 250/868 [05:00<12:29,  1.21s/it]
 29%|██▉       | 251/868 [05:01<12:26,  1.21s/it]
 29%|██▉       | 252/868 [05:02<11:56,  1.16s/it]
 29%|██▉       | 253/868 [05:03<11:38,  1.14s/it]
 29%|██▉       | 254/868 [05:05<12:12,  1.19s/it]
 29%|██▉       | 255/868 [05:06<12:08,  1.19s/it]
 29%|██▉       | 256/868 [05:07<12:08,  1.19s/it]
 30%|██▉       | 257/868 [05:08<11:55,  1.17s/it]
 30%|██▉       | 258/868 [05:10<12:26,  1.22s/it]
 30%|██▉       | 259/868 [05:11<12:03,  1.19s/it]
 30%|██▉       | 260/868 [05:12<11:41,  1.15s/it]
                                                 

 30%|██▉       | 260/868 [05:12<11:41,  1.15s/it]
 30%|███       | 261/868 [05:13<11:44,  1.16s/it]
 30%|███       | 262/868 [05:14<12:07,  1.20s/it]
 30%|███       | 263/868 [05:15<11:47,  1.17s/it]
 30%|███       | 264/868 [05:17<11:31,  1.15s/it]
 31%|███       | 265/868 [05:18<11:36,  1.15s/it]
 31%|███       | 266/868 [05:19<11:53,  1.18s/it]
 31%|███       | 267/868 [05:20<11:27,  1.14s/it]
 31%|███       | 268/868 [05:21<11:18,  1.13s/it]
 31%|███       | 269/868 [05:22<11:42,  1.17s/it]
 31%|███       | 270/868 [05:23<11:25,  1.15s/it]
                                                 

 31%|███       | 270/868 [05:23<11:25,  1.15s/it]
 31%|███       | 271/868 [05:25<11:19,  1.14s/it]
 31%|███▏      | 272/868 [05:26<11:07,  1.12s/it]
 31%|███▏      | 273/868 [05:27<11:31,  1.16s/it]
 32%|███▏      | 274/868 [05:28<11:18,  1.14s/it]
 32%|███▏      | 275/868 [05:29<11:20,  1.15s/it]
 32%|███▏      | 276/868 [05:30<11:10,  1.13s/it]
 32%|███▏      | 277/868 [05:31<11:24,  1.16s/it]
 32%|███▏      | 278/868 [05:33<11:06,  1.13s/it]
 32%|███▏      | 279/868 [05:34<11:07,  1.13s/it]
 32%|███▏      | 280/868 [05:35<10:57,  1.12s/it]
                                                 

 32%|███▏      | 280/868 [05:35<10:57,  1.12s/it]
 32%|███▏      | 281/868 [05:36<11:14,  1.15s/it]
 32%|███▏      | 282/868 [05:37<11:01,  1.13s/it]
 33%|███▎      | 283/868 [05:38<11:32,  1.18s/it]
 33%|███▎      | 284/868 [05:39<11:16,  1.16s/it]
 33%|███▎      | 285/868 [05:41<11:34,  1.19s/it]
 33%|███▎      | 286/868 [05:42<11:27,  1.18s/it]
 33%|███▎      | 287/868 [05:43<11:14,  1.16s/it]
 33%|███▎      | 288/868 [05:44<11:44,  1.22s/it]
 33%|███▎      | 289/868 [05:46<11:56,  1.24s/it]
 33%|███▎      | 290/868 [05:47<11:22,  1.18s/it]
                                                 

 33%|███▎      | 290/868 [05:47<11:22,  1.18s/it]
 34%|███▎      | 291/868 [05:48<11:13,  1.17s/it]
 34%|███▎      | 292/868 [05:49<11:28,  1.19s/it]
 34%|███▍      | 293/868 [05:50<11:08,  1.16s/it]
 34%|███▍      | 294/868 [05:51<11:11,  1.17s/it]
 34%|███▍      | 295/868 [05:53<11:14,  1.18s/it]
 34%|███▍      | 296/868 [05:54<11:25,  1.20s/it]
 34%|███▍      | 297/868 [05:55<11:06,  1.17s/it]
 34%|███▍      | 298/868 [05:56<11:29,  1.21s/it]
 34%|███▍      | 299/868 [05:57<11:08,  1.17s/it]
 35%|███▍      | 300/868 [05:59<11:16,  1.19s/it]
                                                 

 35%|███▍      | 300/868 [05:59<11:16,  1.19s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 35%|███▍      | 301/868 [06:01<14:03,  1.49s/it]
 35%|███▍      | 302/868 [06:02<12:49,  1.36s/it]
 35%|███▍      | 303/868 [06:03<12:04,  1.28s/it]
 35%|███▌      | 304/868 [06:04<12:14,  1.30s/it]
 35%|███▌      | 305/868 [06:05<11:43,  1.25s/it]
 35%|███▌      | 306/868 [06:06<11:12,  1.20s/it]
 35%|███▌      | 307/868 [06:08<11:22,  1.22s/it]
 35%|███▌      | 308/868 [06:09<11:22,  1.22s/it]
 36%|███▌      | 309/868 [06:10<11:19,  1.22s/it]
 36%|███▌      | 310/868 [06:11<10:55,  1.18s/it]
                                                 

 36%|███▌      | 310/868 [06:11<10:55,  1.18s/it]
 36%|███▌      | 311/868 [06:12<11:04,  1.19s/it]
 36%|███▌      | 312/868 [06:14<10:52,  1.17s/it]
 36%|███▌      | 313/868 [06:15<10:58,  1.19s/it]
 36%|███▌      | 314/868 [06:16<10:36,  1.15s/it]
 36%|███▋      | 315/868 [06:17<10:55,  1.18s/it]
 36%|███▋      | 316/868 [06:18<11:17,  1.23s/it]
 37%|███▋      | 317/868 [06:20<11:24,  1.24s/it]
 37%|███▋      | 318/868 [06:21<10:59,  1.20s/it]
 37%|███▋      | 319/868 [06:22<11:25,  1.25s/it]
 37%|███▋      | 320/868 [06:23<11:02,  1.21s/it]
                                                 

 37%|███▋      | 320/868 [06:23<11:02,  1.21s/it]
 37%|███▋      | 321/868 [06:24<10:38,  1.17s/it]
 37%|███▋      | 322/868 [06:26<10:48,  1.19s/it]
 37%|███▋      | 323/868 [06:27<11:03,  1.22s/it]
 37%|███▋      | 324/868 [06:28<10:35,  1.17s/it]
 37%|███▋      | 325/868 [06:29<10:15,  1.13s/it]
 38%|███▊      | 326/868 [06:30<10:19,  1.14s/it]
 38%|███▊      | 327/868 [06:31<10:48,  1.20s/it]
 38%|███▊      | 328/868 [06:33<10:56,  1.22s/it]
 38%|███▊      | 329/868 [06:34<10:44,  1.20s/it]
 38%|███▊      | 330/868 [06:35<10:52,  1.21s/it]
                                                 

 38%|███▊      | 330/868 [06:35<10:52,  1.21s/it]
 38%|███▊      | 331/868 [06:36<10:40,  1.19s/it]
 38%|███▊      | 332/868 [06:37<10:20,  1.16s/it]
 38%|███▊      | 333/868 [06:39<10:43,  1.20s/it]
 38%|███▊      | 334/868 [06:40<10:46,  1.21s/it]
 39%|███▊      | 335/868 [06:41<10:26,  1.18s/it]
 39%|███▊      | 336/868 [06:42<10:11,  1.15s/it]
 39%|███▉      | 337/868 [06:43<10:12,  1.15s/it]
 39%|███▉      | 338/868 [06:45<10:48,  1.22s/it]
 39%|███▉      | 339/868 [06:46<10:22,  1.18s/it]
 39%|███▉      | 340/868 [06:47<10:08,  1.15s/it]
                                                 

 39%|███▉      | 340/868 [06:47<10:08,  1.15s/it]
 39%|███▉      | 341/868 [06:48<10:26,  1.19s/it]
 39%|███▉      | 342/868 [06:49<10:21,  1.18s/it]
 40%|███▉      | 343/868 [06:50<09:59,  1.14s/it]
 40%|███▉      | 344/868 [06:51<09:55,  1.14s/it]
 40%|███▉      | 345/868 [06:53<10:17,  1.18s/it]
 40%|███▉      | 346/868 [06:54<10:04,  1.16s/it]
 40%|███▉      | 347/868 [06:55<10:18,  1.19s/it]
 40%|████      | 348/868 [06:56<10:03,  1.16s/it]
 40%|████      | 349/868 [06:57<10:23,  1.20s/it]
 40%|████      | 350/868 [06:59<10:03,  1.16s/it]
                                                 

 40%|████      | 350/868 [06:59<10:03,  1.16s/it]
 40%|████      | 351/868 [07:00<09:46,  1.13s/it]
 41%|████      | 352/868 [07:01<09:39,  1.12s/it]
 41%|████      | 353/868 [07:02<09:52,  1.15s/it]
 41%|████      | 354/868 [07:03<09:46,  1.14s/it]
 41%|████      | 355/868 [07:04<09:55,  1.16s/it]
 41%|████      | 356/868 [07:05<10:11,  1.20s/it]
 41%|████      | 357/868 [07:07<09:53,  1.16s/it]
 41%|████      | 358/868 [07:08<09:41,  1.14s/it]
 41%|████▏     | 359/868 [07:09<09:45,  1.15s/it]
 41%|████▏     | 360/868 [07:10<09:54,  1.17s/it]
                                                 

 41%|████▏     | 360/868 [07:10<09:54,  1.17s/it]
 42%|████▏     | 361/868 [07:11<10:01,  1.19s/it]
 42%|████▏     | 362/868 [07:12<09:40,  1.15s/it]
 42%|████▏     | 363/868 [07:14<09:56,  1.18s/it]
 42%|████▏     | 364/868 [07:15<10:15,  1.22s/it]
 42%|████▏     | 365/868 [07:16<09:50,  1.17s/it]
 42%|████▏     | 366/868 [07:17<09:54,  1.19s/it]
 42%|████▏     | 367/868 [07:18<09:43,  1.17s/it]
 42%|████▏     | 368/868 [07:20<10:02,  1.20s/it]
 43%|████▎     | 369/868 [07:21<10:06,  1.22s/it]
 43%|████▎     | 370/868 [07:22<09:42,  1.17s/it]
                                                 

 43%|████▎     | 370/868 [07:22<09:42,  1.17s/it]
 43%|████▎     | 371/868 [07:23<09:49,  1.19s/it]
 43%|████▎     | 372/868 [07:24<09:36,  1.16s/it]
 43%|████▎     | 373/868 [07:26<09:57,  1.21s/it]
 43%|████▎     | 374/868 [07:27<09:48,  1.19s/it]
 43%|████▎     | 375/868 [07:28<10:14,  1.25s/it]
 43%|████▎     | 376/868 [07:29<09:46,  1.19s/it]
 43%|████▎     | 377/868 [07:30<09:38,  1.18s/it]
 44%|████▎     | 378/868 [07:31<09:22,  1.15s/it]
 44%|████▎     | 379/868 [07:33<09:34,  1.17s/it]
 44%|████▍     | 380/868 [07:34<09:15,  1.14s/it]
                                                 

 44%|████▍     | 380/868 [07:34<09:15,  1.14s/it]
 44%|████▍     | 381/868 [07:35<09:20,  1.15s/it]
 44%|████▍     | 382/868 [07:36<09:22,  1.16s/it]
 44%|████▍     | 383/868 [07:37<09:38,  1.19s/it]
 44%|████▍     | 384/868 [07:38<09:28,  1.17s/it]
 44%|████▍     | 385/868 [07:39<09:10,  1.14s/it]
 44%|████▍     | 386/868 [07:41<08:54,  1.11s/it]
 45%|████▍     | 387/868 [07:42<09:18,  1.16s/it]
 45%|████▍     | 388/868 [07:43<09:18,  1.16s/it]
 45%|████▍     | 389/868 [07:44<09:17,  1.16s/it]
 45%|████▍     | 390/868 [07:45<09:06,  1.14s/it]
                                                 

 45%|████▍     | 390/868 [07:45<09:06,  1.14s/it]
 45%|████▌     | 391/868 [07:47<09:27,  1.19s/it]
 45%|████▌     | 392/868 [07:48<09:42,  1.22s/it]
 45%|████▌     | 393/868 [07:49<09:32,  1.21s/it]
 45%|████▌     | 394/868 [07:50<09:37,  1.22s/it]
 46%|████▌     | 395/868 [07:51<09:18,  1.18s/it]
 46%|████▌     | 396/868 [07:52<09:05,  1.15s/it]
 46%|████▌     | 397/868 [07:54<08:56,  1.14s/it]
 46%|████▌     | 398/868 [07:55<09:12,  1.18s/it]
 46%|████▌     | 399/868 [07:56<08:58,  1.15s/it]
 46%|████▌     | 400/868 [07:57<08:50,  1.13s/it]
                                                 

 46%|████▌     | 400/868 [07:57<08:50,  1.13s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 46%|████▌     | 401/868 [07:59<11:26,  1.47s/it]
 46%|████▋     | 402/868 [08:00<10:57,  1.41s/it]
 46%|████▋     | 403/868 [08:02<10:15,  1.32s/it]
 47%|████▋     | 404/868 [08:03<09:38,  1.25s/it]
 47%|████▋     | 405/868 [08:04<09:14,  1.20s/it]
 47%|████▋     | 406/868 [08:05<09:34,  1.24s/it]
 47%|████▋     | 407/868 [08:06<09:13,  1.20s/it]
 47%|████▋     | 408/868 [08:07<08:52,  1.16s/it]
 47%|████▋     | 409/868 [08:08<08:43,  1.14s/it]
 47%|████▋     | 410/868 [08:10<09:04,  1.19s/it]
                                                 

 47%|████▋     | 410/868 [08:10<09:04,  1.19s/it]
 47%|████▋     | 411/868 [08:11<08:53,  1.17s/it]
 47%|████▋     | 412/868 [08:12<09:00,  1.19s/it]
 48%|████▊     | 413/868 [08:13<09:16,  1.22s/it]
 48%|████▊     | 414/868 [08:14<08:54,  1.18s/it]
 48%|████▊     | 415/868 [08:15<08:40,  1.15s/it]
 48%|████▊     | 416/868 [08:17<08:39,  1.15s/it]
 48%|████▊     | 417/868 [08:18<08:52,  1.18s/it]
 48%|████▊     | 418/868 [08:19<08:37,  1.15s/it]
 48%|████▊     | 419/868 [08:20<08:23,  1.12s/it]
 48%|████▊     | 420/868 [08:21<08:16,  1.11s/it]
                                                 

 48%|████▊     | 420/868 [08:21<08:16,  1.11s/it]
 49%|████▊     | 421/868 [08:22<08:36,  1.15s/it]
 49%|████▊     | 422/868 [08:23<08:25,  1.13s/it]
 49%|████▊     | 423/868 [08:25<08:29,  1.14s/it]
 49%|████▉     | 424/868 [08:26<08:35,  1.16s/it]
 49%|████▉     | 425/868 [08:27<08:42,  1.18s/it]
 49%|████▉     | 426/868 [08:28<08:58,  1.22s/it]
 49%|████▉     | 427/868 [08:29<08:37,  1.17s/it]
 49%|████▉     | 428/868 [08:30<08:23,  1.14s/it]
 49%|████▉     | 429/868 [08:32<08:39,  1.18s/it]
 50%|████▉     | 430/868 [08:33<08:25,  1.15s/it]
                                                 

 50%|████▉     | 430/868 [08:33<08:25,  1.15s/it]
 50%|████▉     | 431/868 [08:34<08:23,  1.15s/it]
 50%|████▉     | 432/868 [08:35<08:37,  1.19s/it]
 50%|████▉     | 433/868 [08:37<08:50,  1.22s/it]
 50%|█████     | 434/868 [08:38<08:33,  1.18s/it]
 50%|█████     | 435/868 [08:39<08:07,  1.13s/it]
 50%|█████     | 436/868 [08:40<08:22,  1.16s/it]
 50%|█████     | 437/868 [08:41<08:07,  1.13s/it]
 50%|█████     | 438/868 [08:42<08:00,  1.12s/it]
 51%|█████     | 439/868 [08:43<08:00,  1.12s/it]
 51%|█████     | 440/868 [08:45<08:28,  1.19s/it]
                                                 

 51%|█████     | 440/868 [08:45<08:28,  1.19s/it]
 51%|█████     | 441/868 [08:46<08:37,  1.21s/it]
 51%|█████     | 442/868 [08:47<08:32,  1.20s/it]
 51%|█████     | 443/868 [08:48<08:13,  1.16s/it]
 51%|█████     | 444/868 [08:49<08:25,  1.19s/it]
 51%|█████▏    | 445/868 [08:51<08:29,  1.20s/it]
 51%|█████▏    | 446/868 [08:52<08:06,  1.15s/it]
 51%|█████▏    | 447/868 [08:53<08:25,  1.20s/it]
 52%|█████▏    | 448/868 [08:54<08:29,  1.21s/it]
 52%|█████▏    | 449/868 [08:55<08:15,  1.18s/it]
 52%|█████▏    | 450/868 [08:56<07:58,  1.15s/it]
                                                 

 52%|█████▏    | 450/868 [08:56<07:58,  1.15s/it]
 52%|█████▏    | 451/868 [08:58<08:32,  1.23s/it]
 52%|█████▏    | 452/868 [08:59<08:38,  1.25s/it]
 52%|█████▏    | 453/868 [09:00<08:28,  1.23s/it]
 52%|█████▏    | 454/868 [09:01<08:21,  1.21s/it]
 52%|█████▏    | 455/868 [09:03<08:42,  1.27s/it]
 53%|█████▎    | 456/868 [09:04<08:42,  1.27s/it]
 53%|█████▎    | 457/868 [09:05<08:30,  1.24s/it]
 53%|█████▎    | 458/868 [09:06<08:07,  1.19s/it]
 53%|█████▎    | 459/868 [09:08<08:13,  1.21s/it]
 53%|█████▎    | 460/868 [09:09<08:00,  1.18s/it]
                                                 

 53%|█████▎    | 460/868 [09:09<08:00,  1.18s/it]
 53%|█████▎    | 461/868 [09:10<07:59,  1.18s/it]
 53%|█████▎    | 462/868 [09:11<08:11,  1.21s/it]
 53%|█████▎    | 463/868 [09:12<08:11,  1.21s/it]
 53%|█████▎    | 464/868 [09:14<08:21,  1.24s/it]
 54%|█████▎    | 465/868 [09:15<08:03,  1.20s/it]
 54%|█████▎    | 466/868 [09:16<08:06,  1.21s/it]
 54%|█████▍    | 467/868 [09:17<07:49,  1.17s/it]
 54%|█████▍    | 468/868 [09:18<07:42,  1.16s/it]
 54%|█████▍    | 469/868 [09:19<07:34,  1.14s/it]
 54%|█████▍    | 470/868 [09:20<07:46,  1.17s/it]
                                                 

 54%|█████▍    | 470/868 [09:20<07:46,  1.17s/it]
 54%|█████▍    | 471/868 [09:22<07:39,  1.16s/it]
 54%|█████▍    | 472/868 [09:23<07:45,  1.18s/it]
 54%|█████▍    | 473/868 [09:24<07:32,  1.14s/it]
 55%|█████▍    | 474/868 [09:25<07:41,  1.17s/it]
 55%|█████▍    | 475/868 [09:26<07:43,  1.18s/it]
 55%|█████▍    | 476/868 [09:28<07:54,  1.21s/it]
 55%|█████▍    | 477/868 [09:29<07:51,  1.20s/it]
 55%|█████▌    | 478/868 [09:30<08:23,  1.29s/it]
 55%|█████▌    | 479/868 [09:31<07:57,  1.23s/it]
 55%|█████▌    | 480/868 [09:32<07:34,  1.17s/it]
                                                 

 55%|█████▌    | 480/868 [09:32<07:34,  1.17s/it]
 55%|█████▌    | 481/868 [09:34<07:48,  1.21s/it]
 56%|█████▌    | 482/868 [09:35<07:34,  1.18s/it]
 56%|█████▌    | 483/868 [09:36<07:29,  1.17s/it]
 56%|█████▌    | 484/868 [09:37<07:19,  1.14s/it]
 56%|█████▌    | 485/868 [09:38<07:44,  1.21s/it]
 56%|█████▌    | 486/868 [09:39<07:24,  1.16s/it]
 56%|█████▌    | 487/868 [09:41<07:18,  1.15s/it]
 56%|█████▌    | 488/868 [09:42<07:14,  1.14s/it]
 56%|█████▋    | 489/868 [09:43<07:34,  1.20s/it]
 56%|█████▋    | 490/868 [09:44<07:17,  1.16s/it]
                                                 

 56%|█████▋    | 490/868 [09:44<07:17,  1.16s/it]
 57%|█████▋    | 491/868 [09:45<07:17,  1.16s/it]
 57%|█████▋    | 492/868 [09:47<07:31,  1.20s/it]
 57%|█████▋    | 493/868 [09:48<07:34,  1.21s/it]
 57%|█████▋    | 494/868 [09:49<07:23,  1.19s/it]
 57%|█████▋    | 495/868 [09:50<07:11,  1.16s/it]
 57%|█████▋    | 496/868 [09:51<07:26,  1.20s/it]
 57%|█████▋    | 497/868 [09:53<07:31,  1.22s/it]
 57%|█████▋    | 498/868 [09:54<07:16,  1.18s/it]
 57%|█████▋    | 499/868 [09:55<07:22,  1.20s/it]
 58%|█████▊    | 500/868 [09:56<07:09,  1.17s/it]
                                                 

 58%|█████▊    | 500/868 [09:56<07:09,  1.17s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 58%|█████▊    | 501/868 [09:59<09:32,  1.56s/it]
 58%|█████▊    | 502/868 [10:00<08:33,  1.40s/it]
 58%|█████▊    | 503/868 [10:01<07:59,  1.31s/it]
 58%|█████▊    | 504/868 [10:02<07:53,  1.30s/it]
 58%|█████▊    | 505/868 [10:03<07:36,  1.26s/it]
 58%|█████▊    | 506/868 [10:04<07:17,  1.21s/it]
 58%|█████▊    | 507/868 [10:05<06:59,  1.16s/it]
 59%|█████▊    | 508/868 [10:06<07:04,  1.18s/it]
 59%|█████▊    | 509/868 [10:08<07:07,  1.19s/it]
 59%|█████▉    | 510/868 [10:09<07:01,  1.18s/it]
                                                 

 59%|█████▉    | 510/868 [10:09<07:01,  1.18s/it]
 59%|█████▉    | 511/868 [10:10<07:02,  1.18s/it]
 59%|█████▉    | 512/868 [10:11<07:13,  1.22s/it]
 59%|█████▉    | 513/868 [10:12<07:03,  1.19s/it]
 59%|█████▉    | 514/868 [10:14<06:50,  1.16s/it]
 59%|█████▉    | 515/868 [10:15<07:13,  1.23s/it]
 59%|█████▉    | 516/868 [10:16<06:54,  1.18s/it]
 60%|█████▉    | 517/868 [10:17<06:42,  1.15s/it]
 60%|█████▉    | 518/868 [10:18<06:35,  1.13s/it]
 60%|█████▉    | 519/868 [10:19<06:50,  1.17s/it]
 60%|█████▉    | 520/868 [10:21<06:48,  1.17s/it]
                                                 

 60%|█████▉    | 520/868 [10:21<06:48,  1.17s/it]
 60%|██████    | 521/868 [10:22<06:39,  1.15s/it]
 60%|██████    | 522/868 [10:23<06:31,  1.13s/it]
 60%|██████    | 523/868 [10:24<07:05,  1.23s/it]
 60%|██████    | 524/868 [10:25<06:52,  1.20s/it]
 60%|██████    | 525/868 [10:26<06:36,  1.16s/it]
 61%|██████    | 526/868 [10:28<06:34,  1.15s/it]
 61%|██████    | 527/868 [10:29<06:54,  1.22s/it]
 61%|██████    | 528/868 [10:30<06:37,  1.17s/it]
 61%|██████    | 529/868 [10:31<06:26,  1.14s/it]
 61%|██████    | 530/868 [10:32<06:55,  1.23s/it]
                                                 

 61%|██████    | 530/868 [10:32<06:55,  1.23s/it]
 61%|██████    | 531/868 [10:34<06:46,  1.21s/it]
 61%|██████▏   | 532/868 [10:35<06:34,  1.17s/it]
 61%|██████▏   | 533/868 [10:36<06:23,  1.14s/it]
 62%|██████▏   | 534/868 [10:37<06:44,  1.21s/it]
 62%|██████▏   | 535/868 [10:38<06:30,  1.17s/it]
 62%|██████▏   | 536/868 [10:40<06:44,  1.22s/it]
 62%|██████▏   | 537/868 [10:41<06:27,  1.17s/it]
 62%|██████▏   | 538/868 [10:42<06:31,  1.19s/it]
 62%|██████▏   | 539/868 [10:43<06:29,  1.18s/it]
 62%|██████▏   | 540/868 [10:44<06:22,  1.17s/it]
                                                 

 62%|██████▏   | 540/868 [10:44<06:22,  1.17s/it]
 62%|██████▏   | 541/868 [10:45<06:10,  1.13s/it]
 62%|██████▏   | 542/868 [10:47<06:28,  1.19s/it]
 63%|██████▎   | 543/868 [10:48<06:18,  1.17s/it]
 63%|██████▎   | 544/868 [10:49<06:07,  1.13s/it]
 63%|██████▎   | 545/868 [10:50<06:00,  1.12s/it]
 63%|██████▎   | 546/868 [10:51<06:13,  1.16s/it]
 63%|██████▎   | 547/868 [10:52<06:06,  1.14s/it]
 63%|██████▎   | 548/868 [10:53<06:02,  1.13s/it]
 63%|██████▎   | 549/868 [10:54<05:57,  1.12s/it]
 63%|██████▎   | 550/868 [10:56<06:10,  1.17s/it]
                                                 

 63%|██████▎   | 550/868 [10:56<06:10,  1.17s/it]
 63%|██████▎   | 551/868 [10:57<06:02,  1.14s/it]
 64%|██████▎   | 552/868 [10:58<06:04,  1.15s/it]
 64%|██████▎   | 553/868 [10:59<06:14,  1.19s/it]
 64%|██████▍   | 554/868 [11:00<06:05,  1.16s/it]
 64%|██████▍   | 555/868 [11:01<05:56,  1.14s/it]
 64%|██████▍   | 556/868 [11:02<05:55,  1.14s/it]
 64%|██████▍   | 557/868 [11:04<06:03,  1.17s/it]
 64%|██████▍   | 558/868 [11:05<05:54,  1.14s/it]
 64%|██████▍   | 559/868 [11:06<05:46,  1.12s/it]
 65%|██████▍   | 560/868 [11:07<05:54,  1.15s/it]
                                                 

 65%|██████▍   | 560/868 [11:07<05:54,  1.15s/it]
 65%|██████▍   | 561/868 [11:08<06:02,  1.18s/it]
 65%|██████▍   | 562/868 [11:10<06:04,  1.19s/it]
 65%|██████▍   | 563/868 [11:11<06:08,  1.21s/it]
 65%|██████▍   | 564/868 [11:12<06:08,  1.21s/it]
 65%|██████▌   | 565/868 [11:13<06:21,  1.26s/it]
 65%|██████▌   | 566/868 [11:15<06:14,  1.24s/it]
 65%|██████▌   | 567/868 [11:16<06:02,  1.20s/it]
 65%|██████▌   | 568/868 [11:17<06:02,  1.21s/it]
 66%|██████▌   | 569/868 [11:18<06:16,  1.26s/it]
 66%|██████▌   | 570/868 [11:19<05:56,  1.20s/it]
                                                 

 66%|██████▌   | 570/868 [11:19<05:56,  1.20s/it]
 66%|██████▌   | 571/868 [11:21<05:56,  1.20s/it]
 66%|██████▌   | 572/868 [11:22<06:00,  1.22s/it]
 66%|██████▌   | 573/868 [11:23<06:02,  1.23s/it]
 66%|██████▌   | 574/868 [11:24<05:48,  1.19s/it]
 66%|██████▌   | 575/868 [11:25<05:38,  1.15s/it]
 66%|██████▋   | 576/868 [11:27<05:47,  1.19s/it]
 66%|██████▋   | 577/868 [11:28<05:35,  1.15s/it]
 67%|██████▋   | 578/868 [11:29<05:44,  1.19s/it]
 67%|██████▋   | 579/868 [11:30<05:35,  1.16s/it]
 67%|██████▋   | 580/868 [11:31<05:47,  1.21s/it]
                                                 

 67%|██████▋   | 580/868 [11:31<05:47,  1.21s/it]
 67%|██████▋   | 581/868 [11:32<05:46,  1.21s/it]
 67%|██████▋   | 582/868 [11:34<05:34,  1.17s/it]
 67%|██████▋   | 583/868 [11:35<05:25,  1.14s/it]
 67%|██████▋   | 584/868 [11:36<05:32,  1.17s/it]
 67%|██████▋   | 585/868 [11:37<05:22,  1.14s/it]
 68%|██████▊   | 586/868 [11:38<05:14,  1.11s/it]
 68%|██████▊   | 587/868 [11:39<05:25,  1.16s/it]
 68%|██████▊   | 588/868 [11:40<05:30,  1.18s/it]
 68%|██████▊   | 589/868 [11:42<05:25,  1.17s/it]
 68%|██████▊   | 590/868 [11:43<05:15,  1.14s/it]
                                                 

 68%|██████▊   | 590/868 [11:43<05:15,  1.14s/it]
 68%|██████▊   | 591/868 [11:44<05:32,  1.20s/it]
 68%|██████▊   | 592/868 [11:45<05:20,  1.16s/it]
 68%|██████▊   | 593/868 [11:46<05:30,  1.20s/it]
 68%|██████▊   | 594/868 [11:47<05:17,  1.16s/it]
 69%|██████▊   | 595/868 [11:49<05:27,  1.20s/it]
 69%|██████▊   | 596/868 [11:50<05:19,  1.17s/it]
 69%|██████▉   | 597/868 [11:51<05:20,  1.18s/it]
 69%|██████▉   | 598/868 [11:52<05:10,  1.15s/it]
 69%|██████▉   | 599/868 [11:54<05:25,  1.21s/it]
 69%|██████▉   | 600/868 [11:55<05:25,  1.21s/it]
                                                 

 69%|██████▉   | 600/868 [11:55<05:25,  1.21s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 69%|██████▉   | 601/868 [11:57<06:41,  1.50s/it]
 69%|██████▉   | 602/868 [11:58<06:03,  1.37s/it]
 69%|██████▉   | 603/868 [11:59<05:54,  1.34s/it]
 70%|██████▉   | 604/868 [12:00<05:31,  1.26s/it]
 70%|██████▉   | 605/868 [12:01<05:21,  1.22s/it]
 70%|██████▉   | 606/868 [12:03<05:17,  1.21s/it]
 70%|██████▉   | 607/868 [12:04<05:15,  1.21s/it]
 70%|███████   | 608/868 [12:05<05:11,  1.20s/it]
 70%|███████   | 609/868 [12:06<05:04,  1.18s/it]
 70%|███████   | 610/868 [12:08<05:20,  1.24s/it]
                                                 

 70%|███████   | 610/868 [12:08<05:20,  1.24s/it]
 70%|███████   | 611/868 [12:09<05:09,  1.20s/it]
 71%|███████   | 612/868 [12:10<05:03,  1.19s/it]
 71%|███████   | 613/868 [12:11<05:00,  1.18s/it]
 71%|███████   | 614/868 [12:12<05:03,  1.19s/it]
 71%|███████   | 615/868 [12:13<04:55,  1.17s/it]
 71%|███████   | 616/868 [12:14<04:49,  1.15s/it]
 71%|███████   | 617/868 [12:16<04:46,  1.14s/it]
 71%|███████   | 618/868 [12:17<04:53,  1.18s/it]
 71%|███████▏  | 619/868 [12:18<04:51,  1.17s/it]
 71%|███████▏  | 620/868 [12:19<04:48,  1.16s/it]
                                                 

 71%|███████▏  | 620/868 [12:19<04:48,  1.16s/it]
 72%|███████▏  | 621/868 [12:20<04:55,  1.20s/it]
 72%|███████▏  | 622/868 [12:21<04:43,  1.15s/it]
 72%|███████▏  | 623/868 [12:22<04:38,  1.14s/it]
 72%|███████▏  | 624/868 [12:24<04:31,  1.11s/it]
 72%|███████▏  | 625/868 [12:25<04:44,  1.17s/it]
 72%|███████▏  | 626/868 [12:26<04:44,  1.18s/it]
 72%|███████▏  | 627/868 [12:27<04:38,  1.16s/it]
 72%|███████▏  | 628/868 [12:28<04:46,  1.20s/it]
 72%|███████▏  | 629/868 [12:30<04:55,  1.24s/it]
 73%|███████▎  | 630/868 [12:31<04:55,  1.24s/it]
                                                 

 73%|███████▎  | 630/868 [12:31<04:55,  1.24s/it]
 73%|███████▎  | 631/868 [12:32<04:42,  1.19s/it]
 73%|███████▎  | 632/868 [12:33<04:34,  1.16s/it]
 73%|███████▎  | 633/868 [12:34<04:42,  1.20s/it]
 73%|███████▎  | 634/868 [12:36<04:35,  1.18s/it]
 73%|███████▎  | 635/868 [12:37<04:28,  1.15s/it]
 73%|███████▎  | 636/868 [12:38<04:29,  1.16s/it]
 73%|███████▎  | 637/868 [12:39<04:39,  1.21s/it]
 74%|███████▎  | 638/868 [12:40<04:30,  1.18s/it]
 74%|███████▎  | 639/868 [12:42<04:34,  1.20s/it]
 74%|███████▎  | 640/868 [12:43<04:40,  1.23s/it]
                                                 

 74%|███████▎  | 640/868 [12:43<04:40,  1.23s/it]
 74%|███████▍  | 641/868 [12:44<04:28,  1.18s/it]
 74%|███████▍  | 642/868 [12:45<04:18,  1.14s/it]
 74%|███████▍  | 643/868 [12:46<04:13,  1.13s/it]
 74%|███████▍  | 644/868 [12:47<04:30,  1.21s/it]
 74%|███████▍  | 645/868 [12:49<04:26,  1.20s/it]
 74%|███████▍  | 646/868 [12:50<04:16,  1.16s/it]
 75%|███████▍  | 647/868 [12:51<04:11,  1.14s/it]
 75%|███████▍  | 648/868 [12:52<04:22,  1.19s/it]
 75%|███████▍  | 649/868 [12:53<04:17,  1.18s/it]
 75%|███████▍  | 650/868 [12:54<04:10,  1.15s/it]
                                                 

 75%|███████▍  | 650/868 [12:54<04:10,  1.15s/it]
 75%|███████▌  | 651/868 [12:55<04:05,  1.13s/it]
 75%|███████▌  | 652/868 [12:57<04:16,  1.19s/it]
 75%|███████▌  | 653/868 [12:58<04:14,  1.19s/it]
 75%|███████▌  | 654/868 [12:59<04:05,  1.15s/it]
 75%|███████▌  | 655/868 [13:00<04:05,  1.15s/it]
 76%|███████▌  | 656/868 [13:01<04:11,  1.18s/it]
 76%|███████▌  | 657/868 [13:03<04:11,  1.19s/it]
 76%|███████▌  | 658/868 [13:04<04:12,  1.20s/it]
 76%|███████▌  | 659/868 [13:05<04:20,  1.24s/it]
 76%|███████▌  | 660/868 [13:06<04:17,  1.24s/it]
                                                 

 76%|███████▌  | 660/868 [13:06<04:17,  1.24s/it]
 76%|███████▌  | 661/868 [13:08<04:09,  1.21s/it]
 76%|███████▋  | 662/868 [13:09<04:05,  1.19s/it]
 76%|███████▋  | 663/868 [13:10<04:10,  1.22s/it]
 76%|███████▋  | 664/868 [13:11<04:01,  1.19s/it]
 77%|███████▋  | 665/868 [13:12<03:55,  1.16s/it]
 77%|███████▋  | 666/868 [13:13<03:58,  1.18s/it]
 77%|███████▋  | 667/868 [13:15<04:03,  1.21s/it]
 77%|███████▋  | 668/868 [13:16<04:07,  1.24s/it]
 77%|███████▋  | 669/868 [13:17<03:59,  1.20s/it]
 77%|███████▋  | 670/868 [13:18<03:49,  1.16s/it]
                                                 

 77%|███████▋  | 670/868 [13:18<03:49,  1.16s/it]
 77%|███████▋  | 671/868 [13:20<04:00,  1.22s/it]
 77%|███████▋  | 672/868 [13:21<03:54,  1.19s/it]
 78%|███████▊  | 673/868 [13:22<03:44,  1.15s/it]
 78%|███████▊  | 674/868 [13:23<03:39,  1.13s/it]
 78%|███████▊  | 675/868 [13:24<03:58,  1.23s/it]
 78%|███████▊  | 676/868 [13:25<03:51,  1.20s/it]
 78%|███████▊  | 677/868 [13:27<03:49,  1.20s/it]
 78%|███████▊  | 678/868 [13:28<03:43,  1.18s/it]
 78%|███████▊  | 679/868 [13:29<03:54,  1.24s/it]
 78%|███████▊  | 680/868 [13:30<03:43,  1.19s/it]
                                                 

 78%|███████▊  | 680/868 [13:30<03:43,  1.19s/it]
 78%|███████▊  | 681/868 [13:31<03:36,  1.16s/it]
 79%|███████▊  | 682/868 [13:33<03:41,  1.19s/it]
 79%|███████▊  | 683/868 [13:34<03:48,  1.23s/it]
 79%|███████▉  | 684/868 [13:35<03:45,  1.23s/it]
 79%|███████▉  | 685/868 [13:36<03:36,  1.18s/it]
 79%|███████▉  | 686/868 [13:37<03:41,  1.22s/it]
 79%|███████▉  | 687/868 [13:39<03:38,  1.21s/it]
 79%|███████▉  | 688/868 [13:40<03:36,  1.20s/it]
 79%|███████▉  | 689/868 [13:41<03:26,  1.16s/it]
 79%|███████▉  | 690/868 [13:42<03:28,  1.17s/it]
                                                 

 79%|███████▉  | 690/868 [13:42<03:28,  1.17s/it]
 80%|███████▉  | 691/868 [13:43<03:28,  1.18s/it]
 80%|███████▉  | 692/868 [13:45<03:30,  1.19s/it]
 80%|███████▉  | 693/868 [13:46<03:25,  1.18s/it]
 80%|███████▉  | 694/868 [13:47<03:40,  1.27s/it]
 80%|████████  | 695/868 [13:48<03:33,  1.23s/it]
 80%|████████  | 696/868 [13:49<03:29,  1.22s/it]
 80%|████████  | 697/868 [13:51<03:32,  1.24s/it]
 80%|████████  | 698/868 [13:52<03:23,  1.20s/it]
 81%|████████  | 699/868 [13:53<03:17,  1.17s/it]
 81%|████████  | 700/868 [13:54<03:16,  1.17s/it]
                                                 

 81%|████████  | 700/868 [13:54<03:16,  1.17s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 81%|████████  | 701/868 [13:57<04:35,  1.65s/it]
 81%|████████  | 702/868 [13:58<04:07,  1.49s/it]
 81%|████████  | 703/868 [13:59<03:44,  1.36s/it]
 81%|████████  | 704/868 [14:00<03:30,  1.28s/it]
 81%|████████  | 705/868 [14:01<03:28,  1.28s/it]
 81%|████████▏ | 706/868 [14:03<03:17,  1.22s/it]
 81%|████████▏ | 707/868 [14:04<03:10,  1.19s/it]
 82%|████████▏ | 708/868 [14:05<03:11,  1.20s/it]
 82%|████████▏ | 709/868 [14:06<03:17,  1.24s/it]
 82%|████████▏ | 710/868 [14:07<03:07,  1.19s/it]
                                                 

 82%|████████▏ | 710/868 [14:07<03:07,  1.19s/it]
 82%|████████▏ | 711/868 [14:08<03:02,  1.16s/it]
 82%|████████▏ | 712/868 [14:10<03:01,  1.16s/it]
 82%|████████▏ | 713/868 [14:11<03:04,  1.19s/it]
 82%|████████▏ | 714/868 [14:12<02:59,  1.17s/it]
 82%|████████▏ | 715/868 [14:13<03:03,  1.20s/it]
 82%|████████▏ | 716/868 [14:14<03:01,  1.20s/it]
 83%|████████▎ | 717/868 [14:16<03:04,  1.22s/it]
 83%|████████▎ | 718/868 [14:17<02:58,  1.19s/it]
 83%|████████▎ | 719/868 [14:18<02:54,  1.17s/it]
 83%|████████▎ | 720/868 [14:19<02:56,  1.19s/it]
                                                 

 83%|████████▎ | 720/868 [14:19<02:56,  1.19s/it]
 83%|████████▎ | 721/868 [14:20<02:49,  1.15s/it]
 83%|████████▎ | 722/868 [14:21<02:47,  1.15s/it]
 83%|████████▎ | 723/868 [14:23<02:48,  1.16s/it]
 83%|████████▎ | 724/868 [14:24<02:59,  1.25s/it]
 84%|████████▎ | 725/868 [14:25<02:52,  1.21s/it]
 84%|████████▎ | 726/868 [14:26<02:51,  1.21s/it]
 84%|████████▍ | 727/868 [14:27<02:45,  1.17s/it]
 84%|████████▍ | 728/868 [14:29<02:47,  1.20s/it]
 84%|████████▍ | 729/868 [14:30<02:40,  1.16s/it]
 84%|████████▍ | 730/868 [14:31<02:36,  1.14s/it]
                                                 

 84%|████████▍ | 730/868 [14:31<02:36,  1.14s/it]
 84%|████████▍ | 731/868 [14:32<02:34,  1.13s/it]
 84%|████████▍ | 732/868 [14:33<02:39,  1.17s/it]
 84%|████████▍ | 733/868 [14:34<02:33,  1.14s/it]
 85%|████████▍ | 734/868 [14:35<02:28,  1.11s/it]
 85%|████████▍ | 735/868 [14:36<02:26,  1.10s/it]
 85%|████████▍ | 736/868 [14:38<02:31,  1.15s/it]
 85%|████████▍ | 737/868 [14:39<02:29,  1.14s/it]
 85%|████████▌ | 738/868 [14:40<02:25,  1.12s/it]
 85%|████████▌ | 739/868 [14:41<02:23,  1.11s/it]
 85%|████████▌ | 740/868 [14:42<02:28,  1.16s/it]
                                                 

 85%|████████▌ | 740/868 [14:42<02:28,  1.16s/it]
 85%|████████▌ | 741/868 [14:43<02:28,  1.17s/it]
 85%|████████▌ | 742/868 [14:45<02:29,  1.19s/it]
 86%|████████▌ | 743/868 [14:46<02:32,  1.22s/it]
 86%|████████▌ | 744/868 [14:47<02:27,  1.19s/it]
 86%|████████▌ | 745/868 [14:48<02:25,  1.18s/it]
 86%|████████▌ | 746/868 [14:49<02:20,  1.16s/it]
 86%|████████▌ | 747/868 [14:51<02:22,  1.18s/it]
 86%|████████▌ | 748/868 [14:52<02:19,  1.16s/it]
 86%|████████▋ | 749/868 [14:53<02:20,  1.18s/it]
 86%|████████▋ | 750/868 [14:54<02:18,  1.18s/it]
                                                 

 86%|████████▋ | 750/868 [14:54<02:18,  1.18s/it]
 87%|████████▋ | 751/868 [14:55<02:21,  1.21s/it]
 87%|████████▋ | 752/868 [14:56<02:19,  1.20s/it]
 87%|████████▋ | 753/868 [14:58<02:13,  1.16s/it]
 87%|████████▋ | 754/868 [14:59<02:17,  1.20s/it]
 87%|████████▋ | 755/868 [15:00<02:25,  1.29s/it]
 87%|████████▋ | 756/868 [15:02<02:20,  1.25s/it]
 87%|████████▋ | 757/868 [15:03<02:21,  1.27s/it]
 87%|████████▋ | 758/868 [15:04<02:15,  1.23s/it]
 87%|████████▋ | 759/868 [15:05<02:21,  1.30s/it]
 88%|████████▊ | 760/868 [15:07<02:13,  1.24s/it]
                                                 

 88%|████████▊ | 760/868 [15:07<02:13,  1.24s/it]
 88%|████████▊ | 761/868 [15:08<02:11,  1.23s/it]
 88%|████████▊ | 762/868 [15:09<02:11,  1.24s/it]
 88%|████████▊ | 763/868 [15:10<02:06,  1.20s/it]
 88%|████████▊ | 764/868 [15:11<02:03,  1.18s/it]
 88%|████████▊ | 765/868 [15:12<01:59,  1.16s/it]
 88%|████████▊ | 766/868 [15:14<02:00,  1.18s/it]
 88%|████████▊ | 767/868 [15:15<01:55,  1.15s/it]
 88%|████████▊ | 768/868 [15:16<01:54,  1.15s/it]
 89%|████████▊ | 769/868 [15:17<01:54,  1.15s/it]
 89%|████████▊ | 770/868 [15:18<01:58,  1.21s/it]
                                                 

 89%|████████▊ | 770/868 [15:18<01:58,  1.21s/it]
 89%|████████▉ | 771/868 [15:19<01:54,  1.18s/it]
 89%|████████▉ | 772/868 [15:21<01:50,  1.15s/it]
 89%|████████▉ | 773/868 [15:22<01:47,  1.13s/it]
 89%|████████▉ | 774/868 [15:23<01:50,  1.17s/it]
 89%|████████▉ | 775/868 [15:24<01:49,  1.18s/it]
 89%|████████▉ | 776/868 [15:25<01:44,  1.14s/it]
 90%|████████▉ | 777/868 [15:26<01:42,  1.12s/it]
 90%|████████▉ | 778/868 [15:27<01:44,  1.16s/it]
 90%|████████▉ | 779/868 [15:29<01:42,  1.15s/it]
 90%|████████▉ | 780/868 [15:30<01:40,  1.15s/it]
                                                 

 90%|████████▉ | 780/868 [15:30<01:40,  1.15s/it]
 90%|████████▉ | 781/868 [15:31<01:43,  1.18s/it]
 90%|█████████ | 782/868 [15:32<01:38,  1.15s/it]
 90%|█████████ | 783/868 [15:33<01:39,  1.16s/it]
 90%|█████████ | 784/868 [15:34<01:37,  1.16s/it]
 90%|█████████ | 785/868 [15:36<01:39,  1.20s/it]
 91%|█████████ | 786/868 [15:37<01:36,  1.17s/it]
 91%|█████████ | 787/868 [15:38<01:36,  1.19s/it]
 91%|█████████ | 788/868 [15:39<01:35,  1.19s/it]
 91%|█████████ | 789/868 [15:40<01:36,  1.22s/it]
 91%|█████████ | 790/868 [15:42<01:31,  1.18s/it]
                                                 

 91%|█████████ | 790/868 [15:42<01:31,  1.18s/it]
 91%|█████████ | 791/868 [15:43<01:29,  1.16s/it]
 91%|█████████ | 792/868 [15:44<01:26,  1.14s/it]
 91%|█████████▏| 793/868 [15:45<01:27,  1.17s/it]
 91%|█████████▏| 794/868 [15:46<01:23,  1.13s/it]
 92%|█████████▏| 795/868 [15:47<01:22,  1.13s/it]
 92%|█████████▏| 796/868 [15:48<01:24,  1.17s/it]
 92%|█████████▏| 797/868 [15:50<01:21,  1.15s/it]
 92%|█████████▏| 798/868 [15:51<01:20,  1.15s/it]
 92%|█████████▏| 799/868 [15:52<01:18,  1.14s/it]
 92%|█████████▏| 800/868 [15:53<01:21,  1.20s/it]
                                                 

 92%|█████████▏| 800/868 [15:53<01:21,  1.20s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(

 92%|█████████▏| 801/868 [15:56<01:44,  1.56s/it]
 92%|█████████▏| 802/868 [15:57<01:34,  1.42s/it]
 93%|█████████▎| 803/868 [15:58<01:25,  1.31s/it]
 93%|█████████▎| 804/868 [15:59<01:22,  1.30s/it]
 93%|█████████▎| 805/868 [16:00<01:18,  1.25s/it]
 93%|█████████▎| 806/868 [16:01<01:14,  1.20s/it]
 93%|█████████▎| 807/868 [16:02<01:11,  1.17s/it]
 93%|█████████▎| 808/868 [16:04<01:15,  1.27s/it]
 93%|█████████▎| 809/868 [16:05<01:11,  1.22s/it]
 93%|█████████▎| 810/868 [16:06<01:08,  1.18s/it]
                                                 

 93%|█████████▎| 810/868 [16:06<01:08,  1.18s/it]
 93%|█████████▎| 811/868 [16:07<01:06,  1.17s/it]
 94%|█████████▎| 812/868 [16:08<01:07,  1.20s/it]
 94%|█████████▎| 813/868 [16:10<01:04,  1.18s/it]
 94%|█████████▍| 814/868 [16:11<01:03,  1.18s/it]
 94%|█████████▍| 815/868 [16:12<01:01,  1.15s/it]
 94%|█████████▍| 816/868 [16:13<01:02,  1.20s/it]
 94%|█████████▍| 817/868 [16:14<00:59,  1.17s/it]
 94%|█████████▍| 818/868 [16:15<00:57,  1.14s/it]
 94%|█████████▍| 819/868 [16:17<00:58,  1.20s/it]
 94%|█████████▍| 820/868 [16:18<00:56,  1.18s/it]
                                                 

 94%|█████████▍| 820/868 [16:18<00:56,  1.18s/it]
 95%|█████████▍| 821/868 [16:19<00:54,  1.15s/it]
 95%|█████████▍| 822/868 [16:20<00:53,  1.17s/it]
 95%|█████████▍| 823/868 [16:21<00:54,  1.21s/it]
 95%|█████████▍| 824/868 [16:22<00:52,  1.18s/it]
 95%|█████████▌| 825/868 [16:24<00:49,  1.15s/it]
 95%|█████████▌| 826/868 [16:25<00:48,  1.16s/it]
 95%|█████████▌| 827/868 [16:26<00:49,  1.20s/it]
 95%|█████████▌| 828/868 [16:27<00:46,  1.16s/it]
 96%|█████████▌| 829/868 [16:28<00:44,  1.13s/it]
 96%|█████████▌| 830/868 [16:29<00:43,  1.15s/it]
                                                 

 96%|█████████▌| 830/868 [16:29<00:43,  1.15s/it]
 96%|█████████▌| 831/868 [16:31<00:44,  1.20s/it]
 96%|█████████▌| 832/868 [16:32<00:42,  1.17s/it]
 96%|█████████▌| 833/868 [16:33<00:40,  1.16s/it]
 96%|█████████▌| 834/868 [16:34<00:38,  1.13s/it]
 96%|█████████▌| 835/868 [16:35<00:40,  1.21s/it]
 96%|█████████▋| 836/868 [16:36<00:37,  1.17s/it]
 96%|█████████▋| 837/868 [16:38<00:35,  1.16s/it]
 97%|█████████▋| 838/868 [16:39<00:35,  1.20s/it]
 97%|█████████▋| 839/868 [16:40<00:34,  1.20s/it]
 97%|█████████▋| 840/868 [16:41<00:32,  1.17s/it]
                                                 

 97%|█████████▋| 840/868 [16:41<00:32,  1.17s/it]
 97%|█████████▋| 841/868 [16:42<00:30,  1.15s/it]
 97%|█████████▋| 842/868 [16:44<00:31,  1.21s/it]
 97%|█████████▋| 843/868 [16:45<00:29,  1.18s/it]
 97%|█████████▋| 844/868 [16:46<00:27,  1.14s/it]
 97%|█████████▋| 845/868 [16:47<00:26,  1.16s/it]
 97%|█████████▋| 846/868 [16:48<00:26,  1.20s/it]
 98%|█████████▊| 847/868 [16:49<00:24,  1.16s/it]
 98%|█████████▊| 848/868 [16:51<00:23,  1.17s/it]
 98%|█████████▊| 849/868 [16:52<00:22,  1.17s/it]
 98%|█████████▊| 850/868 [16:53<00:21,  1.18s/it]
                                                 

 98%|█████████▊| 850/868 [16:53<00:21,  1.18s/it]
 98%|█████████▊| 851/868 [16:54<00:19,  1.15s/it]
 98%|█████████▊| 852/868 [16:55<00:18,  1.16s/it]
 98%|█████████▊| 853/868 [16:57<00:18,  1.23s/it]
 98%|█████████▊| 854/868 [16:58<00:17,  1.24s/it]
 99%|█████████▊| 855/868 [16:59<00:15,  1.19s/it]
 99%|█████████▊| 856/868 [17:00<00:13,  1.15s/it]
 99%|█████████▊| 857/868 [17:01<00:12,  1.18s/it]
 99%|█████████▉| 858/868 [17:02<00:11,  1.15s/it]
 99%|█████████▉| 859/868 [17:03<00:10,  1.16s/it]
 99%|█████████▉| 860/868 [17:05<00:09,  1.18s/it]
                                                 

 99%|█████████▉| 860/868 [17:05<00:09,  1.18s/it]
 99%|█████████▉| 861/868 [17:06<00:08,  1.19s/it]
 99%|█████████▉| 862/868 [17:07<00:07,  1.22s/it]
 99%|█████████▉| 863/868 [17:08<00:06,  1.21s/it]
100%|█████████▉| 864/868 [17:10<00:04,  1.23s/it]
100%|█████████▉| 865/868 [17:11<00:03,  1.23s/it]
100%|█████████▉| 866/868 [17:12<00:02,  1.20s/it]
100%|█████████▉| 867/868 [17:13<00:01,  1.17s/it]
100%|██████████| 868/868 [17:14<00:00,  1.20s/it]
                                                 

100%|██████████| 868/868 [17:16<00:00,  1.20s/it]
100%|██████████| 868/868 [17:16<00:00,  1.19s/it]
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:   eval_dataset_size ▁
wandb:    test_split_ratio ▁
wandb:  total_dataset_size ▁
wandb:    total_parameters ▁
wandb:         train/epoch ▁▁▁▂▂▂▂▂▂▃▃▃▄▄▄▄▄▄▄▅▅▅▅▅▅▅▅▆▆▆▆▆▆▆▇▇▇▇██
wandb:   train/global_step ▁▁▁▁▁▂▂▂▂▃▃▃▃▄▄▄▄▄▄▄▅▅▅▅▅▅▅▅▆▆▇▇▇▇▇▇████
wandb:     train/grad_norm █▁▁▁▁▁▂▂▂▂▂▂▂▂▂▃▃▃▃▃▃▃▂▂▂▂▃▂▃▂▂▂▃▂▂▂▂▂▂▁
wandb: train/learning_rate █████▇▇▇▇▆▆▆▆▆▆▅▅▅▅▅▅▅▅▄▄▄▄▄▃▃▃▃▃▃▃▂▂▂▂▁
wandb:          train/loss █▆▆▆▅▅▅▅▅▄▄▄▃▃▃▃▂▂▂▂▁▂▁▁▂▁▁▁▂▁▁▁▁▁▁▁▁▁▁▁
wandb:  train_dataset_size ▁
wandb: 
wandb: Run summary:
wandb:        eval_dataset_size 695
wandb:         final_output_dir /home/<USER>/m/maryam....
wandb:         test_split_ratio 0.1
wandb:       total_dataset_size 6949
wandb:               total_flos 2.52733385253888e+17
wandb:         total_parameters 7298355200
wandb:              train/epoch 3.9908
wandb:        train/global_step 868
wandb:          train/grad_norm 1.50138
wandb:      train/learning_rate 0.0
wandb:               train/loss 0.1235
wandb:       train_dataset_size 6254
wandb:               train_loss 0.49822
wandb:            train_runtime 1036.1588
wandb: train_samples_per_second 26.826
wandb:   train_steps_per_second 0.838
wandb:       training_completed True
wandb: 
wandb: 🚀 View run worthy-spaceship-2 at: https://wandb.ai/sarath-chandar/lora-finetuning/runs/rst3632g
wandb: ⭐️ View project at: https://wandb.ai/sarath-chandar/lora-finetuning
wandb: Synced 5 W&B file(s), 0 media file(s), 0 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250625_162252-rst3632g/logs
