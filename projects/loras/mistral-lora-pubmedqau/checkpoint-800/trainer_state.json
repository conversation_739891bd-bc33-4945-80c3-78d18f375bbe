{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.56, "eval_steps": 500, "global_step": 800, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0032, "grad_norm": 9.59710693359375, "learning_rate": 0.0001998397435897436, "loss": 2.4652, "step": 1}, {"epoch": 0.032, "grad_norm": 1.985697865486145, "learning_rate": 0.0001983974358974359, "loss": 1.8381, "step": 10}, {"epoch": 0.064, "grad_norm": 1.945962905883789, "learning_rate": 0.00019679487179487178, "loss": 1.8122, "step": 20}, {"epoch": 0.096, "grad_norm": 1.823306679725647, "learning_rate": 0.0001951923076923077, "loss": 1.7898, "step": 30}, {"epoch": 0.128, "grad_norm": 2.123161554336548, "learning_rate": 0.0001935897435897436, "loss": 1.7953, "step": 40}, {"epoch": 0.16, "grad_norm": 1.8922276496887207, "learning_rate": 0.0001919871794871795, "loss": 1.8208, "step": 50}, {"epoch": 0.192, "grad_norm": 1.8789223432540894, "learning_rate": 0.00019038461538461538, "loss": 1.81, "step": 60}, {"epoch": 0.224, "grad_norm": 1.8132027387619019, "learning_rate": 0.00018878205128205127, "loss": 1.8162, "step": 70}, {"epoch": 0.256, "grad_norm": 1.8831301927566528, "learning_rate": 0.0001871794871794872, "loss": 1.8152, "step": 80}, {"epoch": 0.288, "grad_norm": 1.8895668983459473, "learning_rate": 0.0001855769230769231, "loss": 1.8404, "step": 90}, {"epoch": 0.32, "grad_norm": 2.1043460369110107, "learning_rate": 0.00018397435897435897, "loss": 1.827, "step": 100}, {"epoch": 0.352, "grad_norm": 2.038529872894287, "learning_rate": 0.00018237179487179487, "loss": 1.775, "step": 110}, {"epoch": 0.384, "grad_norm": 1.9233695268630981, "learning_rate": 0.00018076923076923077, "loss": 1.8647, "step": 120}, {"epoch": 0.416, "grad_norm": 2.0395214557647705, "learning_rate": 0.0001791666666666667, "loss": 1.8191, "step": 130}, {"epoch": 0.448, "grad_norm": 1.9629511833190918, "learning_rate": 0.00017756410256410257, "loss": 1.8252, "step": 140}, {"epoch": 0.48, "grad_norm": 1.9993902444839478, "learning_rate": 0.00017596153846153846, "loss": 1.7672, "step": 150}, {"epoch": 0.512, "grad_norm": 2.0912840366363525, "learning_rate": 0.00017435897435897436, "loss": 1.8222, "step": 160}, {"epoch": 0.544, "grad_norm": 1.9819002151489258, "learning_rate": 0.00017275641025641026, "loss": 1.8172, "step": 170}, {"epoch": 0.576, "grad_norm": 2.123680353164673, "learning_rate": 0.00017115384615384616, "loss": 1.8267, "step": 180}, {"epoch": 0.608, "grad_norm": 2.002261161804199, "learning_rate": 0.00016955128205128206, "loss": 1.8372, "step": 190}, {"epoch": 0.64, "grad_norm": 1.9735636711120605, "learning_rate": 0.00016794871794871796, "loss": 1.7959, "step": 200}, {"epoch": 0.672, "grad_norm": 2.174168825149536, "learning_rate": 0.00016634615384615386, "loss": 1.8112, "step": 210}, {"epoch": 0.704, "grad_norm": 1.9978160858154297, "learning_rate": 0.00016474358974358976, "loss": 1.7721, "step": 220}, {"epoch": 0.736, "grad_norm": 2.0502612590789795, "learning_rate": 0.00016314102564102565, "loss": 1.8266, "step": 230}, {"epoch": 0.768, "grad_norm": 1.971590518951416, "learning_rate": 0.00016153846153846155, "loss": 1.8213, "step": 240}, {"epoch": 0.8, "grad_norm": 2.067819356918335, "learning_rate": 0.00015993589743589745, "loss": 1.8012, "step": 250}, {"epoch": 0.832, "grad_norm": 2.4471848011016846, "learning_rate": 0.00015833333333333332, "loss": 1.8396, "step": 260}, {"epoch": 0.864, "grad_norm": 2.133666515350342, "learning_rate": 0.00015673076923076925, "loss": 1.8098, "step": 270}, {"epoch": 0.896, "grad_norm": 1.9956107139587402, "learning_rate": 0.00015512820512820515, "loss": 1.8396, "step": 280}, {"epoch": 0.928, "grad_norm": 2.1988935470581055, "learning_rate": 0.00015352564102564105, "loss": 1.8215, "step": 290}, {"epoch": 0.96, "grad_norm": 1.995598316192627, "learning_rate": 0.00015192307692307692, "loss": 1.8063, "step": 300}, {"epoch": 0.992, "grad_norm": 2.063180685043335, "learning_rate": 0.00015032051282051282, "loss": 1.8113, "step": 310}, {"epoch": 1.024, "grad_norm": 2.0523080825805664, "learning_rate": 0.00014871794871794872, "loss": 1.5019, "step": 320}, {"epoch": 1.056, "grad_norm": 2.428623676300049, "learning_rate": 0.00014711538461538464, "loss": 1.381, "step": 330}, {"epoch": 1.088, "grad_norm": 2.3025665283203125, "learning_rate": 0.00014551282051282051, "loss": 1.3574, "step": 340}, {"epoch": 1.12, "grad_norm": 2.6631922721862793, "learning_rate": 0.0001439102564102564, "loss": 1.3624, "step": 350}, {"epoch": 1.152, "grad_norm": 2.7005655765533447, "learning_rate": 0.0001423076923076923, "loss": 1.3491, "step": 360}, {"epoch": 1.184, "grad_norm": 3.1635324954986572, "learning_rate": 0.0001407051282051282, "loss": 1.348, "step": 370}, {"epoch": 1.216, "grad_norm": 2.8461594581604004, "learning_rate": 0.0001391025641025641, "loss": 1.3603, "step": 380}, {"epoch": 1.248, "grad_norm": 2.735116481781006, "learning_rate": 0.0001375, "loss": 1.3448, "step": 390}, {"epoch": 1.28, "grad_norm": 3.0248851776123047, "learning_rate": 0.0001358974358974359, "loss": 1.3714, "step": 400}, {"epoch": 1.312, "grad_norm": 2.778353452682495, "learning_rate": 0.00013429487179487178, "loss": 1.3625, "step": 410}, {"epoch": 1.3439999999999999, "grad_norm": 2.733949661254883, "learning_rate": 0.0001326923076923077, "loss": 1.3607, "step": 420}, {"epoch": 1.376, "grad_norm": 2.909402370452881, "learning_rate": 0.0001310897435897436, "loss": 1.3702, "step": 430}, {"epoch": 1.408, "grad_norm": 2.908738613128662, "learning_rate": 0.0001294871794871795, "loss": 1.3472, "step": 440}, {"epoch": 1.44, "grad_norm": 2.8309216499328613, "learning_rate": 0.00012788461538461537, "loss": 1.3583, "step": 450}, {"epoch": 1.472, "grad_norm": 2.947791814804077, "learning_rate": 0.00012628205128205127, "loss": 1.3711, "step": 460}, {"epoch": 1.504, "grad_norm": 3.2139456272125244, "learning_rate": 0.0001246794871794872, "loss": 1.3545, "step": 470}, {"epoch": 1.536, "grad_norm": 2.9597814083099365, "learning_rate": 0.0001230769230769231, "loss": 1.3626, "step": 480}, {"epoch": 1.568, "grad_norm": 3.010314464569092, "learning_rate": 0.00012147435897435897, "loss": 1.3579, "step": 490}, {"epoch": 1.6, "grad_norm": 2.7516047954559326, "learning_rate": 0.00011987179487179487, "loss": 1.3632, "step": 500}, {"epoch": 1.6320000000000001, "grad_norm": 3.15960431098938, "learning_rate": 0.00011826923076923078, "loss": 1.3654, "step": 510}, {"epoch": 1.6640000000000001, "grad_norm": 3.1783995628356934, "learning_rate": 0.00011666666666666668, "loss": 1.38, "step": 520}, {"epoch": 1.696, "grad_norm": 2.958502769470215, "learning_rate": 0.00011506410256410256, "loss": 1.3595, "step": 530}, {"epoch": 1.728, "grad_norm": 2.9373996257781982, "learning_rate": 0.00011346153846153846, "loss": 1.3593, "step": 540}, {"epoch": 1.76, "grad_norm": 3.260676622390747, "learning_rate": 0.00011185897435897436, "loss": 1.372, "step": 550}, {"epoch": 1.792, "grad_norm": 3.127492666244507, "learning_rate": 0.00011025641025641027, "loss": 1.3471, "step": 560}, {"epoch": 1.8239999999999998, "grad_norm": 2.8735198974609375, "learning_rate": 0.00010865384615384615, "loss": 1.3467, "step": 570}, {"epoch": 1.8559999999999999, "grad_norm": 2.97033429145813, "learning_rate": 0.00010705128205128206, "loss": 1.3687, "step": 580}, {"epoch": 1.888, "grad_norm": 3.0956075191497803, "learning_rate": 0.00010544871794871796, "loss": 1.3678, "step": 590}, {"epoch": 1.92, "grad_norm": 3.021268606185913, "learning_rate": 0.00010384615384615386, "loss": 1.3541, "step": 600}, {"epoch": 1.952, "grad_norm": 2.9104838371276855, "learning_rate": 0.00010224358974358974, "loss": 1.3675, "step": 610}, {"epoch": 1.984, "grad_norm": 3.2636961936950684, "learning_rate": 0.00010064102564102564, "loss": 1.371, "step": 620}, {"epoch": 2.016, "grad_norm": 4.362855434417725, "learning_rate": 9.903846153846155e-05, "loss": 1.1467, "step": 630}, {"epoch": 2.048, "grad_norm": 4.002874851226807, "learning_rate": 9.743589743589744e-05, "loss": 0.7945, "step": 640}, {"epoch": 2.08, "grad_norm": 5.074804782867432, "learning_rate": 9.583333333333334e-05, "loss": 0.7841, "step": 650}, {"epoch": 2.112, "grad_norm": 4.6274027824401855, "learning_rate": 9.423076923076924e-05, "loss": 0.7962, "step": 660}, {"epoch": 2.144, "grad_norm": 4.557649612426758, "learning_rate": 9.262820512820513e-05, "loss": 0.7936, "step": 670}, {"epoch": 2.176, "grad_norm": 4.553938865661621, "learning_rate": 9.102564102564103e-05, "loss": 0.7989, "step": 680}, {"epoch": 2.208, "grad_norm": 3.813526153564453, "learning_rate": 8.942307692307693e-05, "loss": 0.7738, "step": 690}, {"epoch": 2.24, "grad_norm": 4.426795959472656, "learning_rate": 8.782051282051283e-05, "loss": 0.8151, "step": 700}, {"epoch": 2.2720000000000002, "grad_norm": 4.850522518157959, "learning_rate": 8.621794871794873e-05, "loss": 0.8173, "step": 710}, {"epoch": 2.304, "grad_norm": 4.148812770843506, "learning_rate": 8.461538461538461e-05, "loss": 0.7911, "step": 720}, {"epoch": 2.336, "grad_norm": 4.55037260055542, "learning_rate": 8.301282051282053e-05, "loss": 0.8224, "step": 730}, {"epoch": 2.368, "grad_norm": 4.2753095626831055, "learning_rate": 8.141025641025641e-05, "loss": 0.8039, "step": 740}, {"epoch": 2.4, "grad_norm": 4.650018215179443, "learning_rate": 7.980769230769231e-05, "loss": 0.8075, "step": 750}, {"epoch": 2.432, "grad_norm": 4.560468673706055, "learning_rate": 7.820512820512821e-05, "loss": 0.7936, "step": 760}, {"epoch": 2.464, "grad_norm": 4.526761531829834, "learning_rate": 7.660256410256411e-05, "loss": 0.8074, "step": 770}, {"epoch": 2.496, "grad_norm": 4.465812683105469, "learning_rate": 7.500000000000001e-05, "loss": 0.815, "step": 780}, {"epoch": 2.528, "grad_norm": 4.8853759765625, "learning_rate": 7.339743589743589e-05, "loss": 0.8028, "step": 790}, {"epoch": 2.56, "grad_norm": 4.296356678009033, "learning_rate": 7.17948717948718e-05, "loss": 0.7988, "step": 800}], "logging_steps": 10, "max_steps": 1248, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.747684096671744e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}