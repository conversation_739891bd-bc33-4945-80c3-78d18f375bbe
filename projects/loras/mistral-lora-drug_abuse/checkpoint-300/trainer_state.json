{"best_metric": null, "best_model_checkpoint": null, "epoch": 1.3793103448275863, "eval_steps": 500, "global_step": 300, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 6.234731197357178, "learning_rate": 0.00019976958525345624, "loss": 1.8935, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.594862461090088, "learning_rate": 0.00019769585253456222, "loss": 1.4647, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.6214826107025146, "learning_rate": 0.00019539170506912442, "loss": 1.3602, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.57422935962677, "learning_rate": 0.00019308755760368663, "loss": 1.3275, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.5863317251205444, "learning_rate": 0.00019078341013824886, "loss": 1.2737, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.5975720882415771, "learning_rate": 0.00018847926267281107, "loss": 1.2582, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.76157808303833, "learning_rate": 0.00018617511520737328, "loss": 1.199, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.7999874353408813, "learning_rate": 0.00018387096774193548, "loss": 1.1699, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.6948373317718506, "learning_rate": 0.0001815668202764977, "loss": 1.1578, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.6032253503799438, "learning_rate": 0.0001792626728110599, "loss": 1.1209, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.9902859926223755, "learning_rate": 0.00017695852534562213, "loss": 1.102, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.7655659914016724, "learning_rate": 0.00017465437788018436, "loss": 1.0786, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.875245451927185, "learning_rate": 0.00017235023041474657, "loss": 1.0593, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.9501055479049683, "learning_rate": 0.00017004608294930878, "loss": 1.0335, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.9582949876785278, "learning_rate": 0.00016774193548387098, "loss": 1.0484, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.9045233726501465, "learning_rate": 0.0001654377880184332, "loss": 1.071, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 2.0465800762176514, "learning_rate": 0.0001631336405529954, "loss": 0.9876, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.9675742387771606, "learning_rate": 0.0001608294930875576, "loss": 0.9981, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 2.070966958999634, "learning_rate": 0.00015852534562211984, "loss": 0.9861, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 2.1034274101257324, "learning_rate": 0.00015622119815668204, "loss": 0.9206, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 2.161008358001709, "learning_rate": 0.00015391705069124425, "loss": 0.9659, "step": 200}, {"epoch": 0.9655172413793104, "grad_norm": 2.1765284538269043, "learning_rate": 0.00015161290322580646, "loss": 0.9034, "step": 210}, {"epoch": 1.0114942528735633, "grad_norm": 2.1035208702087402, "learning_rate": 0.00014930875576036866, "loss": 0.7634, "step": 220}, {"epoch": 1.0574712643678161, "grad_norm": 2.101219654083252, "learning_rate": 0.00014700460829493087, "loss": 0.5987, "step": 230}, {"epoch": 1.103448275862069, "grad_norm": 2.491426467895508, "learning_rate": 0.0001447004608294931, "loss": 0.5891, "step": 240}, {"epoch": 1.1494252873563218, "grad_norm": 2.288226842880249, "learning_rate": 0.0001423963133640553, "loss": 0.5734, "step": 250}, {"epoch": 1.1954022988505748, "grad_norm": 2.4574058055877686, "learning_rate": 0.00014009216589861752, "loss": 0.5509, "step": 260}, {"epoch": 1.2413793103448276, "grad_norm": 2.258432388305664, "learning_rate": 0.00013778801843317972, "loss": 0.5335, "step": 270}, {"epoch": 1.2873563218390804, "grad_norm": 2.2584521770477295, "learning_rate": 0.00013548387096774193, "loss": 0.5679, "step": 280}, {"epoch": 1.3333333333333333, "grad_norm": 2.4729013442993164, "learning_rate": 0.00013317972350230414, "loss": 0.5457, "step": 290}, {"epoch": 1.3793103448275863, "grad_norm": 2.67273211479187, "learning_rate": 0.00013087557603686637, "loss": 0.5135, "step": 300}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 8.7439206564864e+16, "train_batch_size": 16, "trial_name": null, "trial_params": null}