{"best_metric": null, "best_model_checkpoint": null, "epoch": 3.67816091954023, "eval_steps": 500, "global_step": 800, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 6.234731197357178, "learning_rate": 0.00019976958525345624, "loss": 1.8935, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.594862461090088, "learning_rate": 0.00019769585253456222, "loss": 1.4647, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.6214826107025146, "learning_rate": 0.00019539170506912442, "loss": 1.3602, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.57422935962677, "learning_rate": 0.00019308755760368663, "loss": 1.3275, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.5863317251205444, "learning_rate": 0.00019078341013824886, "loss": 1.2737, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.5975720882415771, "learning_rate": 0.00018847926267281107, "loss": 1.2582, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.76157808303833, "learning_rate": 0.00018617511520737328, "loss": 1.199, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.7999874353408813, "learning_rate": 0.00018387096774193548, "loss": 1.1699, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.6948373317718506, "learning_rate": 0.0001815668202764977, "loss": 1.1578, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.6032253503799438, "learning_rate": 0.0001792626728110599, "loss": 1.1209, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.9902859926223755, "learning_rate": 0.00017695852534562213, "loss": 1.102, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.7655659914016724, "learning_rate": 0.00017465437788018436, "loss": 1.0786, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.875245451927185, "learning_rate": 0.00017235023041474657, "loss": 1.0593, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.9501055479049683, "learning_rate": 0.00017004608294930878, "loss": 1.0335, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.9582949876785278, "learning_rate": 0.00016774193548387098, "loss": 1.0484, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.9045233726501465, "learning_rate": 0.0001654377880184332, "loss": 1.071, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 2.0465800762176514, "learning_rate": 0.0001631336405529954, "loss": 0.9876, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.9675742387771606, "learning_rate": 0.0001608294930875576, "loss": 0.9981, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 2.070966958999634, "learning_rate": 0.00015852534562211984, "loss": 0.9861, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 2.1034274101257324, "learning_rate": 0.00015622119815668204, "loss": 0.9206, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 2.161008358001709, "learning_rate": 0.00015391705069124425, "loss": 0.9659, "step": 200}, {"epoch": 0.9655172413793104, "grad_norm": 2.1765284538269043, "learning_rate": 0.00015161290322580646, "loss": 0.9034, "step": 210}, {"epoch": 1.0114942528735633, "grad_norm": 2.1035208702087402, "learning_rate": 0.00014930875576036866, "loss": 0.7634, "step": 220}, {"epoch": 1.0574712643678161, "grad_norm": 2.101219654083252, "learning_rate": 0.00014700460829493087, "loss": 0.5987, "step": 230}, {"epoch": 1.103448275862069, "grad_norm": 2.491426467895508, "learning_rate": 0.0001447004608294931, "loss": 0.5891, "step": 240}, {"epoch": 1.1494252873563218, "grad_norm": 2.288226842880249, "learning_rate": 0.0001423963133640553, "loss": 0.5734, "step": 250}, {"epoch": 1.1954022988505748, "grad_norm": 2.4574058055877686, "learning_rate": 0.00014009216589861752, "loss": 0.5509, "step": 260}, {"epoch": 1.2413793103448276, "grad_norm": 2.258432388305664, "learning_rate": 0.00013778801843317972, "loss": 0.5335, "step": 270}, {"epoch": 1.2873563218390804, "grad_norm": 2.2584521770477295, "learning_rate": 0.00013548387096774193, "loss": 0.5679, "step": 280}, {"epoch": 1.3333333333333333, "grad_norm": 2.4729013442993164, "learning_rate": 0.00013317972350230414, "loss": 0.5457, "step": 290}, {"epoch": 1.3793103448275863, "grad_norm": 2.67273211479187, "learning_rate": 0.00013087557603686637, "loss": 0.5135, "step": 300}, {"epoch": 1.4252873563218391, "grad_norm": 2.5501201152801514, "learning_rate": 0.00012857142857142858, "loss": 0.5337, "step": 310}, {"epoch": 1.471264367816092, "grad_norm": 2.581540107727051, "learning_rate": 0.0001262672811059908, "loss": 0.5308, "step": 320}, {"epoch": 1.5172413793103448, "grad_norm": 2.353632688522339, "learning_rate": 0.00012396313364055302, "loss": 0.5139, "step": 330}, {"epoch": 1.5632183908045976, "grad_norm": 2.7728238105773926, "learning_rate": 0.00012165898617511522, "loss": 0.4997, "step": 340}, {"epoch": 1.6091954022988506, "grad_norm": 2.596442222595215, "learning_rate": 0.00011935483870967743, "loss": 0.4722, "step": 350}, {"epoch": 1.6551724137931034, "grad_norm": 2.523505687713623, "learning_rate": 0.00011705069124423964, "loss": 0.496, "step": 360}, {"epoch": 1.7011494252873565, "grad_norm": 2.079328775405884, "learning_rate": 0.00011474654377880186, "loss": 0.4816, "step": 370}, {"epoch": 1.7471264367816093, "grad_norm": 2.0721147060394287, "learning_rate": 0.00011244239631336406, "loss": 0.4471, "step": 380}, {"epoch": 1.793103448275862, "grad_norm": 2.609833002090454, "learning_rate": 0.00011013824884792627, "loss": 0.461, "step": 390}, {"epoch": 1.839080459770115, "grad_norm": 2.6138575077056885, "learning_rate": 0.00010783410138248849, "loss": 0.4897, "step": 400}, {"epoch": 1.8850574712643677, "grad_norm": 2.584921360015869, "learning_rate": 0.0001055299539170507, "loss": 0.4656, "step": 410}, {"epoch": 1.9310344827586206, "grad_norm": 2.4103236198425293, "learning_rate": 0.0001032258064516129, "loss": 0.4406, "step": 420}, {"epoch": 1.9770114942528736, "grad_norm": 2.5012571811676025, "learning_rate": 0.00010092165898617512, "loss": 0.4468, "step": 430}, {"epoch": 2.0229885057471266, "grad_norm": 2.195446491241455, "learning_rate": 9.861751152073733e-05, "loss": 0.37, "step": 440}, {"epoch": 2.0689655172413794, "grad_norm": 2.032707929611206, "learning_rate": 9.631336405529955e-05, "loss": 0.2469, "step": 450}, {"epoch": 2.1149425287356323, "grad_norm": 2.5692272186279297, "learning_rate": 9.400921658986176e-05, "loss": 0.2419, "step": 460}, {"epoch": 2.160919540229885, "grad_norm": 2.2111358642578125, "learning_rate": 9.170506912442398e-05, "loss": 0.2395, "step": 470}, {"epoch": 2.206896551724138, "grad_norm": 2.7316598892211914, "learning_rate": 8.940092165898618e-05, "loss": 0.2561, "step": 480}, {"epoch": 2.2528735632183907, "grad_norm": 2.607454538345337, "learning_rate": 8.709677419354839e-05, "loss": 0.2367, "step": 490}, {"epoch": 2.2988505747126435, "grad_norm": 2.770090103149414, "learning_rate": 8.479262672811061e-05, "loss": 0.2219, "step": 500}, {"epoch": 2.344827586206897, "grad_norm": 2.3176426887512207, "learning_rate": 8.248847926267282e-05, "loss": 0.2314, "step": 510}, {"epoch": 2.3908045977011496, "grad_norm": 2.814910411834717, "learning_rate": 8.018433179723502e-05, "loss": 0.2394, "step": 520}, {"epoch": 2.4367816091954024, "grad_norm": 2.3224668502807617, "learning_rate": 7.788018433179723e-05, "loss": 0.2528, "step": 530}, {"epoch": 2.4827586206896552, "grad_norm": 2.853928327560425, "learning_rate": 7.557603686635945e-05, "loss": 0.2401, "step": 540}, {"epoch": 2.528735632183908, "grad_norm": 1.9425122737884521, "learning_rate": 7.327188940092167e-05, "loss": 0.2205, "step": 550}, {"epoch": 2.574712643678161, "grad_norm": 2.1718392372131348, "learning_rate": 7.096774193548388e-05, "loss": 0.239, "step": 560}, {"epoch": 2.6206896551724137, "grad_norm": 2.2732651233673096, "learning_rate": 6.86635944700461e-05, "loss": 0.2483, "step": 570}, {"epoch": 2.6666666666666665, "grad_norm": 2.6080565452575684, "learning_rate": 6.63594470046083e-05, "loss": 0.2469, "step": 580}, {"epoch": 2.7126436781609193, "grad_norm": 2.373870372772217, "learning_rate": 6.405529953917051e-05, "loss": 0.233, "step": 590}, {"epoch": 2.7586206896551726, "grad_norm": 2.406656503677368, "learning_rate": 6.175115207373272e-05, "loss": 0.2566, "step": 600}, {"epoch": 2.8045977011494254, "grad_norm": 2.226036310195923, "learning_rate": 5.944700460829493e-05, "loss": 0.2316, "step": 610}, {"epoch": 2.8505747126436782, "grad_norm": 2.3459818363189697, "learning_rate": 5.714285714285714e-05, "loss": 0.2231, "step": 620}, {"epoch": 2.896551724137931, "grad_norm": 2.0686240196228027, "learning_rate": 5.4838709677419355e-05, "loss": 0.2301, "step": 630}, {"epoch": 2.942528735632184, "grad_norm": 2.1627089977264404, "learning_rate": 5.253456221198156e-05, "loss": 0.2093, "step": 640}, {"epoch": 2.9885057471264367, "grad_norm": 2.615729808807373, "learning_rate": 5.023041474654379e-05, "loss": 0.2165, "step": 650}, {"epoch": 3.0344827586206895, "grad_norm": 1.9701573848724365, "learning_rate": 4.792626728110599e-05, "loss": 0.1522, "step": 660}, {"epoch": 3.0804597701149423, "grad_norm": 2.198946952819824, "learning_rate": 4.562211981566821e-05, "loss": 0.1357, "step": 670}, {"epoch": 3.1264367816091956, "grad_norm": 1.8609991073608398, "learning_rate": 4.3317972350230415e-05, "loss": 0.143, "step": 680}, {"epoch": 3.1724137931034484, "grad_norm": 2.3042073249816895, "learning_rate": 4.101382488479263e-05, "loss": 0.1298, "step": 690}, {"epoch": 3.218390804597701, "grad_norm": 1.7188389301300049, "learning_rate": 3.870967741935484e-05, "loss": 0.13, "step": 700}, {"epoch": 3.264367816091954, "grad_norm": 1.9659851789474487, "learning_rate": 3.640552995391705e-05, "loss": 0.1441, "step": 710}, {"epoch": 3.310344827586207, "grad_norm": 2.459677219390869, "learning_rate": 3.410138248847927e-05, "loss": 0.1275, "step": 720}, {"epoch": 3.3563218390804597, "grad_norm": 2.1026175022125244, "learning_rate": 3.1797235023041475e-05, "loss": 0.1355, "step": 730}, {"epoch": 3.4022988505747125, "grad_norm": 2.287853479385376, "learning_rate": 2.9493087557603688e-05, "loss": 0.1257, "step": 740}, {"epoch": 3.4482758620689653, "grad_norm": 2.369732618331909, "learning_rate": 2.7188940092165898e-05, "loss": 0.1425, "step": 750}, {"epoch": 3.4942528735632186, "grad_norm": 1.6479662656784058, "learning_rate": 2.488479262672811e-05, "loss": 0.1339, "step": 760}, {"epoch": 3.5402298850574714, "grad_norm": 2.2361879348754883, "learning_rate": 2.258064516129032e-05, "loss": 0.1241, "step": 770}, {"epoch": 3.586206896551724, "grad_norm": 2.0438268184661865, "learning_rate": 2.0276497695852538e-05, "loss": 0.1285, "step": 780}, {"epoch": 3.632183908045977, "grad_norm": 1.5818936824798584, "learning_rate": 1.7972350230414748e-05, "loss": 0.1263, "step": 790}, {"epoch": 3.67816091954023, "grad_norm": 1.3734722137451172, "learning_rate": 1.5668202764976958e-05, "loss": 0.1207, "step": 800}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2.331078730573824e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}