  0%|                                                                                                                                                 | 0/6046 [00:00<?, ?it/s]
tensor([[[ 0.5430,  0.1738],
         [ 0.3516, -0.0864],
         [ 0.3379,  0.1177],
         [-0.1729, -0.2969],
         [ 0.2178, -0.7305],
         [ 0.3594, -0.1768],
         [ 0.4160, -0.0184],
         [-0.0347, -0.5391],
         [-0.0923, -0.1650],
         [ 0.0359, -0.2793],
         [ 0.2285, -0.5039],
         [-0.0486, -0.1582],
         [ 0.0908, -0.2715],
         [ 0.2793, -1.1875],
         [ 0.1777, -0.2949],
         [-0.1611, -0.2324],
         [ 0.0981,  0.0052],
         [ 0.2520,  0.0349],
         [ 0.6680,  0.3965],
         [ 0.2676, -0.3457],
         [ 0.1045, -0.1533],
         [-0.2988, -0.4238],
         [ 0.3789,  0.2637],
         [ 0.5977, -0.3574],
         [ 0.2051, -0.2578],
         [ 0.3711,  0.0181],
         [ 0.0099, -0.0430],
         [-0.1660, -0.2461],
         [ 0.2402, -0.2012],
         [ 0.7461,  0.1855],
         [-0.4492, -0.4648],
         [ 0.2275, -0.1582],
         [ 0.3047,  0.0464],
         [ 0.4336,  0.4316],
         [ 0.3184, -1.1094],
         [ 0.3789, -0.2305],
         [ 0.0498, -0.1099],
         [-0.2002, -0.2637],
         [ 0.4805,  0.0209],
         [ 0.3262,  0.0952],
         [ 0.1758, -0.0674],
         [ 0.4082, -0.1235],
         [ 0.1973, -0.0229],
         [-0.0173, -0.1953],
         [ 0.4160, -0.1748],
         [ 0.7227, -0.0422],
         [ 0.2734,  0.2637],
         [ 0.0121, -0.5039],
         [-0.3066, -0.3281],
         [-0.0471, -0.1953],
         [ 0.0422, -0.2109],
         [ 0.4941, -0.1914],
         [ 0.2139,  0.1377],
         [ 0.3906, -0.7188],
         [ 0.2129, -0.2793],
         [ 0.4941,  0.2148],
         [ 0.1416,  0.1172],
         [ 0.0162, -0.1641],
         [ 0.2285, -0.7305],
         [ 0.2031, -0.2471],
         [ 0.8633,  0.3711],
         [ 0.0688, -0.4375],
         [ 0.0659, -0.0320],
         [ 0.3086, -0.0233],
         [ 0.5156,  0.2773],
         [ 0.5234, -0.1157],
         [-0.1377, -0.1982],
         [ 0.4824,  0.0464],
         [ 0.2852,  0.1060],
         [ 0.2676, -0.2441],
         [ 0.2832,  0.0293],
         [ 0.5938, -0.1660],
         [ 0.5234, -0.0903],
         [ 0.3418, -0.0693],
         [-0.2041, -0.4473],
         [ 0.2207, -0.0488],
         [ 0.2021,  0.0187],
         [ 0.5703, -0.4922],
         [ 0.3203, -0.3789],
         [ 0.8984, -0.2793],
         [-0.1445, -0.5859],
         [ 0.2949,  0.0420],
         [ 0.3652, -0.5117],
         [ 0.2930, -0.2441],
         [ 0.1035, -0.4043],
         [ 0.4434,  0.2227],
         [ 0.0242,  0.0119],
         [ 0.0342, -0.1191],
         [-0.1777, -0.2793],
         [ 0.3438, -0.1699]]], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<TopkBackward0>)
tensor([[[0.5898, 0.4082],
         [0.6094, 0.3926],
         [0.5547, 0.4453],
         [0.5312, 0.4688],
         [0.7227, 0.2793],
         [0.6328, 0.3691],
         [0.6055, 0.3926],
         [0.6250, 0.3770],
         [0.5195, 0.4824],
         [0.5781, 0.4219],
         [0.6758, 0.3242],
         [0.5273, 0.4727],
         [0.5898, 0.4102],
         [0.8125, 0.1875],
         [0.6172, 0.3848],
         [0.5195, 0.4824],
         [0.5234, 0.4766],
         [0.5547, 0.4453],
         [0.5664, 0.4316],
         [0.6484, 0.3516],
         [0.5625, 0.4355],
         [0.5312, 0.4688],
         [0.5273, 0.4707],
         [0.7227, 0.2773],
         [0.6133, 0.3867],
         [0.5859, 0.4121],
         [0.5117, 0.4863],
         [0.5195, 0.4805],
         [0.6094, 0.3906],
         [0.6367, 0.3633],
         [0.5039, 0.4961],
         [0.5938, 0.4043],
         [0.5625, 0.4355],
         [0.5000, 0.5000],
         [0.8047, 0.1934],
         [0.6484, 0.3516],
         [0.5391, 0.4609],
         [0.5156, 0.4844],
         [0.6133, 0.3867],
         [0.5586, 0.4434],
         [0.5586, 0.4395],
         [0.6289, 0.3691],
         [0.5547, 0.4453],
         [0.5430, 0.4551],
         [0.6445, 0.3574],
         [0.6836, 0.3184],
         [0.5039, 0.4980],
         [0.6250, 0.3730],
         [0.5039, 0.4941],
         [0.5352, 0.4629],
         [0.5625, 0.4375],
         [0.6641, 0.3359],
         [0.5195, 0.4805],
         [0.7539, 0.2480],
         [0.6211, 0.3789],
         [0.5703, 0.4297],
         [0.5078, 0.4941],
         [0.5469, 0.4551],
         [0.7227, 0.2773],
         [0.6094, 0.3887],
         [0.6211, 0.3789],
         [0.6250, 0.3770],
         [0.5234, 0.4746],
         [0.5820, 0.4180],
         [0.5586, 0.4414],
         [0.6562, 0.3457],
         [0.5156, 0.4844],
         [0.6055, 0.3926],
         [0.5430, 0.4551],
         [0.6250, 0.3750],
         [0.5625, 0.4375],
         [0.6797, 0.3184],
         [0.6484, 0.3516],
         [0.6016, 0.3984],
         [0.5586, 0.4395],
         [0.5664, 0.4336],
         [0.5469, 0.4551],
         [0.7422, 0.2559],
         [0.6680, 0.3320],
         [0.7656, 0.2354],
         [0.6094, 0.3906],
         [0.5625, 0.4375],
         [0.7070, 0.2930],
         [0.6328, 0.3691],
         [0.6250, 0.3750],
         [0.5547, 0.4453],
         [0.5039, 0.4961],
         [0.5391, 0.4609],
         [0.5234, 0.4746],
         [0.6250, 0.3750]]], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<ToCopyBackward0>)
tensor([[[ 0.3516,  0.1484],
         [ 0.2598, -0.0796],
         [ 0.2520,  0.1055],
         [-0.2285, -0.1475],
         [ 0.1787, -0.4219],
         [ 0.2637, -0.1494],
         [-0.0182,  0.2949],
         [-0.3496, -0.0337],
         [-0.0845, -0.1416],
         [-0.2178,  0.0344],
         [ 0.1865, -0.3359],
         [-0.0464, -0.1367],
         [-0.2129,  0.0830],
         [-0.5430,  0.2178],
         [-0.2275,  0.1504],
         [-0.1387, -0.1885],
         [ 0.0052,  0.0889],
         [ 0.2012,  0.0339],
         [ 0.2832,  0.4004],
         [-0.2578,  0.2109],
         [-0.1328,  0.0947],
         [-0.2988, -0.2305],
         [ 0.2080,  0.2754],
         [-0.2637,  0.3750],
         [-0.2051,  0.1709],
         [ 0.0178,  0.2695],
         [ 0.0099, -0.0410],
         [-0.1973, -0.1426],
         [-0.1670,  0.1934],
         [ 0.1562,  0.4258],
         [-0.3164, -0.3086],
         [-0.1367,  0.1855],
         [ 0.0442,  0.2334],
         [ 0.3008,  0.3027],
         [-0.5273,  0.2412],
         [-0.1865,  0.2754],
         [-0.0991,  0.0476],
         [-0.1660, -0.2080],
         [ 0.0204,  0.3242],
         [ 0.0869,  0.2451],
         [-0.0630,  0.1504],
         [-0.1099,  0.2910],
         [ 0.1650, -0.0225],
         [-0.1631, -0.0171],
         [-0.1494,  0.2949],
         [ 0.4199, -0.0405],
         [ 0.2080,  0.2148],
         [ 0.0119, -0.3359],
         [-0.2471, -0.2354],
         [-0.1631, -0.0449],
         [-0.1738,  0.0405],
         [ 0.3320, -0.1611],
         [ 0.1768,  0.1206],
         [-0.4180,  0.2812],
         [-0.2178,  0.1758],
         [ 0.1768,  0.3320],
         [ 0.1240,  0.1050],
         [-0.1406,  0.0160],
         [-0.4219,  0.1865],
         [-0.1973,  0.1689],
         [ 0.2695,  0.4648],
         [-0.3047,  0.0645],
         [-0.0310,  0.0620],
         [-0.0228,  0.2354],
         [ 0.2168,  0.3398],
         [-0.1035,  0.3438],
         [-0.1206, -0.1660],
         [ 0.0442,  0.3242],
         [ 0.0957,  0.2227],
         [-0.1963,  0.2109],
         [ 0.2207,  0.0284],
         [ 0.3730, -0.1426],
         [-0.0825,  0.3438],
         [ 0.2539, -0.0649],
         [-0.3086, -0.1699],
         [ 0.1807, -0.0466],
         [ 0.0184,  0.1680],
         [-0.3301,  0.3633],
         [-0.2754,  0.2422],
         [-0.2178,  0.4727],
         [-0.3691, -0.1270],
         [ 0.0405,  0.2275],
         [-0.3379,  0.2676],
         [-0.1963,  0.2256],
         [ 0.0938, -0.2871],
         [ 0.1826,  0.3066],
         [ 0.0117,  0.0236],
         [-0.1064,  0.0332],
         [-0.1504, -0.2178],
         [-0.1445,  0.2559]]], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<DivBackward0>)
tensor([[[ 0.3516,  0.1484],
         [ 0.2598, -0.0796],
         [ 0.2520,  0.1055],
         [-0.2285, -0.1475],
         [ 0.1787, -0.4219],
         [ 0.2637, -0.1494],
         [-0.0182,  0.2949],
         [-0.3496, -0.0337],
         [-0.0845, -0.1416],
         [-0.2178,  0.0344],
         [ 0.1865, -0.3359],
         [-0.0464, -0.1367],
         [-0.2129,  0.0830],
         [-0.5430,  0.2178],
         [-0.2275,  0.1504],
         [-0.1387, -0.1885],
         [ 0.0052,  0.0889],
         [ 0.2012,  0.0339],
         [ 0.2832,  0.4004],
         [-0.2578,  0.2109],
         [-0.1328,  0.0947],
         [-0.2988, -0.2305],
         [ 0.2080,  0.2754],
         [-0.2637,  0.3750],
         [-0.2051,  0.1709],
         [ 0.0178,  0.2695],
         [ 0.0099, -0.0410],
         [-0.1973, -0.1426],
         [-0.1670,  0.1934],
         [ 0.1562,  0.4258],
         [-0.3164, -0.3086],
         [-0.1367,  0.1855],
         [ 0.0442,  0.2334],
         [ 0.3008,  0.3027],
         [-0.5273,  0.2412],
         [-0.1865,  0.2754],
         [-0.0991,  0.0476],
         [-0.1660, -0.2080],
         [ 0.0204,  0.3242],
         [ 0.0869,  0.2451],
         [-0.0630,  0.1504],
         [-0.1099,  0.2910],
         [ 0.1650, -0.0225],
         [-0.1631, -0.0171],
         [-0.1494,  0.2949],
         [ 0.4199, -0.0405],
         [ 0.2080,  0.2148],
         [ 0.0119, -0.3359],
         [-0.2471, -0.2354],
         [-0.1631, -0.0449],
         [-0.1738,  0.0405],
         [ 0.3320, -0.1611],
         [ 0.1768,  0.1206],
         [-0.4180,  0.2812],
         [-0.2178,  0.1758],
         [ 0.1768,  0.3320],
         [ 0.1240,  0.1050],
         [-0.1406,  0.0160],
         [-0.4219,  0.1865],
         [-0.1973,  0.1689],
         [ 0.2695,  0.4648],
         [-0.3047,  0.0645],
         [-0.0310,  0.0620],
         [-0.0228,  0.2354],
         [ 0.2168,  0.3398],
         [-0.1035,  0.3438],
         [-0.1206, -0.1660],
         [ 0.0442,  0.3242],
         [ 0.0957,  0.2227],
         [-0.1963,  0.2109],
         [ 0.2207,  0.0284],
         [ 0.3730, -0.1426],
         [-0.0825,  0.3438],
         [ 0.2539, -0.0649],
         [-0.3086, -0.1699],
         [ 0.1807, -0.0466],
         [ 0.0184,  0.1680],
         [-0.3301,  0.3633],
         [-0.2754,  0.2422],
         [-0.2178,  0.4727],
         [-0.3691, -0.1270],
         [ 0.0405,  0.2275],
         [-0.3379,  0.2676],
         [-0.1963,  0.2256],
         [ 0.0938, -0.2871],
         [ 0.1826,  0.3066],
         [ 0.0117,  0.0236],
         [-0.1064,  0.0332],
         [-0.1504, -0.2178],
         [-0.1445,  0.2559]]], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<DivBackward0>)
tensor([[[ 0.3516,  0.1484],
         [ 0.2598, -0.0796],
         [ 0.2520,  0.1055],
         [-0.1475, -0.2285],
         [ 0.1787, -0.4219],
         [ 0.2637, -0.1494],
         [ 0.2949, -0.0182],
         [-0.0337, -0.3496],
         [-0.0845, -0.1416],
         [ 0.0344, -0.2178],
         [ 0.1865, -0.3359],
         [-0.0464, -0.1367],
         [ 0.0830, -0.2129],
         [ 0.2178, -0.5430],
         [ 0.1504, -0.2275],
         [-0.1387, -0.1885],
         [ 0.0889,  0.0052],
         [ 0.2012,  0.0339],
         [ 0.4004,  0.2832],
         [ 0.2109, -0.2578],
         [ 0.0947, -0.1328],
         [-0.2305, -0.2988],
         [ 0.2754,  0.2080],
         [ 0.3750, -0.2637],
         [ 0.1709, -0.2051],
         [ 0.2695,  0.0178],
         [ 0.0099, -0.0410],
         [-0.1426, -0.1973],
         [ 0.1934, -0.1670],
         [ 0.4258,  0.1562],
         [-0.3086, -0.3164],
         [ 0.1855, -0.1367],
         [ 0.2334,  0.0442],
         [ 0.3027,  0.3008],
         [ 0.2412, -0.5273],
         [ 0.2754, -0.1865],
         [ 0.0476, -0.0991],
         [-0.1660, -0.2080],
         [ 0.3242,  0.0204],
         [ 0.2451,  0.0869],
         [ 0.1504, -0.0630],
         [ 0.2910, -0.1099],
         [ 0.1650, -0.0225],
         [-0.0171, -0.1631],
         [ 0.2949, -0.1494],
         [ 0.4199, -0.0405],
         [ 0.2148,  0.2080],
         [ 0.0119, -0.3359],
         [-0.2354, -0.2471],
         [-0.0449, -0.1631],
         [ 0.0405, -0.1738],
         [ 0.3320, -0.1611],
         [ 0.1768,  0.1206],
         [ 0.2812, -0.4180],
         [ 0.1758, -0.2178],
         [ 0.3320,  0.1768],
         [ 0.1240,  0.1050],
         [ 0.0160, -0.1406],
         [ 0.1865, -0.4219],
         [ 0.1689, -0.1973],
         [ 0.4648,  0.2695],
         [ 0.0645, -0.3047],
         [ 0.0620, -0.0310],
         [ 0.2354, -0.0228],
         [ 0.3398,  0.2168],
         [ 0.3438, -0.1035],
         [-0.1206, -0.1660],
         [ 0.3242,  0.0442],
         [ 0.2227,  0.0957],
         [ 0.2109, -0.1963],
         [ 0.2207,  0.0284],
         [ 0.3730, -0.1426],
         [ 0.3438, -0.0825],
         [ 0.2539, -0.0649],
         [-0.1699, -0.3086],
         [ 0.1807, -0.0466],
         [ 0.1680,  0.0184],
         [ 0.3633, -0.3301],
         [ 0.2422, -0.2754],
         [ 0.4727, -0.2178],
         [-0.1270, -0.3691],
         [ 0.2275,  0.0405],
         [ 0.2676, -0.3379],
         [ 0.2256, -0.1963],
         [ 0.0938, -0.2871],
         [ 0.3066,  0.1826],
         [ 0.0236,  0.0117],
         [ 0.0332, -0.1064],
         [-0.1504, -0.2178],
         [ 0.2559, -0.1445]]], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<TopkBackward0>)
