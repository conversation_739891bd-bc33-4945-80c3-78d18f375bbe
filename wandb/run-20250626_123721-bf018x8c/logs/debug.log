2025-06-26 12:37:21,690 INFO    MainThread:2109909 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-26 12:37:21,691 INFO    MainThread:2109909 [wandb_setup.py:_flush():81] Configure stats pid to 2109909
2025-06-26 12:37:21,691 INFO    MainThread:2109909 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/m/maryam.hashemzadeh/.config/wandb/settings
2025-06-26 12:37:21,691 INFO    MainThread:2109909 [wandb_setup.py:_flush():81] Loading settings from /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/settings
2025-06-26 12:37:21,691 INFO    MainThread:2109909 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-26 12:37:21,691 INFO    MainThread:2109909 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/run-20250626_123721-bf018x8c/logs/debug.log
2025-06-26 12:37:21,692 INFO    MainThread:2109909 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/run-20250626_123721-bf018x8c/logs/debug-internal.log
2025-06-26 12:37:21,692 INFO    MainThread:2109909 [wandb_init.py:init():831] calling init triggers
2025-06-26 12:37:21,692 INFO    MainThread:2109909 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-06-26 12:37:21,693 INFO    MainThread:2109909 [wandb_init.py:init():872] starting backend
2025-06-26 12:37:21,920 INFO    MainThread:2109909 [wandb_init.py:init():875] sending inform_init request
2025-06-26 12:37:21,934 INFO    MainThread:2109909 [wandb_init.py:init():883] backend started and connected
2025-06-26 12:37:21,936 INFO    MainThread:2109909 [wandb_init.py:init():956] updated telemetry
2025-06-26 12:37:21,972 INFO    MainThread:2109909 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-26 12:37:22,354 INFO    MainThread:2109909 [wandb_init.py:init():1032] starting run threads in backend
2025-06-26 12:37:22,568 INFO    MainThread:2109909 [wandb_run.py:_console_start():2453] atexit reg
2025-06-26 12:37:22,569 INFO    MainThread:2109909 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-26 12:37:22,569 INFO    MainThread:2109909 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-26 12:37:22,569 INFO    MainThread:2109909 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-26 12:37:22,575 INFO    MainThread:2109909 [wandb_init.py:init():1078] run started, returning control to user process
2025-06-26 12:37:22,576 INFO    MainThread:2109909 [wandb_run.py:_config_callback():1358] config_cb None None {'experiment_name': 'mistral-drug_abuse_pubmedqau-moe', 'seed': 42, 'wandb': {'enabled': True, 'project': 'mistral-moe-training', 'run_name': None, 'tags': ['mistral', 'moe', 'drug_abuse_pubmedqau'], 'notes': 'Training Mistral MoE model for drug_abuse_pubmedqau with router-only training'}, 'model': {'model_type': 'mistral', 'num_experts_per_tok': 2, 'base_model': 'mistralai/Mistral-7B-v0.1', 'experts': [{'expert_name': 'adapter_1', 'model_id': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse'}, {'expert_name': 'adapter_2', 'model_id': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau'}]}, 'training': {'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 1, 'learning_rate': 1e-05, 'save_total_limit': 1, 'num_train_epochs': 5, 'eval_steps': 5000, 'logging_strategy': 'steps', 'logging_steps': 25, 'gradient_accumulation_steps': 4, 'bf16': True, 'logging_first_step': True, 'evaluation_strategy': 'steps', 'save_strategy': 'steps', 'save_steps': 5000, 'load_best_model_at_end': True, 'metric_for_best_model': 'eval_loss', 'greater_is_better': False, 'max_seq_length': 512}, 'data': {'dataset_name': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json', 'test_size': 0.1, 'test_valid_split': 0.7}, 'paths': {'model_checkpoint': 'data/mistral_lora_moe_drug_abuse_pubmedqau_3', 'output_dir': 'data/output/mistral_lora_moe_drug_abuse_pubmedqau_3'}}
2025-06-26 12:42:36,740 INFO    MainThread:2109909 [wandb_run.py:_config_callback():1358] config_cb None None {'vocab_size': 32000, 'max_position_embeddings': 32768, 'hidden_size': 4096, 'intermediate_size': 14336, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'sliding_window': 4096, 'head_dim': 128, 'num_key_value_heads': 8, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'use_cache': True, 'rope_theta': 10000.0, 'attention_dropout': 0.0, 'return_dict': True, 'output_hidden_states': False, 'output_attentions': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['MistralForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': None, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'data/mistral_lora_moe_drug_abuse_pubmedqau_3', '_attn_implementation_autoset': True, 'transformers_version': '4.46.3', 'model_type': 'mistral', 'num_experts': 2, 'num_experts_per_tok': 2, 'router_layers': ['gate_proj', 'down_proj', 'up_proj'], 'adapter_configs': [{'task_type': 'CAUSAL_LM', 'peft_type': 'LORA', 'auto_mapping': None, 'base_model_name_or_path': 'mistralai/Mistral-7B-v0.1', 'revision': None, 'inference_mode': True, 'r': 32, 'target_modules': "{'gate_proj', 'down_proj', 'up_proj'}", 'exclude_modules': None, 'lora_alpha': 64, 'lora_dropout': 0.1, 'fan_in_fan_out': False, 'bias': 'none', 'use_rslora': False, 'modules_to_save': None, 'init_lora_weights': True, 'layers_to_transform': None, 'layers_pattern': None, 'rank_pattern': {}, 'alpha_pattern': {}, 'megatron_config': None, 'megatron_core': 'megatron.core', 'loftq_config': {}, 'eva_config': None, 'use_dora': False, 'layer_replication': None, 'lora_bias': False}, {'task_type': 'CAUSAL_LM', 'peft_type': 'LORA', 'auto_mapping': None, 'base_model_name_or_path': 'mistralai/Mistral-7B-v0.1', 'revision': None, 'inference_mode': True, 'r': 32, 'target_modules': "{'gate_proj', 'down_proj', 'up_proj'}", 'exclude_modules': None, 'lora_alpha': 64, 'lora_dropout': 0.1, 'fan_in_fan_out': False, 'bias': 'none', 'use_rslora': False, 'modules_to_save': None, 'init_lora_weights': True, 'layers_to_transform': None, 'layers_pattern': None, 'rank_pattern': {}, 'alpha_pattern': {}, 'megatron_config': None, 'megatron_core': 'megatron.core', 'loftq_config': {}, 'eva_config': None, 'use_dora': False, 'layer_replication': None, 'lora_bias': False}], 'router_layers_index': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'output_dir': 'data/output/mistral_lora_moe_drug_abuse_pubmedqau_3', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': True, 'do_predict': False, 'eval_strategy': 'steps', 'prediction_loss_only': False, 'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 1, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 4, 'eval_accumulation_steps': None, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 1e-05, 'weight_decay': 0.0, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 1.0, 'num_train_epochs': 5, 'max_steps': -1, 'lr_scheduler_type': 'linear', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.0, 'warmup_steps': 0, 'log_level': 'info', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': 'data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/runs/Jun26_12-42-28_cn-g009.server.mila.quebec', 'logging_strategy': 'steps', 'logging_first_step': True, 'logging_steps': 25, 'logging_nan_inf_filter': True, 'save_strategy': 'steps', 'save_steps': 5000, 'save_total_limit': 1, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': True, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': None, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': 5000, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': 'mistral-drug_abuse_pubmedqau-moe', 'disable_tqdm': False, 'remove_unused_columns': True, 'label_names': None, 'load_best_model_at_end': True, 'metric_for_best_model': 'eval_loss', 'greater_is_better': False, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': None, 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': True, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': False, 'hub_always_push': False, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': True, 'include_for_metrics': ['inputs', 'inputs'], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'evaluation_strategy': 'steps', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'dispatch_batches': None, 'split_batches': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'eval_use_gather_object': False, 'average_tokens_across_devices': False, 'dataset_text_field': 'text', 'packing': False, 'max_seq_length': 512, 'dataset_num_proc': None, 'dataset_batch_size': 1000, 'model_init_kwargs': None, 'dataset_kwargs': {}, 'eval_packing': None, 'num_of_sequences': 1024, 'chars_per_token': '<CHARS_PER_TOKEN>', 'use_liger': False}
2025-06-26 12:42:36,755 INFO    MainThread:2109909 [wandb_config.py:__setitem__():154] [no run ID] config set model/num_parameters = 7356420096 - <bound method Run._config_callback of <wandb.sdk.wandb_run.Run object at 0x7f25d329a740>>
2025-06-26 12:42:36,755 INFO    MainThread:2109909 [wandb_run.py:_config_callback():1358] config_cb model/num_parameters 7356420096 None
