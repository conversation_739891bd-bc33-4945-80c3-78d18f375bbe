# Security

## Reporting Security Issues

> Please refrain from raising issues that could potentially pose security risks.
> It's essential to report any security-related concerns privately to allow us adequate time to address them before they are made public.

Vulnerabilities can be reported by emailing Leeroo team:

- [Leeroo Team](https://www.leeroo.com/) <[<EMAIL>](mailto:<EMAIL>)>

Kindly provide the requested information listed below (to the best of your ability) to assist us in comprehensively understanding the nature and extent of the potential issue:

- Type of issue (e.g. buffer overflow, SQL injection, cross-site scripting, etc.)
- Full paths of source file(s) related to the manifestation of the issue
- The location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Environment (e.g. Linux / Windows / macOS)
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code (if possible)
- Impact of the issue, including how an attacker might exploit the issue

This information will expedite the process of reviewing your report.
